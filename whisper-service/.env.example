# VocaPilot Whisper Service Environment Configuration
# Copy this file to .env and update the values as needed

# Server Configuration
HOST=0.0.0.0
PORT=8000
WORKERS=1

# Whisper Model Configuration
MODEL_SIZE=base  # tiny, base, small, medium, large
MODEL_CACHE_DIR=./models
DEVICE=auto  # auto, cpu, cuda
COMPUTE_TYPE=default  # default, int8, float16

# Audio Processing
SAMPLE_RATE=16000
CHUNK_SIZE=1024
MAX_AUDIO_LENGTH=300  # 5 minutes in seconds
SUPPORTED_FORMATS=wav,mp3,m4a,flac,ogg

# Performance
ENABLE_GPU=true
BATCH_SIZE=1
NUM_WORKERS=1
MAX_CONCURRENT_REQUESTS=5

# Language Support
DEFAULT_LANGUAGE=auto
SUPPORTED_LANGUAGES=en,fr,es,ar,de
LANGUAGE_DETECTION=true

# Transcription Options
ENABLE_VAD=true  # Voice Activity Detection
ENABLE_TIMESTAMPS=true
ENABLE_WORD_TIMESTAMPS=false
TEMPERATURE=0.0
BEST_OF=5
BEAM_SIZE=5

# Real-time Processing
REALTIME_ENABLED=true
REALTIME_CHUNK_DURATION=1.0  # seconds
REALTIME_OVERLAP=0.1  # seconds

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=./logs/whisper.log

# Health Check
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30  # seconds

# Security
MAX_FILE_SIZE=52428800  # 50MB in bytes
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000
CORS_ENABLED=true

# Monitoring
METRICS_ENABLED=false
PROMETHEUS_PORT=9091

# Development
DEBUG_MODE=false
PROFILE_ENABLED=false
