import os
import tempfile
import time
from typing import Optional
import whisper
import torch
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from pydub import AudioSegment
import numpy as np
import librosa
import soundfile as sf
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="VocaPilot Whisper Service",
    description="Real-time speech-to-text transcription service using OpenAI Whisper",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
whisper_model = None
supported_languages = {
    'en': 'english',
    'fr': 'french', 
    'es': 'spanish',
    'ar': 'arabic',
    'de': 'german'
}

@app.on_event("startup")
async def startup_event():
    """Initialize Whisper model on startup"""
    global whisper_model
    try:
        logger.info("Loading Whisper model...")
        
        # Use GPU if available, otherwise CPU
        device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {device}")
        
        # Load base model for faster processing
        # In production, you might want to use 'small' or 'medium' for better accuracy
        whisper_model = whisper.load_model("base", device=device)
        
        logger.info("✅ Whisper model loaded successfully")
    except Exception as e:
        logger.error(f"❌ Failed to load Whisper model: {e}")
        raise e

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "whisper-transcription",
        "model_loaded": whisper_model is not None,
        "device": "cuda" if torch.cuda.is_available() else "cpu",
        "timestamp": time.time()
    }

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "VocaPilot Whisper Service",
        "version": "1.0.0",
        "endpoints": {
            "transcribe": "/transcribe",
            "health": "/health",
            "languages": "/languages"
        }
    }

@app.get("/languages")
async def get_supported_languages():
    """Get list of supported languages"""
    return {
        "supported_languages": [
            {"code": code, "name": name} 
            for code, name in supported_languages.items()
        ]
    }

@app.post("/transcribe")
async def transcribe_audio(
    audio: UploadFile = File(...),
    language: Optional[str] = Form("auto")
):
    """
    Transcribe audio file using Whisper
    
    Args:
        audio: Audio file (WAV, MP3, M4A, etc.)
        language: Language code (en, fr, es, ar, de) or 'auto' for detection
    
    Returns:
        JSON with transcription results
    """
    if whisper_model is None:
        raise HTTPException(status_code=503, detail="Whisper model not loaded")
    
    # Validate file type
    if not audio.content_type.startswith('audio/'):
        raise HTTPException(status_code=400, detail="File must be an audio file")
    
    start_time = time.time()
    
    try:
        # Read audio file
        audio_data = await audio.read()
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_file:
            temp_file.write(audio_data)
            temp_file_path = temp_file.name
        
        try:
            # Convert audio to proper format if needed
            audio_segment = AudioSegment.from_file(temp_file_path)
            
            # Convert to mono, 16kHz (Whisper's preferred format)
            audio_segment = audio_segment.set_channels(1).set_frame_rate(16000)
            
            # Save as WAV
            wav_path = temp_file_path.replace('.wav', '_processed.wav')
            audio_segment.export(wav_path, format="wav")
            
            # Prepare transcription options
            transcribe_options = {}
            
            if language != "auto" and language in supported_languages:
                transcribe_options["language"] = supported_languages[language]
            
            # Transcribe using Whisper
            logger.info(f"Transcribing audio file: {audio.filename}")
            result = whisper_model.transcribe(wav_path, **transcribe_options)
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Extract segments for detailed analysis
            segments = []
            for segment in result.get("segments", []):
                segments.append({
                    "start": segment["start"],
                    "end": segment["end"],
                    "text": segment["text"].strip(),
                    "confidence": segment.get("avg_logprob", 0.0)
                })
            
            # Calculate overall confidence
            if segments:
                avg_confidence = np.mean([seg["confidence"] for seg in segments])
                # Convert log probability to confidence score (0-1)
                confidence = min(1.0, max(0.0, (avg_confidence + 1.0) / 2.0))
            else:
                confidence = 0.0
            
            response = {
                "text": result["text"].strip(),
                "language": result.get("language", "unknown"),
                "confidence": round(confidence, 3),
                "duration": audio_segment.duration_seconds,
                "processing_time": round(processing_time, 3),
                "segments": segments,
                "word_count": len(result["text"].split()),
                "detected_language": result.get("language", "unknown")
            }
            
            logger.info(f"Transcription completed in {processing_time:.2f}s")
            return response
            
        finally:
            # Clean up temporary files
            try:
                os.unlink(temp_file_path)
                if 'wav_path' in locals():
                    os.unlink(wav_path)
            except:
                pass
                
    except Exception as e:
        logger.error(f"Error during transcription: {e}")
        raise HTTPException(status_code=500, detail=f"Transcription failed: {str(e)}")

@app.post("/transcribe-realtime")
async def transcribe_realtime(
    audio: UploadFile = File(...),
    language: Optional[str] = Form("auto")
):
    """
    Real-time transcription for shorter audio chunks
    Optimized for speed over accuracy
    """
    if whisper_model is None:
        raise HTTPException(status_code=503, detail="Whisper model not loaded")

    start_time = time.time()
    temp_file_path = None
    wav_path = None

    try:
        # Validate audio file
        if not audio.content_type or not audio.content_type.startswith('audio/'):
            logger.warning(f"Invalid content type: {audio.content_type}")
            # Accept anyway for real-time processing

        # Read audio data
        audio_data = await audio.read()
        logger.info(f"Received audio data: {len(audio_data)} bytes")

        if len(audio_data) == 0:
            raise HTTPException(status_code=400, detail="Empty audio file")

        # Create temporary file with proper extension
        temp_file_path = tempfile.mktemp(suffix=".webm")

        # Write audio data to temporary file
        with open(temp_file_path, 'wb') as temp_file:
            temp_file.write(audio_data)

        logger.info(f"Temporary file created: {temp_file_path}")

        # Verify file exists
        if not os.path.exists(temp_file_path):
            raise Exception(f"Temporary file not created: {temp_file_path}")

        try:
            # Direct Whisper processing without external audio tools
            logger.info("Processing audio directly with Whisper...")

            # Whisper can handle many audio formats directly
            # Let's try direct transcription first
            try:
                logger.info("Attempting direct Whisper transcription...")

                # Fast transcription with reduced options
                transcribe_options = {
                    "fp16": False,  # Disable FP16 for stability
                    "no_speech_threshold": 0.6,
                    "logprob_threshold": -1.0,
                    "verbose": False
                }

                if language != "auto" and language in supported_languages:
                    transcribe_options["language"] = supported_languages[language]

                # Try direct transcription with the original file
                result = whisper_model.transcribe(temp_file_path, **transcribe_options)
                logger.info("Direct Whisper transcription successful!")

            except Exception as direct_error:
                logger.warning(f"Direct transcription failed: {direct_error}")
                logger.info("Trying audio conversion approach...")

                # If direct fails, try with numpy array approach
                try:
                    # Use librosa for audio loading (without external dependencies)
                    import wave
                    import struct

                    # Try to read as raw audio data and convert to numpy array
                    audio_bytes = await audio.read()

                    # Create a simple WAV file manually
                    wav_path = tempfile.mktemp(suffix=".wav")

                    # For WebM audio, we'll create a basic WAV header
                    # This is a simplified approach for real-time processing
                    sample_rate = 16000
                    channels = 1
                    bits_per_sample = 16

                    # Create WAV file with proper header
                    with wave.open(wav_path, 'wb') as wav_file:
                        wav_file.setnchannels(channels)
                        wav_file.setsampwidth(bits_per_sample // 8)
                        wav_file.setframerate(sample_rate)

                        # Convert audio bytes to 16-bit PCM (simplified)
                        # This is a basic conversion - in production you'd want more robust handling
                        if len(audio_bytes) > 44:  # Skip potential headers
                            audio_data = audio_bytes[44:]  # Skip WAV header if present
                        else:
                            audio_data = audio_bytes

                        wav_file.writeframes(audio_data)

                    logger.info(f"Manual WAV file created: {wav_path}")

                    # Try transcription with the manually created WAV
                    result = whisper_model.transcribe(wav_path, **transcribe_options)
                    logger.info("Manual WAV transcription successful!")

                except Exception as manual_error:
                    logger.error(f"Manual conversion failed: {manual_error}")

                    # Final fallback - return empty result but don't crash
                    logger.warning("All transcription methods failed, returning empty result")
                    result = {
                        "text": "",
                        "language": "unknown"
                    }

            processing_time = time.time() - start_time
            logger.info(f"Transcription completed in {processing_time:.3f}s")

            # Quick confidence estimation
            text = result["text"].strip()
            confidence = 0.8 if text else 0.0

            response = {
                "text": text,
                "confidence": confidence,
                "processing_time": round(processing_time, 3),
                "is_realtime": True,
                "language": result.get("language", "unknown")
            }

            logger.info(f"Transcription result: '{text}'")
            return response

        except Exception as audio_error:
            logger.error(f"Audio processing error: {audio_error}")
            raise Exception(f"Audio processing failed: {str(audio_error)}")

    except Exception as e:
        logger.error(f"Error during real-time transcription: {e}")
        raise HTTPException(status_code=500, detail=f"Real-time transcription failed: {str(e)}")

    finally:
        # Clean up temporary files
        try:
            if temp_file_path and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                logger.info(f"Cleaned up temp file: {temp_file_path}")
        except Exception as cleanup_error:
            logger.warning(f"Failed to cleanup temp file: {cleanup_error}")

        try:
            if wav_path and os.path.exists(wav_path):
                os.unlink(wav_path)
                logger.info(f"Cleaned up WAV file: {wav_path}")
        except Exception as cleanup_error:
            logger.warning(f"Failed to cleanup WAV file: {cleanup_error}")

if __name__ == "__main__":
    # Run the server
    uvicorn.run(
        "app:app",
        host=os.getenv("HOST", "0.0.0.0"),
        port=int(os.getenv("PORT", 8000)),
        reload=False,  # Set to True for development
        log_level="info"
    )
