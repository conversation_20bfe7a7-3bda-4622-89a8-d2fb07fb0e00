import os
import tempfile
import time
from typing import Optional
import torch
from fastapi import FastAPI, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse
import uvicorn
from TTS.api import TTS
import soundfile as sf
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="VocaPilot TTS Service",
    description="Text-to-speech service using Coqui TTS",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
tts_model = None
device = "cuda" if torch.cuda.is_available() else "cpu"

# Supported voices and languages
SUPPORTED_VOICES = {
    'en': {
        'female': 'tts_models/en/ljspeech/tacotron2-DDC',
        'male': 'tts_models/en/ljspeech/glow-tts'
    },
    'fr': {
        'female': 'tts_models/fr/mai/tacotron2-DDC',
        'male': 'tts_models/fr/mai/glow-tts'
    },
    'es': {
        'female': 'tts_models/es/mai/tacotron2-DDC',
        'male': 'tts_models/es/mai/glow-tts'
    },
    'de': {
        'female': 'tts_models/de/thorsten/tacotron2-DDC',
        'male': 'tts_models/de/thorsten/glow-tts'
    }
}

@app.on_event("startup")
async def startup_event():
    """Initialize TTS model on startup"""
    global tts_model
    try:
        logger.info("Initializing TTS model...")
        # Start with English female voice as default
        model_name = SUPPORTED_VOICES['en']['female']
        tts_model = TTS(model_name=model_name, progress_bar=False).to(device)
        logger.info(f"TTS model initialized successfully on {device}")
    except Exception as e:
        logger.error(f"Failed to initialize TTS model: {e}")
        tts_model = None

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "model_loaded": tts_model is not None,
        "device": device,
        "timestamp": time.time()
    }

@app.get("/voices")
async def get_supported_voices():
    """Get list of supported voices"""
    return {
        "supported_voices": SUPPORTED_VOICES,
        "current_device": device
    }

@app.post("/synthesize")
async def synthesize_speech(
    text: str = Form(...),
    language: str = Form(default="en"),
    voice_type: str = Form(default="female"),
    speed: float = Form(default=1.0)
):
    """
    Synthesize speech from text
    
    Args:
        text: Text to synthesize
        language: Language code (en, fr, es, de)
        voice_type: Voice type (female, male)
        speed: Speech speed multiplier (0.5-2.0)
    """
    global tts_model
    
    if not tts_model:
        raise HTTPException(status_code=503, detail="TTS model not initialized")
    
    if not text or len(text.strip()) == 0:
        raise HTTPException(status_code=400, detail="Text cannot be empty")
    
    if language not in SUPPORTED_VOICES:
        raise HTTPException(status_code=400, detail=f"Language {language} not supported")
    
    if voice_type not in SUPPORTED_VOICES[language]:
        raise HTTPException(status_code=400, detail=f"Voice type {voice_type} not available for {language}")
    
    if not (0.5 <= speed <= 2.0):
        raise HTTPException(status_code=400, detail="Speed must be between 0.5 and 2.0")
    
    try:
        start_time = time.time()
        
        # Check if we need to switch models
        required_model = SUPPORTED_VOICES[language][voice_type]
        if tts_model.synthesizer.tts_model.model_name != required_model:
            logger.info(f"Switching to model: {required_model}")
            tts_model = TTS(model_name=required_model, progress_bar=False).to(device)
        
        # Create temporary file for output
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
            output_path = tmp_file.name
        
        # Synthesize speech
        tts_model.tts_to_file(
            text=text.strip(),
            file_path=output_path,
            speed=speed
        )
        
        processing_time = time.time() - start_time
        
        # Get audio file info
        audio_info = sf.info(output_path)
        
        logger.info(f"TTS synthesis completed in {processing_time:.2f}s for {len(text)} characters")
        
        # Return audio file
        return FileResponse(
            output_path,
            media_type="audio/wav",
            filename="speech.wav",
            headers={
                "X-Processing-Time": str(processing_time),
                "X-Text-Length": str(len(text)),
                "X-Audio-Duration": str(audio_info.duration),
                "X-Sample-Rate": str(audio_info.samplerate)
            }
        )
        
    except Exception as e:
        logger.error(f"TTS synthesis failed: {e}")
        raise HTTPException(status_code=500, detail=f"TTS synthesis failed: {str(e)}")

@app.post("/synthesize-stream")
async def synthesize_speech_stream(
    text: str = Form(...),
    language: str = Form(default="en"),
    voice_type: str = Form(default="female")
):
    """
    Synthesize speech with streaming response for real-time applications
    """
    # For now, return regular synthesis
    # In production, implement actual streaming
    return await synthesize_speech(text, language, voice_type)

@app.get("/test")
async def test_tts():
    """Test TTS functionality"""
    try:
        test_text = "Hello, this is a test of the VocaPilot text-to-speech service."
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
            output_path = tmp_file.name
        
        if tts_model:
            tts_model.tts_to_file(text=test_text, file_path=output_path)
            
            # Get file size
            file_size = os.path.getsize(output_path)
            
            # Clean up
            os.unlink(output_path)
            
            return {
                "status": "success",
                "message": "TTS test completed successfully",
                "test_text": test_text,
                "output_file_size": file_size,
                "model_device": device
            }
        else:
            return {
                "status": "error",
                "message": "TTS model not initialized"
            }
            
    except Exception as e:
        logger.error(f"TTS test failed: {e}")
        return {
            "status": "error",
            "message": f"TTS test failed: {str(e)}"
        }

if __name__ == "__main__":
    uvicorn.run(
        app,
        host=os.getenv("HOST", "0.0.0.0"),
        port=int(os.getenv("PORT", 8001)),
        log_level="info"
    )
