import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import { createServer } from 'http'
import { Server } from 'socket.io'
import rateLimit from 'express-rate-limit'
import dotenv from 'dotenv'

// Import routes
import audioRoutes from './routes/audio.js'
import aiRoutes from './routes/ai.js'
import resumeRoutes from './routes/resume.js'
import sessionRoutes from './routes/session.js'
import grammarRoutes from './routes/grammar.js'
import ttsRoutes from './routes/tts.js'
import pronunciationRoutes from './routes/pronunciation.js'
import analyticsRoutes from './routes/analytics.js'

// Import services
import { initializeDatabase } from './services/database.js'
import { setupSocketHandlers } from './services/socket.js'
import { initializeOllama } from './services/ai.js'

dotenv.config()

const app = express()
const server = createServer(app)
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
})

const PORT = process.env.PORT || 5001

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
})

// Middleware
app.use(helmet())
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:3000",
  credentials: true
}))
app.use(morgan('combined'))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))
app.use(limiter)

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  })
})

// API Routes
app.use('/api/audio', audioRoutes)
app.use('/api/ai', aiRoutes)
app.use('/api/resume', resumeRoutes)
app.use('/api/session', sessionRoutes)
app.use('/api/grammar', grammarRoutes)
app.use('/api/tts', ttsRoutes)
app.use('/api/pronunciation', pronunciationRoutes)
app.use('/api/analytics', analyticsRoutes)

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({ 
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  })
})

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' })
})

// Initialize database and start server
async function startServer() {
  try {
    await initializeDatabase()
    console.log('✅ Database initialized')

    // Initialize Ollama AI service
    try {
      await initializeOllama()
      console.log('✅ Ollama AI service initialized')
    } catch (error) {
      console.warn('⚠️ Ollama initialization failed, using fallback responses:', error.message)
    }

    // Setup Socket.IO handlers
    setupSocketHandlers(io)
    console.log('✅ Socket.IO handlers setup')
    
    server.listen(PORT, () => {
      console.log(`🚀 VocaPilot Backend running on port ${PORT}`)
      console.log(`📊 Health check: http://localhost:${PORT}/health`)
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`)
    })
  } catch (error) {
    console.error('❌ Failed to start server:', error)
    process.exit(1)
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully')
  server.close(() => {
    console.log('Process terminated')
  })
})

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully')
  server.close(() => {
    console.log('Process terminated')
  })
})

startServer()

export { app, io }
