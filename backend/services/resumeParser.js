import natural from 'natural'
import compromise from 'compromise'

// Initialize NLP tools
const tokenizer = new natural.WordTokenizer()
const stemmer = natural.PorterStemmer

// Common patterns for resume parsing
const patterns = {
  email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
  phone: /(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g,
  linkedin: /linkedin\.com\/in\/[\w-]+/gi,
  github: /github\.com\/[\w-]+/gi,
  website: /(?:https?:\/\/)?(?:www\.)?[\w-]+\.[\w.-]+/g,
  
  // Date patterns
  dates: /\b(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\.?\s+\d{4}\b|\b\d{1,2}\/\d{4}\b|\b\d{4}\b/gi,
  
  // Education patterns
  degrees: /\b(?:bachelor|master|phd|doctorate|associate|diploma|certificate|b\.?[as]\.?|m\.?[as]\.?|ph\.?d\.?)\b/gi,
  
  // Experience indicators
  companies: /\b(?:inc|corp|corporation|llc|ltd|limited|company|co\.)\b/gi,
  
  // Skills patterns
  programmingLanguages: /\b(?:javascript|python|java|c\+\+|c#|php|ruby|go|rust|swift|kotlin|typescript|scala|r|matlab)\b/gi,
  frameworks: /\b(?:react|angular|vue|node|express|django|flask|spring|laravel|rails|bootstrap|jquery)\b/gi,
  databases: /\b(?:mysql|postgresql|mongodb|redis|sqlite|oracle|sql server|cassandra|dynamodb)\b/gi,
  tools: /\b(?:git|docker|kubernetes|aws|azure|gcp|jenkins|travis|circleci|jira|confluence)\b/gi
}

// Parse resume content and extract structured data
export async function parseResumeContent(content) {
  try {
    const doc = compromise(content)
    
    const parsedData = {
      contact: extractContactInfo(content),
      summary: extractSummary(content, doc),
      experience: extractExperience(content, doc),
      education: extractEducation(content, doc),
      skills: extractSkills(content, doc),
      projects: extractProjects(content, doc),
      certifications: extractCertifications(content, doc),
      languages: extractLanguages(content, doc)
    }

    return parsedData
  } catch (error) {
    console.error('Error parsing resume:', error)
    throw error
  }
}

// Extract contact information
function extractContactInfo(content) {
  const contact = {}
  
  // Extract email
  const emails = content.match(patterns.email)
  if (emails && emails.length > 0) {
    contact.email = emails[0]
  }
  
  // Extract phone
  const phones = content.match(patterns.phone)
  if (phones && phones.length > 0) {
    contact.phone = phones[0]
  }
  
  // Extract LinkedIn
  const linkedin = content.match(patterns.linkedin)
  if (linkedin && linkedin.length > 0) {
    contact.linkedin = linkedin[0]
  }
  
  // Extract GitHub
  const github = content.match(patterns.github)
  if (github && github.length > 0) {
    contact.github = github[0]
  }
  
  // Extract name (first few words before contact info)
  const lines = content.split('\n').filter(line => line.trim())
  if (lines.length > 0) {
    const firstLine = lines[0].trim()
    // Simple heuristic: if first line doesn't contain email/phone, it's likely the name
    if (!patterns.email.test(firstLine) && !patterns.phone.test(firstLine)) {
      contact.name = firstLine
    }
  }
  
  return contact
}

// Extract professional summary
function extractSummary(content, doc) {
  const summaryKeywords = ['summary', 'profile', 'objective', 'about', 'overview']
  const lines = content.split('\n')
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].toLowerCase()
    
    if (summaryKeywords.some(keyword => line.includes(keyword))) {
      // Found summary section, extract next few lines
      let summary = ''
      for (let j = i + 1; j < Math.min(i + 5, lines.length); j++) {
        const nextLine = lines[j].trim()
        if (nextLine && !isHeaderLine(nextLine)) {
          summary += nextLine + ' '
        } else if (summary.length > 50) {
          break
        }
      }
      return summary.trim()
    }
  }
  
  // If no explicit summary section, try to extract from first paragraph
  const sentences = doc.sentences().out('array')
  if (sentences.length > 0) {
    const firstParagraph = sentences.slice(0, 3).join(' ')
    if (firstParagraph.length > 100 && firstParagraph.length < 500) {
      return firstParagraph
    }
  }
  
  return ''
}

// Extract work experience
function extractExperience(content, doc) {
  const experienceKeywords = ['experience', 'employment', 'work history', 'career', 'professional']
  const lines = content.split('\n')
  const experiences = []
  
  let inExperienceSection = false
  let currentExperience = null
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    const lowerLine = line.toLowerCase()
    
    // Check if we're entering experience section
    if (experienceKeywords.some(keyword => lowerLine.includes(keyword)) && isHeaderLine(line)) {
      inExperienceSection = true
      continue
    }
    
    // Check if we're leaving experience section
    if (inExperienceSection && isHeaderLine(line) && 
        !experienceKeywords.some(keyword => lowerLine.includes(keyword))) {
      if (currentExperience) {
        experiences.push(currentExperience)
        currentExperience = null
      }
      inExperienceSection = false
      continue
    }
    
    if (inExperienceSection && line) {
      // Try to identify job titles and companies
      const dates = line.match(patterns.dates)
      
      if (dates || isLikelyJobTitle(line)) {
        // Save previous experience
        if (currentExperience) {
          experiences.push(currentExperience)
        }
        
        // Start new experience
        currentExperience = {
          title: extractJobTitle(line),
          company: extractCompany(line),
          dates: dates ? dates.join(' - ') : '',
          description: []
        }
      } else if (currentExperience && line.startsWith('•') || line.startsWith('-') || line.startsWith('*')) {
        // Add bullet point to current experience
        currentExperience.description.push(line.replace(/^[•\-*]\s*/, ''))
      } else if (currentExperience && line.length > 20) {
        // Add description line
        currentExperience.description.push(line)
      }
    }
  }
  
  // Add last experience
  if (currentExperience) {
    experiences.push(currentExperience)
  }
  
  return experiences.map(exp => ({
    ...exp,
    description: exp.description.join(' ')
  }))
}

// Extract education
function extractEducation(content, doc) {
  const educationKeywords = ['education', 'academic', 'university', 'college', 'school']
  const lines = content.split('\n')
  const education = []
  
  let inEducationSection = false
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    const lowerLine = line.toLowerCase()
    
    if (educationKeywords.some(keyword => lowerLine.includes(keyword)) && isHeaderLine(line)) {
      inEducationSection = true
      continue
    }
    
    if (inEducationSection && isHeaderLine(line) && 
        !educationKeywords.some(keyword => lowerLine.includes(keyword))) {
      inEducationSection = false
      continue
    }
    
    if (inEducationSection && line) {
      const degrees = line.match(patterns.degrees)
      const dates = line.match(patterns.dates)
      
      if (degrees || dates || line.length > 10) {
        education.push({
          degree: degrees ? degrees[0] : '',
          institution: extractInstitution(line),
          year: dates ? dates[dates.length - 1] : '',
          details: line
        })
      }
    }
  }
  
  return education
}

// Extract skills
function extractSkills(content, doc) {
  const skillsKeywords = ['skills', 'technologies', 'technical', 'competencies', 'expertise']
  const lines = content.split('\n')
  const skills = new Set()
  
  let inSkillsSection = false
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    const lowerLine = line.toLowerCase()
    
    if (skillsKeywords.some(keyword => lowerLine.includes(keyword)) && isHeaderLine(line)) {
      inSkillsSection = true
      continue
    }
    
    if (inSkillsSection && isHeaderLine(line) && 
        !skillsKeywords.some(keyword => lowerLine.includes(keyword))) {
      inSkillsSection = false
      continue
    }
    
    if (inSkillsSection && line) {
      // Extract skills from line
      const lineSkills = extractSkillsFromLine(line)
      lineSkills.forEach(skill => skills.add(skill))
    }
  }
  
  // Also extract skills from entire content
  const contentSkills = extractSkillsFromLine(content)
  contentSkills.forEach(skill => skills.add(skill))
  
  return Array.from(skills).filter(skill => skill.length > 1)
}

// Extract projects
function extractProjects(content, doc) {
  const projectKeywords = ['projects', 'portfolio', 'work samples']
  const lines = content.split('\n')
  const projects = []
  
  let inProjectSection = false
  let currentProject = null
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    const lowerLine = line.toLowerCase()
    
    if (projectKeywords.some(keyword => lowerLine.includes(keyword)) && isHeaderLine(line)) {
      inProjectSection = true
      continue
    }
    
    if (inProjectSection && isHeaderLine(line) && 
        !projectKeywords.some(keyword => lowerLine.includes(keyword))) {
      if (currentProject) {
        projects.push(currentProject)
      }
      inProjectSection = false
      continue
    }
    
    if (inProjectSection && line) {
      if (line.length > 10 && !line.startsWith('•') && !line.startsWith('-')) {
        if (currentProject) {
          projects.push(currentProject)
        }
        currentProject = {
          name: line,
          description: []
        }
      } else if (currentProject && (line.startsWith('•') || line.startsWith('-'))) {
        currentProject.description.push(line.replace(/^[•\-]\s*/, ''))
      }
    }
  }
  
  if (currentProject) {
    projects.push(currentProject)
  }
  
  return projects.map(proj => ({
    ...proj,
    description: proj.description.join(' ')
  }))
}

// Extract certifications
function extractCertifications(content, doc) {
  const certKeywords = ['certification', 'certificate', 'licensed', 'certified']
  const certifications = []
  
  const lines = content.split('\n')
  for (const line of lines) {
    if (certKeywords.some(keyword => line.toLowerCase().includes(keyword))) {
      certifications.push(line.trim())
    }
  }
  
  return certifications
}

// Extract languages
function extractLanguages(content, doc) {
  const languageKeywords = ['languages', 'linguistic']
  const commonLanguages = ['english', 'spanish', 'french', 'german', 'chinese', 'japanese', 'arabic', 'portuguese', 'russian', 'italian']
  const languages = []
  
  const lines = content.split('\n')
  for (const line of lines) {
    const lowerLine = line.toLowerCase()
    if (languageKeywords.some(keyword => lowerLine.includes(keyword))) {
      commonLanguages.forEach(lang => {
        if (lowerLine.includes(lang)) {
          languages.push(lang)
        }
      })
    }
  }
  
  return [...new Set(languages)]
}

// Helper functions
function isHeaderLine(line) {
  return line.length < 50 && (
    line.toUpperCase() === line ||
    line.split(' ').length <= 4 ||
    /^[A-Z\s]+$/.test(line)
  )
}

function isLikelyJobTitle(line) {
  const jobTitleWords = ['engineer', 'developer', 'manager', 'analyst', 'specialist', 'coordinator', 'director', 'lead', 'senior', 'junior']
  return jobTitleWords.some(word => line.toLowerCase().includes(word))
}

function extractJobTitle(line) {
  // Simple extraction - take first part before company indicators
  const parts = line.split(/\s+at\s+|\s+@\s+|\s+-\s+/)
  return parts[0].trim()
}

function extractCompany(line) {
  // Extract company name after "at" or "@"
  const match = line.match(/(?:at|@)\s+([^,\n]+)/i)
  return match ? match[1].trim() : ''
}

function extractInstitution(line) {
  // Remove degree and date information to get institution
  let institution = line
  institution = institution.replace(patterns.degrees, '').replace(patterns.dates, '')
  return institution.trim()
}

function extractSkillsFromLine(line) {
  const skills = []
  
  // Extract programming languages
  const progLangs = line.match(patterns.programmingLanguages)
  if (progLangs) skills.push(...progLangs)
  
  // Extract frameworks
  const frameworks = line.match(patterns.frameworks)
  if (frameworks) skills.push(...frameworks)
  
  // Extract databases
  const databases = line.match(patterns.databases)
  if (databases) skills.push(...databases)
  
  // Extract tools
  const tools = line.match(patterns.tools)
  if (tools) skills.push(...tools)
  
  // Extract comma-separated skills
  if (line.includes(',')) {
    const commaSeparated = line.split(',').map(s => s.trim()).filter(s => s.length > 1 && s.length < 30)
    skills.push(...commaSeparated)
  }
  
  return skills.map(skill => skill.toLowerCase().replace(/[^\w\s+#]/g, '').trim())
}

export { patterns }
