import axios from 'axios'
import FormData from 'form-data'

/**
 * Text-to-Speech Service
 * Handles communication with the TTS microservice
 */

class TTSService {
  constructor() {
    this.ttsServiceUrl = process.env.TTS_SERVICE_URL || 'http://localhost:8001'
    this.defaultLanguage = 'en'
    this.defaultVoiceType = 'female'
    this.defaultSpeed = 1.0
  }

  /**
   * Check if TTS service is available
   */
  async isAvailable() {
    try {
      const response = await axios.get(`${this.ttsServiceUrl}/health`, {
        timeout: 3000
      })
      return response.data.status === 'healthy' && response.data.model_loaded
    } catch (error) {
      console.error('TTS service health check failed:', error.message)
      return false
    }
  }

  /**
   * Get supported voices
   */
  async getSupportedVoices() {
    try {
      const response = await axios.get(`${this.ttsServiceUrl}/voices`, {
        timeout: 5000
      })
      return {
        success: true,
        voices: response.data.supported_voices,
        device: response.data.current_device
      }
    } catch (error) {
      console.error('Failed to get supported voices:', error.message)
      return {
        success: false,
        error: error.message,
        voices: {}
      }
    }
  }

  /**
   * Synthesize speech from text
   */
  async synthesizeText(text, options = {}) {
    const {
      language = this.defaultLanguage,
      voiceType = this.defaultVoiceType,
      speed = this.defaultSpeed
    } = options

    if (!text || text.trim().length === 0) {
      throw new Error('Text cannot be empty')
    }

    if (text.length > 1000) {
      throw new Error('Text too long (max 1000 characters)')
    }

    try {
      const formData = new FormData()
      formData.append('text', text.trim())
      formData.append('language', language)
      formData.append('voice_type', voiceType)
      formData.append('speed', speed.toString())

      const response = await axios.post(
        `${this.ttsServiceUrl}/synthesize`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
          },
          responseType: 'arraybuffer',
          timeout: 30000 // 30 seconds for synthesis
        }
      )

      return {
        success: true,
        audioBuffer: response.data,
        processingTime: response.headers['x-processing-time'],
        textLength: response.headers['x-text-length'],
        audioDuration: response.headers['x-audio-duration'],
        sampleRate: response.headers['x-sample-rate'],
        mimeType: 'audio/wav'
      }

    } catch (error) {
      console.error('TTS synthesis failed:', error.message)
      
      if (error.response) {
        throw new Error(`TTS service error: ${error.response.status} - ${error.response.statusText}`)
      } else if (error.code === 'ECONNREFUSED') {
        throw new Error('TTS service is not available')
      } else {
        throw new Error(`TTS synthesis failed: ${error.message}`)
      }
    }
  }

  /**
   * Synthesize speech with streaming (for real-time applications)
   */
  async synthesizeStream(text, options = {}) {
    // For now, use regular synthesis
    // In production, implement actual streaming
    return this.synthesizeText(text, options)
  }

  /**
   * Test TTS functionality
   */
  async testTTS() {
    try {
      const response = await axios.get(`${this.ttsServiceUrl}/test`, {
        timeout: 10000
      })
      return {
        success: true,
        result: response.data
      }
    } catch (error) {
      console.error('TTS test failed:', error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * Generate speech for AI responses
   */
  async generateAIResponseSpeech(responseText, userLanguage = 'en', voicePreference = 'female') {
    try {
      // Clean and prepare text for TTS
      const cleanText = this.cleanTextForTTS(responseText)
      
      if (cleanText.length === 0) {
        throw new Error('No valid text to synthesize')
      }

      const result = await this.synthesizeText(cleanText, {
        language: userLanguage,
        voiceType: voicePreference,
        speed: 1.0
      })

      return {
        success: true,
        audioBuffer: result.audioBuffer,
        originalText: responseText,
        cleanedText: cleanText,
        metadata: {
          processingTime: result.processingTime,
          audioDuration: result.audioDuration,
          language: userLanguage,
          voiceType: voicePreference
        }
      }

    } catch (error) {
      console.error('AI response speech generation failed:', error.message)
      return {
        success: false,
        error: error.message,
        originalText: responseText
      }
    }
  }

  /**
   * Clean text for TTS processing
   */
  cleanTextForTTS(text) {
    if (!text) return ''
    
    return text
      // Remove markdown formatting
      .replace(/\*\*(.*?)\*\*/g, '$1')
      .replace(/\*(.*?)\*/g, '$1')
      .replace(/`(.*?)`/g, '$1')
      // Remove URLs
      .replace(/https?:\/\/[^\s]+/g, '')
      // Remove excessive punctuation
      .replace(/[.]{2,}/g, '.')
      .replace(/[!]{2,}/g, '!')
      .replace(/[?]{2,}/g, '?')
      // Remove special characters that might cause issues
      .replace(/[^\w\s.,!?;:'"()-]/g, '')
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      .trim()
  }

  /**
   * Estimate speech duration
   */
  estimateSpeechDuration(text, wordsPerMinute = 150) {
    const words = text.trim().split(/\s+/).length
    return (words / wordsPerMinute) * 60 // Return duration in seconds
  }

  /**
   * Split long text into chunks for TTS
   */
  splitTextForTTS(text, maxLength = 500) {
    if (text.length <= maxLength) {
      return [text]
    }

    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const chunks = []
    let currentChunk = ''

    for (const sentence of sentences) {
      const trimmedSentence = sentence.trim()
      if (currentChunk.length + trimmedSentence.length + 1 <= maxLength) {
        currentChunk += (currentChunk ? '. ' : '') + trimmedSentence
      } else {
        if (currentChunk) {
          chunks.push(currentChunk + '.')
        }
        currentChunk = trimmedSentence
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk + '.')
    }

    return chunks
  }

  /**
   * Generate speech for multiple text chunks
   */
  async synthesizeMultipleChunks(textChunks, options = {}) {
    const results = []
    
    for (let i = 0; i < textChunks.length; i++) {
      try {
        const result = await this.synthesizeText(textChunks[i], options)
        results.push({
          success: true,
          chunkIndex: i,
          text: textChunks[i],
          audioBuffer: result.audioBuffer,
          metadata: result
        })
      } catch (error) {
        results.push({
          success: false,
          chunkIndex: i,
          text: textChunks[i],
          error: error.message
        })
      }
    }

    return results
  }
}

export default new TTSService()
