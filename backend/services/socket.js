import { transcriptDb, suggestionDb } from './database.js'
import { generateAISuggestion } from './ai.js'
import { analyzeAudioTone } from './audio.js'

// Store active sessions
const activeSessions = new Map()

export function setupSocketHandlers(io) {
  io.on('connection', (socket) => {
    console.log(`Client connected: ${socket.id}`)

    // Join session room
    socket.on('join-session', (sessionData) => {
      const { sessionId, userName } = sessionData
      socket.join(`session-${sessionId}`)
      
      activeSessions.set(socket.id, {
        sessionId,
        userName,
        joinedAt: new Date()
      })
      
      console.log(`User ${userName} joined session ${sessionId}`)
      socket.emit('session-joined', { sessionId, status: 'connected' })
    })

    // Handle real-time audio transcription
    socket.on('audio-transcript', async (data) => {
      try {
        const { sessionId, text, confidence, language, timestamp } = data
        
        // Save transcript to database
        const transcriptId = await transcriptDb.create({
          session_id: sessionId,
          text,
          confidence,
          language
        })

        // Broadcast transcript to all clients in the session
        io.to(`session-${sessionId}`).emit('transcript-update', {
          id: transcriptId,
          text,
          confidence,
          timestamp: timestamp || new Date().toISOString()
        })

        // Generate AI suggestion if transcript is substantial
        if (text.length > 20) {
          const suggestion = await generateAISuggestion(text, sessionId)
          
          if (suggestion) {
            // Save suggestion to database
            const suggestionId = await suggestionDb.create({
              session_id: sessionId,
              transcript_id: transcriptId,
              suggestion: suggestion.text,
              context: suggestion.context,
              confidence: suggestion.confidence
            })

            // Broadcast suggestion to clients
            io.to(`session-${sessionId}`).emit('ai-suggestion', {
              id: suggestionId,
              suggestion: suggestion.text,
              context: suggestion.context,
              confidence: suggestion.confidence,
              timestamp: new Date().toISOString()
            })
          }
        }
      } catch (error) {
        console.error('Error processing audio transcript:', error)
        socket.emit('error', { message: 'Failed to process transcript' })
      }
    })

    // Handle audio data for tone analysis
    socket.on('audio-data', async (data) => {
      try {
        const { sessionId, audioBuffer, sampleRate } = data
        
        // Analyze tone from audio data
        const toneAnalysis = await analyzeAudioTone(audioBuffer, sampleRate)
        
        if (toneAnalysis) {
          // Broadcast tone analysis to clients
          io.to(`session-${sessionId}`).emit('tone-analysis', {
            ...toneAnalysis,
            timestamp: new Date().toISOString()
          })
        }
      } catch (error) {
        console.error('Error analyzing audio tone:', error)
        socket.emit('error', { message: 'Failed to analyze audio tone' })
      }
    })

    // Handle session control
    socket.on('start-recording', (data) => {
      const { sessionId } = data
      io.to(`session-${sessionId}`).emit('recording-started', {
        timestamp: new Date().toISOString()
      })
    })

    socket.on('stop-recording', (data) => {
      const { sessionId } = data
      io.to(`session-${sessionId}`).emit('recording-stopped', {
        timestamp: new Date().toISOString()
      })
    })

    // Handle feedback requests
    socket.on('request-feedback', async (data) => {
      try {
        const { sessionId, type } = data // type: 'pace', 'clarity', 'confidence'
        
        // Get recent transcripts for analysis
        const recentTranscripts = await transcriptDb.getBySessionId(sessionId)
        const recentText = recentTranscripts
          .slice(-5) // Last 5 transcripts
          .map(t => t.text)
          .join(' ')

        if (recentText.length > 50) {
          const feedback = await generateFeedback(recentText, type)
          
          socket.emit('feedback-response', {
            type,
            feedback,
            timestamp: new Date().toISOString()
          })
        }
      } catch (error) {
        console.error('Error generating feedback:', error)
        socket.emit('error', { message: 'Failed to generate feedback' })
      }
    })

    // Handle client disconnect
    socket.on('disconnect', () => {
      const sessionData = activeSessions.get(socket.id)
      if (sessionData) {
        console.log(`User ${sessionData.userName} disconnected from session ${sessionData.sessionId}`)
        activeSessions.delete(socket.id)
      }
      console.log(`Client disconnected: ${socket.id}`)
    })

    // Handle errors
    socket.on('error', (error) => {
      console.error('Socket error:', error)
    })
  })
}

// Generate contextual feedback based on speech patterns
async function generateFeedback(text, type) {
  const words = text.split(' ')
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0)
  
  switch (type) {
    case 'pace':
      const wordsPerMinute = words.length * 2 // Rough estimate
      if (wordsPerMinute > 180) {
        return {
          message: "You're speaking quite fast. Try to slow down a bit for better clarity.",
          severity: 'warning',
          suggestion: 'Take brief pauses between sentences and emphasize key points.'
        }
      } else if (wordsPerMinute < 120) {
        return {
          message: "Your pace is good, but you could speak a bit faster to maintain engagement.",
          severity: 'info',
          suggestion: 'Try to maintain a steady rhythm while speaking.'
        }
      } else {
        return {
          message: "Great pace! You're speaking at an ideal speed.",
          severity: 'success',
          suggestion: 'Keep maintaining this natural rhythm.'
        }
      }

    case 'clarity':
      const avgWordsPerSentence = words.length / sentences.length
      const fillerWords = (text.match(/\b(um|uh|like|you know|actually)\b/gi) || []).length
      const fillerRatio = fillerWords / words.length

      if (fillerRatio > 0.05) {
        return {
          message: "Try to reduce filler words like 'um', 'uh', and 'like'.",
          severity: 'warning',
          suggestion: 'Pause briefly instead of using filler words. Practice speaking more deliberately.'
        }
      } else if (avgWordsPerSentence > 25) {
        return {
          message: "Your sentences are quite long. Try breaking them into shorter, clearer statements.",
          severity: 'info',
          suggestion: 'Use shorter sentences to make your points more impactful.'
        }
      } else {
        return {
          message: "Your speech clarity is excellent!",
          severity: 'success',
          suggestion: 'Continue speaking clearly and concisely.'
        }
      }

    case 'confidence':
      const uncertainWords = (text.match(/\b(maybe|perhaps|i think|i guess|probably)\b/gi) || []).length
      const uncertainRatio = uncertainWords / words.length
      const questionMarks = (text.match(/\?/g) || []).length

      if (uncertainRatio > 0.08 || questionMarks > sentences.length * 0.3) {
        return {
          message: "Try to sound more confident in your responses.",
          severity: 'warning',
          suggestion: 'Use definitive statements and avoid too many uncertain phrases.'
        }
      } else {
        return {
          message: "You sound confident and assured!",
          severity: 'success',
          suggestion: 'Keep up the confident tone and clear assertions.'
        }
      }

    default:
      return {
        message: "Keep up the good work!",
        severity: 'info',
        suggestion: 'Continue practicing and stay focused.'
      }
  }
}

export { activeSessions }
