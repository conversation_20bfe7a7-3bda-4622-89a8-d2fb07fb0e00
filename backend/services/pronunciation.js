import natural from 'natural'

/**
 * Pronunciation Analysis Service
 * Analyzes speech patterns, fluency, and pronunciation quality
 */

class PronunciationService {
  constructor() {
    this.phoneticAlgorithm = natural.Metaphone
    this.stemmer = natural.PorterStemmer
    
    // Common pronunciation patterns and rules
    this.pronunciationRules = {
      en: {
        vowelSounds: ['a', 'e', 'i', 'o', 'u'],
        consonantClusters: ['th', 'ch', 'sh', 'ph', 'gh', 'ck'],
        difficultSounds: ['th', 'r', 'l', 'v', 'w'],
        stressPatterns: {
          'tion': 'SHUN',
          'sion': 'ZHUN',
          'ough': 'UFF'
        }
      },
      fr: {
        vowelSounds: ['a', 'e', 'i', 'o', 'u', 'é', 'è', 'ê'],
        nasalSounds: ['an', 'en', 'in', 'on', 'un'],
        difficultSounds: ['r', 'u', 'eu', 'ou']
      },
      es: {
        vowelSounds: ['a', 'e', 'i', 'o', 'u'],
        rolledR: ['rr', 'r'],
        difficultSounds: ['ñ', 'll', 'j']
      }
    }
  }

  /**
   * Analyze pronunciation quality from transcript and reference text
   */
  async analyzePronunciation(spokenText, referenceText, language = 'en', audioMetrics = {}) {
    try {
      const analysis = {
        accuracy: this.calculateAccuracy(spokenText, referenceText),
        fluency: this.analyzeFluency(spokenText, audioMetrics),
        intonation: this.analyzeIntonation(spokenText, audioMetrics),
        wordStress: this.analyzeWordStress(spokenText, referenceText, language),
        phoneticAccuracy: this.analyzePhoneticAccuracy(spokenText, referenceText),
        overallScore: 0,
        feedback: [],
        improvements: []
      }

      // Calculate overall score
      analysis.overallScore = this.calculateOverallScore(analysis)
      
      // Generate feedback
      analysis.feedback = this.generatePronunciationFeedback(analysis, language)
      
      // Generate improvement suggestions
      analysis.improvements = this.generateImprovementSuggestions(analysis, language)

      return {
        success: true,
        analysis,
        spokenText,
        referenceText,
        language
      }

    } catch (error) {
      console.error('Pronunciation analysis failed:', error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * Calculate accuracy by comparing spoken text to reference
   */
  calculateAccuracy(spoken, reference) {
    const spokenWords = this.normalizeText(spoken).split(/\s+/)
    const referenceWords = this.normalizeText(reference).split(/\s+/)
    
    if (referenceWords.length === 0) return 0
    
    let correctWords = 0
    const minLength = Math.min(spokenWords.length, referenceWords.length)
    
    for (let i = 0; i < minLength; i++) {
      if (this.wordsMatch(spokenWords[i], referenceWords[i])) {
        correctWords++
      }
    }
    
    // Penalize for length differences
    const lengthPenalty = Math.abs(spokenWords.length - referenceWords.length) * 0.1
    const accuracy = (correctWords / referenceWords.length) * 100
    
    return Math.max(0, accuracy - lengthPenalty)
  }

  /**
   * Analyze fluency based on speech patterns
   */
  analyzeFluency(spokenText, audioMetrics = {}) {
    const words = spokenText.split(/\s+/)
    const sentences = spokenText.split(/[.!?]+/).filter(s => s.trim().length > 0)
    
    const metrics = {
      wordsPerMinute: audioMetrics.wordsPerMinute || this.estimateWPM(words.length, audioMetrics.duration),
      pauseFrequency: audioMetrics.pauseCount || 0,
      hesitations: this.countHesitations(spokenText),
      repetitions: this.countRepetitions(spokenText),
      fillerWords: this.countFillerWords(spokenText),
      sentenceCompleteness: this.analyzeSentenceCompleteness(sentences)
    }
    
    // Calculate fluency score (0-100)
    let score = 100
    
    // Optimal WPM is 140-160
    if (metrics.wordsPerMinute < 100 || metrics.wordsPerMinute > 200) {
      score -= 20
    } else if (metrics.wordsPerMinute < 120 || metrics.wordsPerMinute > 180) {
      score -= 10
    }
    
    // Penalize for hesitations and fillers
    score -= metrics.hesitations * 5
    score -= metrics.fillerWords * 3
    score -= metrics.repetitions * 4
    
    // Reward sentence completeness
    score += metrics.sentenceCompleteness * 10
    
    return {
      score: Math.max(0, Math.min(100, score)),
      metrics,
      rating: this.getFluencyRating(score)
    }
  }

  /**
   * Analyze intonation patterns
   */
  analyzeIntonation(spokenText, audioMetrics = {}) {
    const sentences = spokenText.split(/[.!?]+/).filter(s => s.trim().length > 0)
    
    const patterns = {
      questions: this.countQuestions(spokenText),
      statements: sentences.length - this.countQuestions(spokenText),
      emphasis: this.detectEmphasis(spokenText),
      monotone: audioMetrics.pitchVariation ? audioMetrics.pitchVariation < 0.3 : false
    }
    
    let score = 70 // Base score
    
    // Reward varied intonation
    if (patterns.questions > 0 && patterns.statements > 0) score += 15
    if (patterns.emphasis.length > 0) score += 10
    if (patterns.monotone) score -= 25
    
    return {
      score: Math.max(0, Math.min(100, score)),
      patterns,
      rating: this.getIntonationRating(score)
    }
  }

  /**
   * Analyze word stress patterns
   */
  analyzeWordStress(spoken, reference, language) {
    const spokenWords = this.normalizeText(spoken).split(/\s+/)
    const referenceWords = this.normalizeText(reference).split(/\s+/)
    
    const stressAnalysis = {
      correctStress: 0,
      totalWords: referenceWords.length,
      stressErrors: []
    }
    
    const rules = this.pronunciationRules[language] || this.pronunciationRules.en
    
    for (let i = 0; i < Math.min(spokenWords.length, referenceWords.length); i++) {
      const spokenWord = spokenWords[i]
      const referenceWord = referenceWords[i]
      
      if (this.hasCorrectStress(spokenWord, referenceWord, rules)) {
        stressAnalysis.correctStress++
      } else {
        stressAnalysis.stressErrors.push({
          word: referenceWord,
          position: i,
          expected: this.getExpectedStress(referenceWord, rules),
          detected: this.getDetectedStress(spokenWord, rules)
        })
      }
    }
    
    const accuracy = stressAnalysis.totalWords > 0 
      ? (stressAnalysis.correctStress / stressAnalysis.totalWords) * 100 
      : 0
    
    return {
      accuracy,
      ...stressAnalysis,
      rating: this.getStressRating(accuracy)
    }
  }

  /**
   * Analyze phonetic accuracy
   */
  analyzePhoneticAccuracy(spoken, reference) {
    const spokenPhonetic = this.phoneticAlgorithm.process(this.normalizeText(spoken))
    const referencePhonetic = this.phoneticAlgorithm.process(this.normalizeText(reference))
    
    const similarity = this.calculatePhoneticSimilarity(spokenPhonetic, referencePhonetic)
    
    return {
      similarity,
      spokenPhonetic,
      referencePhonetic,
      rating: this.getPhoneticRating(similarity)
    }
  }

  /**
   * Calculate overall pronunciation score
   */
  calculateOverallScore(analysis) {
    const weights = {
      accuracy: 0.3,
      fluency: 0.25,
      intonation: 0.2,
      wordStress: 0.15,
      phoneticAccuracy: 0.1
    }
    
    return Math.round(
      analysis.accuracy * weights.accuracy +
      analysis.fluency.score * weights.fluency +
      analysis.intonation.score * weights.intonation +
      analysis.wordStress.accuracy * weights.wordStress +
      analysis.phoneticAccuracy.similarity * weights.phoneticAccuracy
    )
  }

  /**
   * Generate pronunciation feedback
   */
  generatePronunciationFeedback(analysis, language) {
    const feedback = []
    
    // Accuracy feedback
    if (analysis.accuracy < 70) {
      feedback.push({
        type: 'error',
        category: 'accuracy',
        message: 'Focus on pronouncing each word clearly. Practice reading the text slowly first.',
        priority: 'high'
      })
    } else if (analysis.accuracy > 90) {
      feedback.push({
        type: 'success',
        category: 'accuracy',
        message: 'Excellent word accuracy! Your pronunciation is very clear.',
        priority: 'positive'
      })
    }
    
    // Fluency feedback
    if (analysis.fluency.score < 60) {
      feedback.push({
        type: 'warning',
        category: 'fluency',
        message: 'Try to speak more smoothly. Reduce hesitations and filler words.',
        priority: 'high'
      })
    }
    
    // Intonation feedback
    if (analysis.intonation.patterns.monotone) {
      feedback.push({
        type: 'info',
        category: 'intonation',
        message: 'Add more variation to your voice. Practice emphasizing important words.',
        priority: 'medium'
      })
    }
    
    // Word stress feedback
    if (analysis.wordStress.accuracy < 70) {
      feedback.push({
        type: 'warning',
        category: 'stress',
        message: 'Work on word stress patterns. Listen to native speakers and practice.',
        priority: 'medium'
      })
    }
    
    return feedback
  }

  /**
   * Generate improvement suggestions
   */
  generateImprovementSuggestions(analysis, language) {
    const suggestions = []
    
    if (analysis.accuracy < 80) {
      suggestions.push({
        title: 'Practice Individual Words',
        description: 'Break down difficult words and practice them separately',
        exercises: ['Word repetition', 'Phonetic practice', 'Slow speech']
      })
    }
    
    if (analysis.fluency.score < 70) {
      suggestions.push({
        title: 'Improve Speech Flow',
        description: 'Practice speaking in longer phrases without hesitation',
        exercises: ['Reading aloud', 'Shadowing exercises', 'Breathing techniques']
      })
    }
    
    if (analysis.intonation.score < 70) {
      suggestions.push({
        title: 'Work on Intonation',
        description: 'Practice varying your voice pitch and emphasis',
        exercises: ['Question intonation', 'Stress practice', 'Emotion expression']
      })
    }
    
    return suggestions
  }

  // Helper methods
  normalizeText(text) {
    return text.toLowerCase().replace(/[^\w\s]/g, '').trim()
  }

  wordsMatch(word1, word2) {
    return this.phoneticAlgorithm.process(word1) === this.phoneticAlgorithm.process(word2)
  }

  estimateWPM(wordCount, durationSeconds) {
    if (!durationSeconds || durationSeconds === 0) return 0
    return Math.round((wordCount / durationSeconds) * 60)
  }

  countHesitations(text) {
    const hesitations = text.match(/\b(um|uh|er|ah|hmm)\b/gi)
    return hesitations ? hesitations.length : 0
  }

  countRepetitions(text) {
    const words = text.toLowerCase().split(/\s+/)
    let repetitions = 0
    for (let i = 1; i < words.length; i++) {
      if (words[i] === words[i-1]) repetitions++
    }
    return repetitions
  }

  countFillerWords(text) {
    const fillers = text.match(/\b(like|you know|actually|basically|literally)\b/gi)
    return fillers ? fillers.length : 0
  }

  countQuestions(text) {
    return (text.match(/\?/g) || []).length
  }

  detectEmphasis(text) {
    // Simple emphasis detection based on capitalization and punctuation
    const emphasis = text.match(/[A-Z]{2,}|!{2,}/g)
    return emphasis || []
  }

  analyzeSentenceCompleteness(sentences) {
    if (sentences.length === 0) return 0
    const complete = sentences.filter(s => s.trim().length > 3).length
    return complete / sentences.length
  }

  hasCorrectStress(spoken, reference, rules) {
    // Simplified stress analysis
    return spoken.length === reference.length
  }

  getExpectedStress(word, rules) {
    // Simplified stress pattern detection
    return 'primary'
  }

  getDetectedStress(word, rules) {
    return 'primary'
  }

  calculatePhoneticSimilarity(phonetic1, phonetic2) {
    if (!phonetic1 || !phonetic2) return 0
    const maxLength = Math.max(phonetic1.length, phonetic2.length)
    if (maxLength === 0) return 100
    
    let matches = 0
    for (let i = 0; i < Math.min(phonetic1.length, phonetic2.length); i++) {
      if (phonetic1[i] === phonetic2[i]) matches++
    }
    
    return (matches / maxLength) * 100
  }

  getFluencyRating(score) {
    if (score >= 90) return 'Excellent'
    if (score >= 80) return 'Good'
    if (score >= 70) return 'Fair'
    if (score >= 60) return 'Needs Improvement'
    return 'Poor'
  }

  getIntonationRating(score) {
    return this.getFluencyRating(score)
  }

  getStressRating(score) {
    return this.getFluencyRating(score)
  }

  getPhoneticRating(score) {
    return this.getFluencyRating(score)
  }
}

export default new PronunciationService()
