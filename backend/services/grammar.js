import axios from 'axios'
import natural from 'natural'

/**
 * Grammar Analysis Service
 * Provides real-time grammar checking and feedback using LanguageTool
 */

class GrammarService {
  constructor() {
    this.languageToolUrl = process.env.LANGUAGETOOL_URL || 'https://api.languagetool.org/v2'
    this.fillerWords = {
      en: ['um', 'uh', 'like', 'you know', 'actually', 'basically', 'literally', 'sort of', 'kind of'],
      fr: ['euh', 'ben', 'alors', 'donc', 'en fait', 'quoi'],
      es: ['eh', 'este', 'bueno', 'pues', 'o sea'],
      de: ['äh', 'ähm', 'also', 'ja', 'halt'],
      ar: ['يعني', 'إذن', 'طيب', 'أه']
    }
  }

  /**
   * Analyze text for grammar errors and improvements
   */
  async analyzeGrammar(text, language = 'en') {
    try {
      const response = await axios.post(`${this.languageToolUrl}/check`, {
        text: text,
        language: language,
        enabledOnly: false
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        timeout: 5000
      })

      const matches = response.data.matches || []
      
      return {
        success: true,
        errors: matches.map(match => ({
          message: match.message,
          shortMessage: match.shortMessage,
          offset: match.offset,
          length: match.length,
          category: match.rule.category.name,
          severity: this.getSeverityLevel(match.rule.category.name),
          suggestions: match.replacements.slice(0, 3).map(r => r.value),
          context: match.context.text,
          rule: match.rule.description
        })),
        totalErrors: matches.length,
        errorsByCategory: this.categorizeErrors(matches)
      }
    } catch (error) {
      console.error('Grammar analysis failed:', error.message)
      
      // Fallback to basic analysis
      return this.basicGrammarAnalysis(text, language)
    }
  }

  /**
   * Analyze speech patterns and provide feedback
   */
  analyzeSpeechPatterns(text, language = 'en') {
    const words = text.toLowerCase().split(/\s+/)
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0)
    
    // Detect filler words
    const fillerWordsFound = this.detectFillerWords(text, language)
    
    // Calculate speech metrics
    const metrics = {
      wordCount: words.length,
      sentenceCount: sentences.length,
      averageWordsPerSentence: sentences.length > 0 ? words.length / sentences.length : 0,
      fillerWordCount: fillerWordsFound.length,
      fillerWordPercentage: words.length > 0 ? (fillerWordsFound.length / words.length) * 100 : 0,
      readabilityScore: this.calculateReadabilityScore(text),
      confidenceScore: this.calculateConfidenceScore(text, fillerWordsFound.length)
    }

    // Generate feedback
    const feedback = this.generateSpeechFeedback(metrics, fillerWordsFound, language)

    return {
      success: true,
      metrics,
      fillerWords: fillerWordsFound,
      feedback,
      suggestions: this.generateImprovementSuggestions(metrics, language)
    }
  }

  /**
   * Detect filler words in text
   */
  detectFillerWords(text, language = 'en') {
    const fillers = this.fillerWords[language] || this.fillerWords.en
    const words = text.toLowerCase().split(/\s+/)
    const found = []

    words.forEach((word, index) => {
      fillers.forEach(filler => {
        if (word.includes(filler) || text.toLowerCase().includes(filler)) {
          found.push({
            word: filler,
            position: index,
            context: words.slice(Math.max(0, index - 2), index + 3).join(' ')
          })
        }
      })
    })

    return found
  }

  /**
   * Calculate confidence score based on speech patterns
   */
  calculateConfidenceScore(text, fillerCount) {
    const words = text.split(/\s+/).length
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0).length
    
    let score = 100
    
    // Penalize for filler words
    if (words > 0) {
      const fillerPercentage = (fillerCount / words) * 100
      score -= fillerPercentage * 2
    }
    
    // Penalize for very short or very long sentences
    if (sentences > 0) {
      const avgWordsPerSentence = words / sentences
      if (avgWordsPerSentence < 5) score -= 10
      if (avgWordsPerSentence > 25) score -= 15
    }
    
    // Penalize for repetitive words
    const uniqueWords = new Set(text.toLowerCase().split(/\s+/)).size
    if (words > 0) {
      const uniqueRatio = uniqueWords / words
      if (uniqueRatio < 0.6) score -= 20
    }
    
    return Math.max(0, Math.min(100, score))
  }

  /**
   * Calculate readability score (simplified Flesch Reading Ease)
   */
  calculateReadabilityScore(text) {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const words = text.split(/\s+/)
    const syllables = words.reduce((count, word) => count + this.countSyllables(word), 0)
    
    if (sentences.length === 0 || words.length === 0) return 0
    
    const avgSentenceLength = words.length / sentences.length
    const avgSyllablesPerWord = syllables / words.length
    
    const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord)
    return Math.max(0, Math.min(100, score))
  }

  /**
   * Count syllables in a word (simplified)
   */
  countSyllables(word) {
    word = word.toLowerCase()
    if (word.length <= 3) return 1
    
    const vowels = word.match(/[aeiouy]+/g)
    let count = vowels ? vowels.length : 1
    
    if (word.endsWith('e')) count--
    if (count === 0) count = 1
    
    return count
  }

  /**
   * Generate speech feedback based on metrics
   */
  generateSpeechFeedback(metrics, fillerWords, language) {
    const feedback = []
    
    // Filler word feedback
    if (metrics.fillerWordPercentage > 5) {
      feedback.push({
        type: 'warning',
        category: 'filler_words',
        message: `High usage of filler words (${metrics.fillerWordPercentage.toFixed(1)}%). Try to pause instead of using filler words.`,
        severity: 'medium'
      })
    } else if (metrics.fillerWordPercentage > 2) {
      feedback.push({
        type: 'info',
        category: 'filler_words',
        message: `Moderate use of filler words. Consider reducing them for clearer speech.`,
        severity: 'low'
      })
    }
    
    // Sentence length feedback
    if (metrics.averageWordsPerSentence > 20) {
      feedback.push({
        type: 'warning',
        category: 'sentence_length',
        message: 'Your sentences are quite long. Try breaking them into shorter, clearer statements.',
        severity: 'medium'
      })
    } else if (metrics.averageWordsPerSentence < 5) {
      feedback.push({
        type: 'info',
        category: 'sentence_length',
        message: 'Your sentences are very short. Consider adding more detail to your responses.',
        severity: 'low'
      })
    }
    
    // Confidence feedback
    if (metrics.confidenceScore < 60) {
      feedback.push({
        type: 'warning',
        category: 'confidence',
        message: 'Your speech patterns suggest low confidence. Try speaking more deliberately and reducing hesitations.',
        severity: 'high'
      })
    } else if (metrics.confidenceScore > 85) {
      feedback.push({
        type: 'success',
        category: 'confidence',
        message: 'Great confidence in your speech! Keep up the clear communication.',
        severity: 'positive'
      })
    }
    
    return feedback
  }

  /**
   * Generate improvement suggestions
   */
  generateImprovementSuggestions(metrics, language) {
    const suggestions = []
    
    if (metrics.fillerWordPercentage > 3) {
      suggestions.push({
        type: 'technique',
        title: 'Reduce Filler Words',
        description: 'Practice pausing instead of using filler words. Take a breath when you need time to think.',
        priority: 'high'
      })
    }
    
    if (metrics.averageWordsPerSentence > 18) {
      suggestions.push({
        type: 'structure',
        title: 'Shorter Sentences',
        description: 'Break long sentences into shorter ones. Use periods more frequently.',
        priority: 'medium'
      })
    }
    
    if (metrics.confidenceScore < 70) {
      suggestions.push({
        type: 'confidence',
        title: 'Speak with Authority',
        description: 'Use definitive language. Replace "I think" with "I believe" or direct statements.',
        priority: 'high'
      })
    }
    
    return suggestions
  }

  /**
   * Basic grammar analysis fallback
   */
  basicGrammarAnalysis(text, language) {
    const words = text.split(/\s+/)
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0)
    
    return {
      success: true,
      errors: [],
      totalErrors: 0,
      errorsByCategory: {},
      fallback: true,
      basicMetrics: {
        wordCount: words.length,
        sentenceCount: sentences.length,
        averageWordsPerSentence: sentences.length > 0 ? words.length / sentences.length : 0
      }
    }
  }

  /**
   * Categorize grammar errors
   */
  categorizeErrors(matches) {
    const categories = {}
    matches.forEach(match => {
      const category = match.rule.category.name
      categories[category] = (categories[category] || 0) + 1
    })
    return categories
  }

  /**
   * Get severity level for error category
   */
  getSeverityLevel(category) {
    const severityMap = {
      'GRAMMAR': 'high',
      'TYPOS': 'medium',
      'STYLE': 'low',
      'PUNCTUATION': 'medium',
      'REDUNDANCY': 'low',
      'SEMANTICS': 'high'
    }
    return severityMap[category] || 'medium'
  }
}

export default new GrammarService()
