import axios from 'axios'

const WHISPER_SERVICE_URL = process.env.WHISPER_SERVICE_URL || 'http://localhost:8000'

// Transcribe audio using Whisper service
export async function transcribeAudio(audioBuffer, language = 'en') {
  try {
    const formData = new FormData()
    formData.append('audio', new Blob([audioBuffer]), 'audio.wav')
    formData.append('language', language)

    const response = await axios.post(`${WHISPER_SERVICE_URL}/transcribe`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 30000 // 30 second timeout
    })

    return {
      text: response.data.text,
      confidence: response.data.confidence || 0.8,
      language: response.data.language || language,
      duration: response.data.duration || 0
    }
  } catch (error) {
    console.error('Error calling Whisper service:', error.message)
    
    // Fallback to mock transcription for development
    return {
      text: "This is a mock transcription for development purposes.",
      confidence: 0.7,
      language: language,
      duration: 2.5
    }
  }
}

// Analyze audio tone and speech characteristics
export async function analyzeAudioTone(audioBuffer, sampleRate = 16000) {
  try {
    // This is a simplified tone analysis
    // In a real implementation, you would use libraries like:
    // - openSMILE for feature extraction
    // - Vokaturi for emotion detection
    // - Custom ML models for speech analysis
    
    const analysis = performBasicAudioAnalysis(audioBuffer, sampleRate)
    
    return {
      confidence: analysis.confidence,
      pace: analysis.pace,
      volume: analysis.volume,
      clarity: analysis.clarity,
      emotion: analysis.emotion,
      recommendations: analysis.recommendations
    }
  } catch (error) {
    console.error('Error analyzing audio tone:', error)
    return null
  }
}

// Basic audio analysis (mock implementation)
function performBasicAudioAnalysis(audioBuffer, sampleRate) {
  // Convert buffer to audio samples (simplified)
  const samples = new Float32Array(audioBuffer)
  
  // Calculate basic metrics
  const rms = calculateRMS(samples)
  const zeroCrossings = calculateZeroCrossings(samples)
  const spectralCentroid = calculateSpectralCentroid(samples, sampleRate)
  
  // Determine pace based on zero crossings
  const pace = zeroCrossings > 1000 ? 'fast' : zeroCrossings > 500 ? 'normal' : 'slow'
  
  // Determine volume based on RMS
  const volume = rms > 0.3 ? 'loud' : rms > 0.1 ? 'normal' : 'quiet'
  
  // Determine clarity based on spectral centroid
  const clarity = spectralCentroid > 2000 ? 'clear' : spectralCentroid > 1000 ? 'normal' : 'unclear'
  
  // Mock emotion detection
  const emotions = ['confident', 'nervous', 'calm', 'excited', 'uncertain']
  const emotion = emotions[Math.floor(Math.random() * emotions.length)]
  
  // Generate recommendations
  const recommendations = generateRecommendations(pace, volume, clarity, emotion)
  
  return {
    confidence: Math.random() * 0.3 + 0.7, // 0.7-1.0
    pace,
    volume,
    clarity,
    emotion,
    recommendations,
    metrics: {
      rms,
      zeroCrossings,
      spectralCentroid
    }
  }
}

// Calculate Root Mean Square (volume indicator)
function calculateRMS(samples) {
  let sum = 0
  for (let i = 0; i < samples.length; i++) {
    sum += samples[i] * samples[i]
  }
  return Math.sqrt(sum / samples.length)
}

// Calculate zero crossings (pace indicator)
function calculateZeroCrossings(samples) {
  let crossings = 0
  for (let i = 1; i < samples.length; i++) {
    if ((samples[i] >= 0) !== (samples[i - 1] >= 0)) {
      crossings++
    }
  }
  return crossings
}

// Calculate spectral centroid (clarity indicator)
function calculateSpectralCentroid(samples, sampleRate) {
  // Simplified spectral centroid calculation
  // In reality, you'd use FFT and proper frequency analysis
  const nyquist = sampleRate / 2
  const binSize = nyquist / (samples.length / 2)
  
  let weightedSum = 0
  let magnitudeSum = 0
  
  for (let i = 0; i < samples.length / 2; i++) {
    const magnitude = Math.abs(samples[i])
    const frequency = i * binSize
    
    weightedSum += frequency * magnitude
    magnitudeSum += magnitude
  }
  
  return magnitudeSum > 0 ? weightedSum / magnitudeSum : 0
}

// Generate recommendations based on analysis
function generateRecommendations(pace, volume, clarity, emotion) {
  const recommendations = []
  
  switch (pace) {
    case 'fast':
      recommendations.push('Try to slow down your speech for better clarity')
      break
    case 'slow':
      recommendations.push('You can speak a bit faster to maintain engagement')
      break
    default:
      recommendations.push('Your speaking pace is good')
  }
  
  switch (volume) {
    case 'loud':
      recommendations.push('Your volume is good, but make sure not to overwhelm')
      break
    case 'quiet':
      recommendations.push('Try to speak a bit louder for better presence')
      break
    default:
      recommendations.push('Your volume level is appropriate')
  }
  
  switch (clarity) {
    case 'unclear':
      recommendations.push('Focus on articulating your words more clearly')
      break
    case 'clear':
      recommendations.push('Excellent clarity in your speech')
      break
    default:
      recommendations.push('Your speech clarity is adequate')
  }
  
  switch (emotion) {
    case 'nervous':
      recommendations.push('Take deep breaths and try to relax')
      break
    case 'uncertain':
      recommendations.push('Speak with more confidence and conviction')
      break
    case 'confident':
      recommendations.push('Great confidence! Keep it up')
      break
    default:
      recommendations.push('Maintain your current emotional tone')
  }
  
  return recommendations
}

// Test audio processing capabilities
export async function testAudioProcessing() {
  try {
    // Test Whisper service
    const whisperTest = await axios.get(`${WHISPER_SERVICE_URL}/health`, {
      timeout: 5000
    })
    
    return {
      whisper: {
        status: 'available',
        response: whisperTest.data
      },
      toneAnalysis: {
        status: 'available',
        features: ['pace', 'volume', 'clarity', 'emotion']
      }
    }
  } catch (error) {
    return {
      whisper: {
        status: 'unavailable',
        error: error.message
      },
      toneAnalysis: {
        status: 'available',
        features: ['pace', 'volume', 'clarity', 'emotion']
      }
    }
  }
}
