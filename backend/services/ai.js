import axios from 'axios'
import { resumeDb, sessionDb } from './database.js'

const OLLAMA_URL = process.env.OLLAMA_URL || 'http://localhost:11434'
const DEFAULT_MODEL = 'llama3.2:1b'

// Initialize Ollama connection
export async function initializeOllama() {
  try {
    const response = await axios.get(`${OLLAMA_URL}/api/tags`)
    console.log('✅ Ollama connected successfully')

    // Warm up the model with a quick test
    console.log('🔥 Warming up Ollama model...')
    const warmupResponse = await axios.post(`${OLLAMA_URL}/api/generate`, {
      model: DEFAULT_MODEL,
      prompt: 'Hello',
      stream: false,
      options: {
        num_predict: 5
      }
    }, {
      timeout: 60000
    })

    if (warmupResponse.data && warmupResponse.data.response) {
      console.log('✅ Ollama model warmed up successfully')
    }

    return response.data
  } catch (error) {
    console.error('❌ Failed to connect to Ollama:', error.message)
    throw error
  }
}

// Generate AI suggestion based on transcript and context
export async function generateAISuggestion(transcript, sessionId) {
  try {
    // Get session context
    const session = await sessionDb.getById(sessionId)
    if (!session) {
      throw new Error('Session not found')
    }

    // Get user's resume data
    const resume = await resumeDb.getByUserName(session.user_name)
    
    // Build context for AI
    const context = buildContext(transcript, session, resume)
    
    // Generate suggestion using Ollama
    const suggestion = await callOllama(context)
    
    return {
      text: suggestion,
      context: 'interview_assistance',
      confidence: 0.85
    }
  } catch (error) {
    console.error('Error generating AI suggestion:', error)
    return null
  }
}

// Build context for AI prompt
function buildContext(transcript, session, resume) {
  let context = `You are VocaPilot, an AI interview assistant. Your role is to help the user during a live interview by providing concise, relevant suggestions based on what they're being asked.

Current situation:
- Language: ${session.language}
- User: ${session.user_name}

Interview Question/Context: "${transcript}"

`

  // Add job description if available
  if (session.job_description) {
    context += `Job Description:
${session.job_description}

`
  }

  // Add resume information if available
  if (resume && resume.parsed_data) {
    const resumeData = resume.parsed_data
    context += `User's Background:
`
    if (resumeData.skills) {
      context += `Skills: ${resumeData.skills.join(', ')}
`
    }
    if (resumeData.experience) {
      context += `Experience: ${resumeData.experience.map(exp => `${exp.title} at ${exp.company}`).join(', ')}
`
    }
    if (resumeData.education) {
      context += `Education: ${resumeData.education.map(edu => `${edu.degree} from ${edu.institution}`).join(', ')}
`
    }
  }

  context += `
REAL-TIME INTERVIEW ASSISTANCE:
Provide an INSTANT, confident response (1-2 sentences) that the candidate can say immediately. Focus on:
- Direct answer to the question
- Specific example from their background
- Professional and conversational tone
- Ready to speak now

RESPONSE SUGGESTION:`

  return context
}

// Call Ollama API
async function callOllama(prompt) {
  try {
    console.log('🤖 Calling Ollama with model:', DEFAULT_MODEL)

    const response = await axios.post(`${OLLAMA_URL}/api/generate`, {
      model: DEFAULT_MODEL,
      prompt: prompt,
      stream: false,
      options: {
        temperature: 0.7,
        top_p: 0.9,
        num_predict: 100,  // Limit response length for speed
        stop: ['\n\n', 'RESPONSE:', 'Response:', 'USER:', 'ASSISTANT:']  // Stop at natural breaks
      }
    }, {
      timeout: 30000, // 30 second timeout for first response
      headers: {
        'Content-Type': 'application/json'
      }
    })

    console.log('✅ Ollama response received, status:', response.status)

    if (response.data && response.data.response) {
      const aiResponse = response.data.response.trim()
      console.log('🎯 AI generated response:', aiResponse.substring(0, 100) + '...')
      return aiResponse
    } else {
      console.error('❌ Invalid response format from Ollama:', response.data)
      throw new Error('Invalid response format from Ollama')
    }
  } catch (error) {
    console.error('❌ Error calling Ollama:', error.message)

    // Fallback to rule-based suggestions
    return generateFallbackSuggestion(prompt)
  }
}

// Fallback suggestion when AI is unavailable
function generateFallbackSuggestion(prompt) {
  const lowerPrompt = prompt.toLowerCase()
  
  // Common interview question patterns
  if (lowerPrompt.includes('tell me about yourself')) {
    return "Structure your response: current role → relevant experience → key achievements → why you're interested in this position."
  }
  
  if (lowerPrompt.includes('weakness') || lowerPrompt.includes('weaknesses')) {
    return "Choose a real weakness you're actively working to improve, explain the steps you're taking, and show progress."
  }
  
  if (lowerPrompt.includes('strength') || lowerPrompt.includes('strengths')) {
    return "Pick a strength relevant to the job, provide a specific example, and explain how it would benefit this role."
  }
  
  if (lowerPrompt.includes('why do you want') || lowerPrompt.includes('why are you interested')) {
    return "Connect your career goals with the company's mission, mention specific aspects that excite you, and show you've researched the role."
  }
  
  if (lowerPrompt.includes('where do you see yourself')) {
    return "Align your goals with potential growth in this role, show ambition but be realistic, and emphasize learning and contribution."
  }
  
  if (lowerPrompt.includes('experience') || lowerPrompt.includes('background')) {
    return "Highlight relevant experience, quantify achievements where possible, and connect your background to the job requirements."
  }
  
  if (lowerPrompt.includes('challenge') || lowerPrompt.includes('difficult')) {
    return "Use the STAR method: Situation → Task → Action → Result. Focus on what you learned and how you grew."
  }
  
  if (lowerPrompt.includes('questions') || lowerPrompt.includes('ask me')) {
    return "Ask about team dynamics, growth opportunities, company culture, or specific challenges in the role."
  }
  
  // Generic helpful response
  return "Take a moment to think, structure your response clearly, and provide specific examples from your experience."
}

// Generate interview feedback
export async function generateInterviewFeedback(sessionId) {
  try {
    const session = await sessionDb.getById(sessionId)
    if (!session) {
      throw new Error('Session not found')
    }

    const transcript = session.transcript || ''
    if (transcript.length < 100) {
      return {
        overall_score: 7,
        feedback: "Session was too short for comprehensive analysis.",
        suggestions: ["Try longer practice sessions for better feedback."]
      }
    }

    // Analyze transcript for feedback
    const analysis = analyzeTranscript(transcript)
    
    return {
      overall_score: analysis.score,
      feedback: analysis.feedback,
      suggestions: analysis.suggestions,
      strengths: analysis.strengths,
      areas_for_improvement: analysis.improvements
    }
  } catch (error) {
    console.error('Error generating interview feedback:', error)
    throw error
  }
}

// Analyze transcript for patterns and quality
function analyzeTranscript(transcript) {
  const words = transcript.split(' ')
  const sentences = transcript.split(/[.!?]+/).filter(s => s.trim().length > 0)
  
  let score = 7 // Base score
  const feedback = []
  const suggestions = []
  const strengths = []
  const improvements = []
  
  // Analyze length and detail
  if (words.length > 500) {
    score += 1
    strengths.push("Provided detailed responses")
  } else if (words.length < 200) {
    score -= 1
    improvements.push("Provide more detailed responses")
    suggestions.push("Elaborate on your examples with specific details")
  }
  
  // Analyze filler words
  const fillerWords = (transcript.match(/\b(um|uh|like|you know|actually)\b/gi) || []).length
  const fillerRatio = fillerWords / words.length
  
  if (fillerRatio < 0.03) {
    score += 1
    strengths.push("Minimal use of filler words")
  } else if (fillerRatio > 0.08) {
    score -= 1
    improvements.push("Reduce filler words")
    suggestions.push("Practice pausing instead of using 'um' or 'uh'")
  }
  
  // Analyze confidence indicators
  const uncertainWords = (transcript.match(/\b(maybe|perhaps|i think|i guess|probably)\b/gi) || []).length
  const uncertainRatio = uncertainWords / words.length
  
  if (uncertainRatio < 0.05) {
    score += 1
    strengths.push("Confident and decisive language")
  } else if (uncertainRatio > 0.12) {
    score -= 1
    improvements.push("Use more confident language")
    suggestions.push("Replace uncertain phrases with definitive statements")
  }
  
  // Analyze structure
  const avgWordsPerSentence = words.length / sentences.length
  if (avgWordsPerSentence > 15 && avgWordsPerSentence < 25) {
    score += 0.5
    strengths.push("Well-structured responses")
  } else if (avgWordsPerSentence > 30) {
    improvements.push("Break down long sentences")
    suggestions.push("Use shorter, clearer sentences for better impact")
  }
  
  // Generate overall feedback
  if (score >= 8.5) {
    feedback.push("Excellent interview performance! You demonstrated strong communication skills.")
  } else if (score >= 7) {
    feedback.push("Good interview performance with room for improvement.")
  } else {
    feedback.push("Your interview skills need some work, but you're on the right track.")
  }
  
  return {
    score: Math.min(10, Math.max(1, score)),
    feedback: feedback.join(' '),
    suggestions,
    strengths,
    improvements
  }
}

// Helper function to get language name
function getLanguageName(code) {
  const languages = {
    'en': 'English',
    'fr': 'French',
    'es': 'Spanish',
    'ar': 'Arabic',
    'de': 'German'
  }
  return languages[code] || 'English'
}

export { DEFAULT_MODEL }
