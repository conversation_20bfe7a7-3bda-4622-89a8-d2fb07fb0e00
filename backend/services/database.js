import Database from 'better-sqlite3'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Create database connection
const dbPath = path.join(__dirname, '..', 'vocapilot.db')
const db = new Database(dbPath)

// Database helper methods
const dbRun = (sql, params = []) => {
  try {
    const stmt = db.prepare(sql)
    const result = stmt.run(params)
    return { lastID: result.lastInsertRowid, changes: result.changes }
  } catch (err) {
    throw err
  }
}

const dbGet = (sql, params = []) => {
  try {
    const stmt = db.prepare(sql)
    return stmt.get(params)
  } catch (err) {
    throw err
  }
}

const dbAll = (sql, params = []) => {
  try {
    const stmt = db.prepare(sql)
    return stmt.all(params)
  } catch (err) {
    throw err
  }
}

// Initialize database tables
export function initializeDatabase() {
  try {
    // Sessions table
    dbRun(`
      CREATE TABLE IF NOT EXISTS sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_name TEXT NOT NULL,
        job_description TEXT,
        language TEXT DEFAULT 'en',
        start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        end_time DATETIME,
        duration INTEGER,
        transcript TEXT,
        suggestions TEXT,
        tone_analysis TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Resume data table
    dbRun(`
      CREATE TABLE IF NOT EXISTS resumes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_name TEXT NOT NULL,
        filename TEXT NOT NULL,
        content TEXT NOT NULL,
        parsed_data TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Audio transcripts table
    dbRun(`
      CREATE TABLE IF NOT EXISTS transcripts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id INTEGER,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        text TEXT NOT NULL,
        confidence REAL,
        language TEXT,
        FOREIGN KEY (session_id) REFERENCES sessions (id)
      )
    `)

    // AI suggestions table
    dbRun(`
      CREATE TABLE IF NOT EXISTS suggestions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id INTEGER,
        transcript_id INTEGER,
        suggestion TEXT NOT NULL,
        type TEXT DEFAULT 'general',
        confidence REAL,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (session_id) REFERENCES sessions (id),
        FOREIGN KEY (transcript_id) REFERENCES transcripts (id)
      )
    `)

    console.log('Database tables initialized successfully')
  } catch (error) {
    console.error('Error initializing database:', error)
    throw error
  }
}

// Session operations
export const sessionDb = {
  create(sessionData) {
    const { user_name, job_description, language } = sessionData
    const result = dbRun(
      'INSERT INTO sessions (user_name, job_description, language) VALUES (?, ?, ?)',
      [user_name, job_description, language]
    )
    return result.lastID
  },

  update(id, updateData) {
    const fields = []
    const values = []
    
    Object.entries(updateData).forEach(([key, value]) => {
      fields.push(`${key} = ?`)
      values.push(value)
    })
    
    values.push(id)
    
    const result = dbRun(
      `UPDATE sessions SET ${fields.join(', ')} WHERE id = ?`,
      values
    )
    return result.changes > 0
  },

  getById(id) {
    return dbGet('SELECT * FROM sessions WHERE id = ?', [id])
  },

  getByUserName(userName, limit = 10) {
    return dbAll(
      'SELECT * FROM sessions WHERE user_name = ? ORDER BY created_at DESC LIMIT ?',
      [userName, limit]
    )
  },

  delete(id) {
    const result = dbRun('DELETE FROM sessions WHERE id = ?', [id])
    return result.changes > 0
  }
}

// Resume operations
export const resumeDb = {
  create(resumeData) {
    const { user_name, filename, content, parsed_data } = resumeData
    const result = dbRun(
      'INSERT INTO resumes (user_name, filename, content, parsed_data) VALUES (?, ?, ?, ?)',
      [user_name, filename, content, JSON.stringify(parsed_data)]
    )
    return result.lastID
  },

  getByUserName(userName) {
    const result = dbGet(
      'SELECT * FROM resumes WHERE user_name = ? ORDER BY created_at DESC LIMIT 1',
      [userName]
    )
    if (result && result.parsed_data) {
      result.parsed_data = JSON.parse(result.parsed_data)
    }
    return result
  },

  update(id, updateData) {
    const { content, parsed_data } = updateData
    const result = dbRun(
      'UPDATE resumes SET content = ?, parsed_data = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [content, JSON.stringify(parsed_data), id]
    )
    return result.changes > 0
  },

  delete(id) {
    const result = dbRun('DELETE FROM resumes WHERE id = ?', [id])
    return result.changes > 0
  }
}

// Transcript operations
export const transcriptDb = {
  create(transcriptData) {
    const { session_id, text, confidence, language } = transcriptData
    const result = dbRun(
      'INSERT INTO transcripts (session_id, text, confidence, language) VALUES (?, ?, ?, ?)',
      [session_id, text, confidence, language]
    )
    return result.lastID
  },

  getBySessionId(sessionId) {
    return dbAll(
      'SELECT * FROM transcripts WHERE session_id = ? ORDER BY timestamp ASC',
      [sessionId]
    )
  },

  delete(id) {
    const result = dbRun('DELETE FROM transcripts WHERE id = ?', [id])
    return result.changes > 0
  }
}

// Suggestion operations
export const suggestionDb = {
  create(suggestionData) {
    const { session_id, transcript_id, suggestion, type, confidence } = suggestionData
    const result = dbRun(
      'INSERT INTO suggestions (session_id, transcript_id, suggestion, type, confidence) VALUES (?, ?, ?, ?, ?)',
      [session_id, transcript_id, suggestion, type, confidence]
    )
    return result.lastID
  },

  getBySessionId(sessionId) {
    return dbAll(
      'SELECT * FROM suggestions WHERE session_id = ? ORDER BY timestamp ASC',
      [sessionId]
    )
  },

  getByTranscriptId(transcriptId) {
    return dbAll(
      'SELECT * FROM suggestions WHERE transcript_id = ? ORDER BY timestamp ASC',
      [transcriptId]
    )
  },

  delete(id) {
    const result = dbRun('DELETE FROM suggestions WHERE id = ?', [id])
    return result.changes > 0
  }
}

// Close database connection on process exit
process.on('exit', () => {
  db.close()
})

process.on('SIGINT', () => {
  db.close()
  process.exit(0)
})

process.on('SIGTERM', () => {
  db.close()
  process.exit(0)
})
