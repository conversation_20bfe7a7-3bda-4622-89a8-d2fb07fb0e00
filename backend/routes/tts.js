import express from 'express'
import { body, validationResult } from 'express-validator'
import TTSService from '../services/tts.js'

const router = express.Router()

/**
 * @route POST /api/tts/synthesize
 * @desc Synthesize speech from text
 */
router.post('/synthesize', [
  body('text').notEmpty().withMessage('Text is required').isLength({ max: 1000 }).withMessage('Text too long (max 1000 characters)'),
  body('language').optional().isIn(['en', 'fr', 'es', 'de']).withMessage('Invalid language'),
  body('voiceType').optional().isIn(['female', 'male']).withMessage('Invalid voice type'),
  body('speed').optional().isFloat({ min: 0.5, max: 2.0 }).withMessage('Speed must be between 0.5 and 2.0')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { text, language = 'en', voiceType = 'female', speed = 1.0 } = req.body

    const result = await TTSService.synthesizeText(text, {
      language,
      voiceType,
      speed
    })

    if (result.success) {
      res.set({
        'Content-Type': 'audio/wav',
        'Content-Disposition': 'attachment; filename="speech.wav"',
        'X-Processing-Time': result.processingTime,
        'X-Text-Length': result.textLength,
        'X-Audio-Duration': result.audioDuration
      })
      res.send(result.audioBuffer)
    } else {
      res.status(500).json({
        success: false,
        error: 'TTS synthesis failed'
      })
    }
  } catch (error) {
    console.error('TTS synthesis error:', error)
    res.status(500).json({
      success: false,
      error: error.message
    })
  }
})

/**
 * @route POST /api/tts/ai-response
 * @desc Generate speech for AI responses
 */
router.post('/ai-response', [
  body('text').notEmpty().withMessage('Text is required'),
  body('language').optional().isIn(['en', 'fr', 'es', 'de']).withMessage('Invalid language'),
  body('voiceType').optional().isIn(['female', 'male']).withMessage('Invalid voice type')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { text, language = 'en', voiceType = 'female' } = req.body

    const result = await TTSService.generateAIResponseSpeech(text, language, voiceType)

    if (result.success) {
      res.set({
        'Content-Type': 'audio/wav',
        'Content-Disposition': 'attachment; filename="ai-response.wav"',
        'X-Original-Text': encodeURIComponent(result.originalText),
        'X-Cleaned-Text': encodeURIComponent(result.cleanedText),
        'X-Processing-Time': result.metadata.processingTime,
        'X-Audio-Duration': result.metadata.audioDuration
      })
      res.send(result.audioBuffer)
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
        originalText: result.originalText
      })
    }
  } catch (error) {
    console.error('AI response TTS error:', error)
    res.status(500).json({
      success: false,
      error: error.message
    })
  }
})

/**
 * @route GET /api/tts/voices
 * @desc Get supported voices
 */
router.get('/voices', async (req, res) => {
  try {
    const result = await TTSService.getSupportedVoices()
    res.json(result)
  } catch (error) {
    console.error('Error getting TTS voices:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get supported voices'
    })
  }
})

/**
 * @route GET /api/tts/health
 * @desc Check TTS service health
 */
router.get('/health', async (req, res) => {
  try {
    const isAvailable = await TTSService.isAvailable()
    
    res.json({
      success: true,
      available: isAvailable,
      service: 'TTS Service',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('TTS health check error:', error)
    res.status(500).json({
      success: false,
      error: 'TTS health check failed'
    })
  }
})

/**
 * @route POST /api/tts/estimate-duration
 * @desc Estimate speech duration for given text
 */
router.post('/estimate-duration', [
  body('text').notEmpty().withMessage('Text is required'),
  body('wordsPerMinute').optional().isInt({ min: 50, max: 300 }).withMessage('Words per minute must be between 50 and 300')
], (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { text, wordsPerMinute = 150 } = req.body
    const duration = TTSService.estimateSpeechDuration(text, wordsPerMinute)
    
    res.json({
      success: true,
      text,
      estimatedDuration: duration,
      wordsPerMinute,
      wordCount: text.trim().split(/\s+/).length
    })
  } catch (error) {
    console.error('Duration estimation error:', error)
    res.status(500).json({
      success: false,
      error: 'Duration estimation failed'
    })
  }
})

/**
 * @route POST /api/tts/split-text
 * @desc Split long text into TTS-friendly chunks
 */
router.post('/split-text', [
  body('text').notEmpty().withMessage('Text is required'),
  body('maxLength').optional().isInt({ min: 100, max: 1000 }).withMessage('Max length must be between 100 and 1000')
], (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { text, maxLength = 500 } = req.body
    const chunks = TTSService.splitTextForTTS(text, maxLength)
    
    res.json({
      success: true,
      originalText: text,
      chunks,
      chunkCount: chunks.length,
      maxLength
    })
  } catch (error) {
    console.error('Text splitting error:', error)
    res.status(500).json({
      success: false,
      error: 'Text splitting failed'
    })
  }
})

/**
 * @route GET /api/tts/test
 * @desc Test TTS functionality
 */
router.get('/test', async (req, res) => {
  try {
    const result = await TTSService.testTTS()
    res.json(result)
  } catch (error) {
    console.error('TTS test error:', error)
    res.status(500).json({
      success: false,
      error: 'TTS test failed',
      details: error.message
    })
  }
})

export default router
