import express from 'express'
import { body, validationResult } from 'express-validator'
import GrammarService from '../services/grammar.js'

const router = express.Router()

/**
 * @route POST /api/grammar/analyze
 * @desc Analyze text for grammar errors and improvements
 */
router.post('/analyze', [
  body('text').notEmpty().withMessage('Text is required'),
  body('language').optional().isIn(['en', 'fr', 'es', 'de', 'ar']).withMessage('Invalid language')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { text, language = 'en' } = req.body

    const result = await GrammarService.analyzeGrammar(text, language)
    
    res.json(result)
  } catch (error) {
    console.error('Grammar analysis error:', error)
    res.status(500).json({
      success: false,
      error: 'Grammar analysis failed'
    })
  }
})

/**
 * @route POST /api/grammar/speech-patterns
 * @desc Analyze speech patterns and provide feedback
 */
router.post('/speech-patterns', [
  body('text').notEmpty().withMessage('Text is required'),
  body('language').optional().isIn(['en', 'fr', 'es', 'de', 'ar']).withMessage('Invalid language')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { text, language = 'en' } = req.body

    const result = GrammarService.analyzeSpeechPatterns(text, language)
    
    res.json(result)
  } catch (error) {
    console.error('Speech pattern analysis error:', error)
    res.status(500).json({
      success: false,
      error: 'Speech pattern analysis failed'
    })
  }
})

/**
 * @route POST /api/grammar/filler-words
 * @desc Detect filler words in text
 */
router.post('/filler-words', [
  body('text').notEmpty().withMessage('Text is required'),
  body('language').optional().isIn(['en', 'fr', 'es', 'de', 'ar']).withMessage('Invalid language')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { text, language = 'en' } = req.body

    const fillerWords = GrammarService.detectFillerWords(text, language)
    
    res.json({
      success: true,
      fillerWords,
      count: fillerWords.length,
      text
    })
  } catch (error) {
    console.error('Filler word detection error:', error)
    res.status(500).json({
      success: false,
      error: 'Filler word detection failed'
    })
  }
})

/**
 * @route POST /api/grammar/confidence-score
 * @desc Calculate confidence score based on speech patterns
 */
router.post('/confidence-score', [
  body('text').notEmpty().withMessage('Text is required'),
  body('fillerCount').optional().isInt({ min: 0 }).withMessage('Filler count must be a non-negative integer')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { text, fillerCount } = req.body
    const actualFillerCount = fillerCount !== undefined 
      ? fillerCount 
      : GrammarService.detectFillerWords(text).length

    const score = GrammarService.calculateConfidenceScore(text, actualFillerCount)
    
    res.json({
      success: true,
      confidenceScore: score,
      fillerCount: actualFillerCount,
      text
    })
  } catch (error) {
    console.error('Confidence score calculation error:', error)
    res.status(500).json({
      success: false,
      error: 'Confidence score calculation failed'
    })
  }
})

/**
 * @route GET /api/grammar/supported-languages
 * @desc Get list of supported languages for grammar analysis
 */
router.get('/supported-languages', (req, res) => {
  try {
    const supportedLanguages = {
      en: 'English',
      fr: 'French',
      es: 'Spanish',
      de: 'German',
      ar: 'Arabic'
    }

    res.json({
      success: true,
      languages: supportedLanguages
    })
  } catch (error) {
    console.error('Error getting supported languages:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get supported languages'
    })
  }
})

/**
 * @route GET /api/grammar/test
 * @desc Test grammar service functionality
 */
router.get('/test', async (req, res) => {
  try {
    const testText = "This is a test sentence for grammar analysis. Um, it contains some filler words like, you know, to test the system."
    
    const grammarResult = await GrammarService.analyzeGrammar(testText, 'en')
    const speechResult = GrammarService.analyzeSpeechPatterns(testText, 'en')
    
    res.json({
      success: true,
      message: 'Grammar service test completed',
      testText,
      results: {
        grammar: grammarResult,
        speechPatterns: speechResult
      }
    })
  } catch (error) {
    console.error('Grammar service test error:', error)
    res.status(500).json({
      success: false,
      error: 'Grammar service test failed',
      details: error.message
    })
  }
})

export default router
