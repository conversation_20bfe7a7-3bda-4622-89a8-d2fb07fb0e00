import express from 'express'
import { body, validationResult } from 'express-validator'
import PronunciationService from '../services/pronunciation.js'

const router = express.Router()

/**
 * @route POST /api/pronunciation/analyze
 * @desc Analyze pronunciation quality
 */
router.post('/analyze', [
  body('spokenText').notEmpty().withMessage('Spoken text is required'),
  body('referenceText').notEmpty().withMessage('Reference text is required'),
  body('language').optional().isIn(['en', 'fr', 'es', 'de']).withMessage('Invalid language'),
  body('audioMetrics').optional().isObject().withMessage('Audio metrics must be an object')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { spokenText, referenceText, language = 'en', audioMetrics = {} } = req.body

    const result = await PronunciationService.analyzePronunciation(
      spokenText,
      referenceText,
      language,
      audioMetrics
    )
    
    res.json(result)
  } catch (error) {
    console.error('Pronunciation analysis error:', error)
    res.status(500).json({
      success: false,
      error: 'Pronunciation analysis failed'
    })
  }
})

/**
 * @route POST /api/pronunciation/accuracy
 * @desc Calculate pronunciation accuracy
 */
router.post('/accuracy', [
  body('spokenText').notEmpty().withMessage('Spoken text is required'),
  body('referenceText').notEmpty().withMessage('Reference text is required')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { spokenText, referenceText } = req.body
    const accuracy = PronunciationService.calculateAccuracy(spokenText, referenceText)
    
    res.json({
      success: true,
      accuracy,
      spokenText,
      referenceText,
      rating: accuracy >= 90 ? 'Excellent' : 
              accuracy >= 80 ? 'Good' : 
              accuracy >= 70 ? 'Fair' : 
              accuracy >= 60 ? 'Needs Improvement' : 'Poor'
    })
  } catch (error) {
    console.error('Accuracy calculation error:', error)
    res.status(500).json({
      success: false,
      error: 'Accuracy calculation failed'
    })
  }
})

/**
 * @route POST /api/pronunciation/fluency
 * @desc Analyze speech fluency
 */
router.post('/fluency', [
  body('spokenText').notEmpty().withMessage('Spoken text is required'),
  body('audioMetrics').optional().isObject().withMessage('Audio metrics must be an object')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { spokenText, audioMetrics = {} } = req.body
    const fluencyAnalysis = PronunciationService.analyzeFluency(spokenText, audioMetrics)
    
    res.json({
      success: true,
      fluency: fluencyAnalysis,
      spokenText
    })
  } catch (error) {
    console.error('Fluency analysis error:', error)
    res.status(500).json({
      success: false,
      error: 'Fluency analysis failed'
    })
  }
})

/**
 * @route POST /api/pronunciation/word-stress
 * @desc Analyze word stress patterns
 */
router.post('/word-stress', [
  body('spokenText').notEmpty().withMessage('Spoken text is required'),
  body('referenceText').notEmpty().withMessage('Reference text is required'),
  body('language').optional().isIn(['en', 'fr', 'es', 'de']).withMessage('Invalid language')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { spokenText, referenceText, language = 'en' } = req.body
    const stressAnalysis = PronunciationService.analyzeWordStress(spokenText, referenceText, language)
    
    res.json({
      success: true,
      wordStress: stressAnalysis,
      spokenText,
      referenceText,
      language
    })
  } catch (error) {
    console.error('Word stress analysis error:', error)
    res.status(500).json({
      success: false,
      error: 'Word stress analysis failed'
    })
  }
})

/**
 * @route POST /api/pronunciation/phonetic
 * @desc Analyze phonetic accuracy
 */
router.post('/phonetic', [
  body('spokenText').notEmpty().withMessage('Spoken text is required'),
  body('referenceText').notEmpty().withMessage('Reference text is required')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { spokenText, referenceText } = req.body
    const phoneticAnalysis = PronunciationService.analyzePhoneticAccuracy(spokenText, referenceText)
    
    res.json({
      success: true,
      phonetic: phoneticAnalysis,
      spokenText,
      referenceText
    })
  } catch (error) {
    console.error('Phonetic analysis error:', error)
    res.status(500).json({
      success: false,
      error: 'Phonetic analysis failed'
    })
  }
})

/**
 * @route GET /api/pronunciation/practice-sentences
 * @desc Get practice sentences for pronunciation training
 */
router.get('/practice-sentences', (req, res) => {
  try {
    const { language = 'en', difficulty = 'medium', count = 10 } = req.query

    const practiceSentences = {
      en: {
        easy: [
          "Hello, how are you today?",
          "The weather is nice outside.",
          "I like to read books.",
          "Can you help me please?",
          "Thank you very much."
        ],
        medium: [
          "The quick brown fox jumps over the lazy dog.",
          "She sells seashells by the seashore.",
          "How much wood would a woodchuck chuck?",
          "Peter Piper picked a peck of pickled peppers.",
          "I scream, you scream, we all scream for ice cream."
        ],
        hard: [
          "The sixth sick sheik's sixth sheep's sick.",
          "Red leather, yellow leather, red leather, yellow leather.",
          "Unique New York, unique New York, you know you need unique New York.",
          "How can a clam cram in a clean cream can?",
          "Fuzzy Wuzzy was a bear, Fuzzy Wuzzy had no hair."
        ]
      },
      fr: {
        easy: [
          "Bonjour, comment allez-vous?",
          "Il fait beau aujourd'hui.",
          "J'aime lire des livres.",
          "Pouvez-vous m'aider s'il vous plaît?",
          "Merci beaucoup."
        ],
        medium: [
          "Les chaussettes de l'archiduchesse sont-elles sèches?",
          "Un chasseur sachant chasser doit savoir chasser sans son chien.",
          "Trois petites truites cuites, trois petites truites crues.",
          "Si six scies scient six cyprès, six cent scies scient six cent cyprès.",
          "Panier piano, panier piano, panier piano."
        ]
      },
      es: {
        easy: [
          "Hola, ¿cómo estás hoy?",
          "El tiempo está hermoso afuera.",
          "Me gusta leer libros.",
          "¿Puedes ayudarme por favor?",
          "Muchas gracias."
        ],
        medium: [
          "Tres tristes tigres tragaban trigo en un trigal.",
          "El perro de San Roque no tiene rabo porque Ramón Ramírez se lo ha cortado.",
          "Pablito clavó un clavito en la calva de un calvito.",
          "Cuando cuentes cuentos, cuenta cuántos cuentos cuentas.",
          "El cielo está enladrillado, ¿quién lo desenladrillará?"
        ]
      }
    }

    const sentences = practiceSentences[language]?.[difficulty] || practiceSentences.en.medium
    const selectedSentences = sentences.slice(0, Math.min(count, sentences.length))

    res.json({
      success: true,
      sentences: selectedSentences,
      language,
      difficulty,
      count: selectedSentences.length
    })
  } catch (error) {
    console.error('Error getting practice sentences:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get practice sentences'
    })
  }
})

/**
 * @route GET /api/pronunciation/test
 * @desc Test pronunciation service functionality
 */
router.get('/test', async (req, res) => {
  try {
    const testSpoken = "Hello, this is a test of the pronunciation analysis system."
    const testReference = "Hello, this is a test of the pronunciation analysis system."
    
    const result = await PronunciationService.analyzePronunciation(
      testSpoken,
      testReference,
      'en',
      { duration: 5, wordsPerMinute: 120 }
    )
    
    res.json({
      success: true,
      message: 'Pronunciation service test completed',
      testData: {
        spokenText: testSpoken,
        referenceText: testReference
      },
      result
    })
  } catch (error) {
    console.error('Pronunciation service test error:', error)
    res.status(500).json({
      success: false,
      error: 'Pronunciation service test failed',
      details: error.message
    })
  }
})

export default router
