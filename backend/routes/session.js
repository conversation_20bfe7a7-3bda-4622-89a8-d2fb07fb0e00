import express from 'express'
import { body, validationResult } from 'express-validator'
import { sessionDb, transcriptDb, suggestionDb } from '../services/database.js'

const router = express.Router()

// Create new session
router.post('/create',
  [
    body('userName').notEmpty().withMessage('User name is required'),
    body('jobDescription').optional().isString(),
    body('language').optional().isIn(['en', 'fr', 'es', 'ar', 'de']).withMessage('Invalid language code')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() })
      }

      const { userName, jobDescription = '', language = 'en' } = req.body

      // Create new session
      const sessionId = sessionDb.create({
        user_name: userName,
        job_description: jobDescription,
        language
      })

      res.json({
        success: true,
        session: {
          id: sessionId,
          userName,
          jobDescription,
          language,
          startTime: new Date().toISOString()
        }
      })
    } catch (error) {
      console.error('Error creating session:', error)
      res.status(500).json({ 
        error: 'Failed to create session',
        message: error.message 
      })
    }
  }
)

// Get session by ID
router.get('/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params

    if (!sessionId || isNaN(parseInt(sessionId))) {
      return res.status(400).json({ error: 'Valid session ID is required' })
    }

    const session = sessionDb.getById(parseInt(sessionId))

    if (!session) {
      return res.status(404).json({ error: 'Session not found' })
    }

    // Get transcripts and suggestions for this session
    const transcripts = transcriptDb.getBySessionId(parseInt(sessionId))
    const suggestions = suggestionDb.getBySessionId(parseInt(sessionId))

    res.json({
      success: true,
      session: {
        ...session,
        transcripts,
        suggestions
      }
    })
  } catch (error) {
    console.error('Error fetching session:', error)
    res.status(500).json({ 
      error: 'Failed to fetch session',
      message: error.message 
    })
  }
})

// Update session
router.put('/:sessionId',
  [
    body('endTime').optional().isISO8601(),
    body('duration').optional().isInt(),
    body('transcript').optional().isString(),
    body('toneAnalysis').optional().isString()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() })
      }

      const { sessionId } = req.params
      const updateData = req.body

      if (!sessionId || isNaN(parseInt(sessionId))) {
        return res.status(400).json({ error: 'Valid session ID is required' })
      }

      // Check if session exists
      const session = await sessionDb.getById(parseInt(sessionId))
      if (!session) {
        return res.status(404).json({ error: 'Session not found' })
      }

      // Update session
      await sessionDb.update(parseInt(sessionId), updateData)

      res.json({
        success: true,
        message: 'Session updated successfully'
      })
    } catch (error) {
      console.error('Error updating session:', error)
      res.status(500).json({ 
        error: 'Failed to update session',
        message: error.message 
      })
    }
  }
)

// Get user's sessions
router.get('/user/:userName', async (req, res) => {
  try {
    const { userName } = req.params
    const { limit = 10 } = req.query

    if (!userName) {
      return res.status(400).json({ error: 'User name is required' })
    }

    const sessions = await sessionDb.getByUserName(userName, parseInt(limit))

    res.json({
      success: true,
      sessions: sessions.map(session => ({
        id: session.id,
        userName: session.user_name,
        jobDescription: session.job_description,
        language: session.language,
        startTime: session.start_time,
        endTime: session.end_time,
        duration: session.duration,
        createdAt: session.created_at
      }))
    })
  } catch (error) {
    console.error('Error fetching user sessions:', error)
    res.status(500).json({ 
      error: 'Failed to fetch sessions',
      message: error.message 
    })
  }
})

// Delete session
router.delete('/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params

    if (!sessionId || isNaN(parseInt(sessionId))) {
      return res.status(400).json({ error: 'Valid session ID is required' })
    }

    // Check if session exists
    const session = await sessionDb.getById(parseInt(sessionId))
    if (!session) {
      return res.status(404).json({ error: 'Session not found' })
    }

    // Delete related data first
    const transcripts = await transcriptDb.getBySessionId(parseInt(sessionId))
    for (const transcript of transcripts) {
      await transcriptDb.delete(transcript.id)
    }

    const suggestions = await suggestionDb.getBySessionId(parseInt(sessionId))
    for (const suggestion of suggestions) {
      await suggestionDb.delete(suggestion.id)
    }

    // Delete session
    await sessionDb.delete(parseInt(sessionId))

    res.json({
      success: true,
      message: 'Session deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting session:', error)
    res.status(500).json({ 
      error: 'Failed to delete session',
      message: error.message 
    })
  }
})

// Get session statistics
router.get('/:sessionId/stats', async (req, res) => {
  try {
    const { sessionId } = req.params

    if (!sessionId || isNaN(parseInt(sessionId))) {
      return res.status(400).json({ error: 'Valid session ID is required' })
    }

    const session = await sessionDb.getById(parseInt(sessionId))
    if (!session) {
      return res.status(404).json({ error: 'Session not found' })
    }

    const transcripts = await transcriptDb.getBySessionId(parseInt(sessionId))
    const suggestions = await suggestionDb.getBySessionId(parseInt(sessionId))

    // Calculate statistics
    const stats = calculateSessionStats(session, transcripts, suggestions)

    res.json({
      success: true,
      stats
    })
  } catch (error) {
    console.error('Error calculating session stats:', error)
    res.status(500).json({ 
      error: 'Failed to calculate session statistics',
      message: error.message 
    })
  }
})

// Export session data
router.get('/:sessionId/export', async (req, res) => {
  try {
    const { sessionId } = req.params
    const { format = 'json' } = req.query

    if (!sessionId || isNaN(parseInt(sessionId))) {
      return res.status(400).json({ error: 'Valid session ID is required' })
    }

    const session = await sessionDb.getById(parseInt(sessionId))
    if (!session) {
      return res.status(404).json({ error: 'Session not found' })
    }

    const transcripts = await transcriptDb.getBySessionId(parseInt(sessionId))
    const suggestions = await suggestionDb.getBySessionId(parseInt(sessionId))

    const exportData = {
      session: {
        id: session.id,
        userName: session.user_name,
        jobDescription: session.job_description,
        language: session.language,
        startTime: session.start_time,
        endTime: session.end_time,
        duration: session.duration
      },
      transcripts: transcripts.map(t => ({
        timestamp: t.timestamp,
        text: t.text,
        confidence: t.confidence,
        language: t.language
      })),
      suggestions: suggestions.map(s => ({
        timestamp: s.timestamp,
        suggestion: s.suggestion,
        context: s.context,
        confidence: s.confidence
      })),
      exportedAt: new Date().toISOString()
    }

    if (format === 'json') {
      res.json(exportData)
    } else if (format === 'txt') {
      const textContent = formatAsText(exportData)
      res.setHeader('Content-Type', 'text/plain')
      res.setHeader('Content-Disposition', `attachment; filename="session-${sessionId}.txt"`)
      res.send(textContent)
    } else {
      res.status(400).json({ error: 'Unsupported export format. Use json or txt.' })
    }
  } catch (error) {
    console.error('Error exporting session:', error)
    res.status(500).json({ 
      error: 'Failed to export session',
      message: error.message 
    })
  }
})

// Calculate session statistics
function calculateSessionStats(session, transcripts, suggestions) {
  const totalWords = transcripts.reduce((sum, t) => sum + t.text.split(' ').length, 0)
  const avgConfidence = transcripts.length > 0 
    ? transcripts.reduce((sum, t) => sum + (t.confidence || 0), 0) / transcripts.length 
    : 0

  const duration = session.duration || 0
  const wordsPerMinute = duration > 0 ? (totalWords / (duration / 60)) : 0

  return {
    sessionId: session.id,
    duration: duration,
    totalTranscripts: transcripts.length,
    totalSuggestions: suggestions.length,
    totalWords: totalWords,
    averageConfidence: Math.round(avgConfidence * 100) / 100,
    wordsPerMinute: Math.round(wordsPerMinute),
    language: session.language,
    startTime: session.start_time,
    endTime: session.end_time
  }
}

// Format export data as text
function formatAsText(data) {
  let text = `VocaPilot Session Export\n`
  text += `========================\n\n`
  text += `Session ID: ${data.session.id}\n`
  text += `User: ${data.session.userName}\n`
  text += `Language: ${data.session.language}\n`
  text += `Start Time: ${data.session.startTime}\n`
  text += `End Time: ${data.session.endTime || 'N/A'}\n`
  text += `Duration: ${data.session.duration || 0} seconds\n\n`

  if (data.session.jobDescription) {
    text += `Job Description:\n${data.session.jobDescription}\n\n`
  }

  text += `Transcript:\n`
  text += `-----------\n`
  data.transcripts.forEach((transcript, index) => {
    text += `[${transcript.timestamp}] ${transcript.text}\n`
  })

  text += `\nAI Suggestions:\n`
  text += `---------------\n`
  data.suggestions.forEach((suggestion, index) => {
    text += `[${suggestion.timestamp}] ${suggestion.suggestion}\n`
  })

  text += `\nExported at: ${data.exportedAt}\n`
  
  return text
}

export default router
