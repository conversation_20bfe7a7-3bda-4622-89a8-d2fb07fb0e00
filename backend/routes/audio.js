import express from 'express'
import multer from 'multer'
import { body, validationResult } from 'express-validator'
import { analyzeAudioTone, transcribeAudio } from '../services/audio.js'

const router = express.Router()

// Configure multer for audio file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true)
    } else {
      cb(new Error('Only audio files are allowed'), false)
    }
  }
})

// Transcribe audio file
router.post('/transcribe', 
  upload.single('audio'),
  [
    body('language').optional().isIn(['en', 'fr', 'es', 'ar', 'de']).withMessage('Invalid language code')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() })
      }

      if (!req.file) {
        return res.status(400).json({ error: 'No audio file provided' })
      }

      const { language = 'en' } = req.body
      const audioBuffer = req.file.buffer

      // Transcribe audio using Whisper service
      const transcription = await transcribeAudio(audioBuffer, language)

      res.json({
        success: true,
        transcription: {
          text: transcription.text,
          confidence: transcription.confidence,
          language: transcription.language,
          duration: transcription.duration
        }
      })
    } catch (error) {
      console.error('Error transcribing audio:', error)
      res.status(500).json({ 
        error: 'Failed to transcribe audio',
        message: error.message 
      })
    }
  }
)

// Analyze audio tone
router.post('/analyze-tone',
  upload.single('audio'),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No audio file provided' })
      }

      const audioBuffer = req.file.buffer
      const sampleRate = parseInt(req.body.sampleRate) || 16000

      // Analyze tone using audio processing
      const toneAnalysis = await analyzeAudioTone(audioBuffer, sampleRate)

      res.json({
        success: true,
        analysis: toneAnalysis
      })
    } catch (error) {
      console.error('Error analyzing audio tone:', error)
      res.status(500).json({ 
        error: 'Failed to analyze audio tone',
        message: error.message 
      })
    }
  }
)

// Get supported languages
router.get('/languages', (req, res) => {
  const supportedLanguages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
    { code: 'es', name: 'Spanish', flag: '🇪🇸' },
    { code: 'ar', name: 'Arabic', flag: '🇸🇦' },
    { code: 'de', name: 'German', flag: '🇩🇪' }
  ]

  res.json({
    success: true,
    languages: supportedLanguages
  })
})

// Test audio processing capabilities
router.get('/test', async (req, res) => {
  try {
    // Test Whisper service connection
    const whisperStatus = await testWhisperConnection()
    
    res.json({
      success: true,
      services: {
        whisper: whisperStatus,
        toneAnalysis: true // Always available as it's built-in
      },
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error testing audio services:', error)
    res.status(500).json({ 
      error: 'Audio services test failed',
      message: error.message 
    })
  }
})

// Test Whisper service connection
async function testWhisperConnection() {
  try {
    // This would test the actual Whisper service
    // For now, we'll return a mock response
    return {
      status: 'available',
      version: '1.0.0',
      models: ['base', 'small', 'medium']
    }
  } catch (error) {
    return {
      status: 'unavailable',
      error: error.message
    }
  }
}

export default router
