import express from 'express'
import { body, query, validationResult } from 'express-validator'
import AnalyticsService from '../services/analytics.js'

const router = express.Router()

/**
 * @route POST /api/analytics/record
 * @desc Record session analytics
 */
router.post('/record', [
  body('sessionId').isInt({ min: 1 }).withMessage('Valid session ID is required'),
  body('analytics').isObject().withMessage('Analytics data is required'),
  body('analytics.speakingTime').optional().isInt({ min: 0 }).withMessage('Speaking time must be non-negative'),
  body('analytics.wordCount').optional().isInt({ min: 0 }).withMessage('Word count must be non-negative'),
  body('analytics.fillerWordCount').optional().isInt({ min: 0 }).withMessage('Filler word count must be non-negative'),
  body('analytics.averageConfidence').optional().isFloat({ min: 0, max: 100 }).withMessage('Confidence must be between 0 and 100'),
  body('analytics.grammarScore').optional().isFloat({ min: 0, max: 100 }).withMessage('Grammar score must be between 0 and 100'),
  body('analytics.pronunciationScore').optional().isFloat({ min: 0, max: 100 }).withMessage('Pronunciation score must be between 0 and 100'),
  body('analytics.fluencyScore').optional().isFloat({ min: 0, max: 100 }).withMessage('Fluency score must be between 0 and 100'),
  body('analytics.sessionType').optional().isIn(['interview', 'practice', 'roleplay', 'pronunciation']).withMessage('Invalid session type')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { sessionId, analytics } = req.body
    const result = await AnalyticsService.recordSessionAnalytics(sessionId, analytics)
    
    res.json(result)
  } catch (error) {
    console.error('Analytics recording error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to record analytics'
    })
  }
})

/**
 * @route GET /api/analytics/progress/:userName
 * @desc Get user progress over time
 */
router.get('/progress/:userName', [
  query('timeframe').optional().isIn(['7d', '30d', '90d']).withMessage('Invalid timeframe')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { userName } = req.params
    const { timeframe = '30d' } = req.query

    const result = await AnalyticsService.getUserProgress(userName, timeframe)
    
    res.json(result)
  } catch (error) {
    console.error('Progress retrieval error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get user progress'
    })
  }
})

/**
 * @route GET /api/analytics/stats/:userName
 * @desc Get overall user statistics
 */
router.get('/stats/:userName', async (req, res) => {
  try {
    const { userName } = req.params
    const result = await AnalyticsService.getUserStats(userName)
    
    res.json(result)
  } catch (error) {
    console.error('Stats retrieval error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get user statistics'
    })
  }
})

/**
 * @route GET /api/analytics/weekly-summary/:userName
 * @desc Get weekly summary for user
 */
router.get('/weekly-summary/:userName', async (req, res) => {
  try {
    const { userName } = req.params
    const result = await AnalyticsService.getWeeklySummary(userName)
    
    res.json(result)
  } catch (error) {
    console.error('Weekly summary error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get weekly summary'
    })
  }
})

/**
 * @route GET /api/analytics/insights/:userName
 * @desc Get learning insights and recommendations
 */
router.get('/insights/:userName', async (req, res) => {
  try {
    const { userName } = req.params
    const result = await AnalyticsService.getLearningInsights(userName)
    
    res.json(result)
  } catch (error) {
    console.error('Insights retrieval error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get learning insights'
    })
  }
})

/**
 * @route POST /api/analytics/initialize
 * @desc Initialize analytics tables (admin endpoint)
 */
router.post('/initialize', async (req, res) => {
  try {
    const result = await AnalyticsService.initializeAnalyticsTables()
    
    res.json({
      success: true,
      message: 'Analytics tables initialized successfully',
      result
    })
  } catch (error) {
    console.error('Analytics initialization error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to initialize analytics tables'
    })
  }
})

/**
 * @route GET /api/analytics/dashboard/:userName
 * @desc Get comprehensive dashboard data
 */
router.get('/dashboard/:userName', async (req, res) => {
  try {
    const { userName } = req.params
    const { timeframe = '30d' } = req.query

    // Get all dashboard data in parallel
    const [stats, progress, weeklySummary, insights] = await Promise.all([
      AnalyticsService.getUserStats(userName),
      AnalyticsService.getUserProgress(userName, timeframe),
      AnalyticsService.getWeeklySummary(userName),
      AnalyticsService.getLearningInsights(userName)
    ])

    res.json({
      success: true,
      dashboard: {
        stats: stats.success ? stats.stats : null,
        progress: progress.success ? progress.progress : [],
        weeklySummary: weeklySummary.success ? weeklySummary.summary : null,
        insights: insights.success ? insights : null,
        timeframe
      },
      errors: {
        stats: !stats.success ? stats.error : null,
        progress: !progress.success ? progress.error : null,
        weeklySummary: !weeklySummary.success ? weeklySummary.error : null,
        insights: !insights.success ? insights.error : null
      }
    })
  } catch (error) {
    console.error('Dashboard data error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get dashboard data'
    })
  }
})

/**
 * @route GET /api/analytics/export/:userName
 * @desc Export user analytics data
 */
router.get('/export/:userName', [
  query('format').optional().isIn(['json', 'csv']).withMessage('Invalid export format'),
  query('timeframe').optional().isIn(['7d', '30d', '90d', 'all']).withMessage('Invalid timeframe')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const { userName } = req.params
    const { format = 'json', timeframe = '30d' } = req.query

    // Get comprehensive data
    const [stats, progress] = await Promise.all([
      AnalyticsService.getUserStats(userName),
      AnalyticsService.getUserProgress(userName, timeframe)
    ])

    const exportData = {
      userName,
      exportDate: new Date().toISOString(),
      timeframe,
      stats: stats.success ? stats.stats : null,
      progress: progress.success ? progress.progress : []
    }

    if (format === 'csv') {
      // Convert to CSV format
      let csv = 'Date,Sessions,Speaking Time,Confidence,Grammar,Pronunciation,Fluency,Words,Filler Rate\n'
      
      if (progress.success) {
        progress.progress.forEach(day => {
          csv += `${day.date},${day.sessionCount},${day.averageSpeakingTime},${day.averageConfidence},${day.grammarScore},${day.pronunciationScore},${day.fluencyScore},${day.totalWords},${day.fillerWordRate}\n`
        })
      }

      res.set({
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${userName}-analytics-${timeframe}.csv"`
      })
      res.send(csv)
    } else {
      res.set({
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="${userName}-analytics-${timeframe}.json"`
      })
      res.json(exportData)
    }
  } catch (error) {
    console.error('Analytics export error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to export analytics data'
    })
  }
})

/**
 * @route GET /api/analytics/test
 * @desc Test analytics service functionality
 */
router.get('/test', async (req, res) => {
  try {
    // Test analytics initialization
    const initResult = await AnalyticsService.initializeAnalyticsTables()
    
    res.json({
      success: true,
      message: 'Analytics service test completed',
      initialization: initResult,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Analytics service test error:', error)
    res.status(500).json({
      success: false,
      error: 'Analytics service test failed',
      details: error.message
    })
  }
})

export default router
