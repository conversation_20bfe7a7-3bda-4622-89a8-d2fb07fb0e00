import express from 'express'
import { body, validationResult } from 'express-validator'
import { generateAISuggestion, generateInterviewFeedback, initializeOllama } from '../services/ai.js'
import { sessionDb } from '../services/database.js'

const router = express.Router()

// Generate AI suggestion based on transcript
router.post('/suggest',
  [
    body('transcript').notEmpty().withMessage('Transcript is required'),
    body('sessionId').isInt().withMessage('Valid session ID is required'),
    body('context').optional().isString()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() })
      }

      const { transcript, sessionId, context } = req.body

      // Generate AI suggestion
      const suggestion = await generateAISuggestion(transcript, sessionId)

      if (!suggestion) {
        return res.status(500).json({ 
          error: 'Failed to generate suggestion',
          fallback: 'Take a moment to think about your response and provide specific examples.'
        })
      }

      res.json({
        success: true,
        suggestion: {
          text: suggestion.text,
          context: suggestion.context,
          confidence: suggestion.confidence,
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      console.error('Error generating AI suggestion:', error)
      res.status(500).json({ 
        error: 'Failed to generate suggestion',
        message: error.message 
      })
    }
  }
)

// Generate interview feedback for a session
router.post('/feedback/:sessionId',
  async (req, res) => {
    try {
      const { sessionId } = req.params

      if (!sessionId || isNaN(parseInt(sessionId))) {
        return res.status(400).json({ error: 'Valid session ID is required' })
      }

      // Check if session exists
      const session = await sessionDb.getById(parseInt(sessionId))
      if (!session) {
        return res.status(404).json({ error: 'Session not found' })
      }

      // Generate comprehensive feedback
      const feedback = await generateInterviewFeedback(parseInt(sessionId))

      res.json({
        success: true,
        feedback: {
          sessionId: parseInt(sessionId),
          overallScore: feedback.overall_score,
          feedback: feedback.feedback,
          suggestions: feedback.suggestions,
          strengths: feedback.strengths,
          areasForImprovement: feedback.areas_for_improvement,
          generatedAt: new Date().toISOString()
        }
      })
    } catch (error) {
      console.error('Error generating feedback:', error)
      res.status(500).json({ 
        error: 'Failed to generate feedback',
        message: error.message 
      })
    }
  }
)

// Get AI service status
router.get('/status', async (req, res) => {
  try {
    const ollamaStatus = await initializeOllama()
    
    res.json({
      success: true,
      services: {
        ollama: {
          status: 'available',
          models: ollamaStatus.models || [],
          version: ollamaStatus.version || 'unknown'
        }
      },
      capabilities: [
        'interview_suggestions',
        'feedback_generation',
        'multilingual_support',
        'context_awareness'
      ],
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error checking AI status:', error)
    res.json({
      success: true,
      services: {
        ollama: {
          status: 'unavailable',
          error: error.message
        }
      },
      capabilities: [
        'fallback_suggestions',
        'basic_feedback'
      ],
      timestamp: new Date().toISOString()
    })
  }
})

// Test AI suggestion generation
router.post('/test',
  [
    body('text').notEmpty().withMessage('Test text is required')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() })
      }

      const { text } = req.body

      // Create a mock session for testing
      const mockSessionId = await sessionDb.create({
        user_name: 'test_user',
        job_description: 'Software Engineer position requiring strong problem-solving skills',
        language: 'en'
      })

      // Generate test suggestion
      const suggestion = await generateAISuggestion(text, mockSessionId)

      // Clean up test session
      await sessionDb.delete(mockSessionId)

      res.json({
        success: true,
        test: {
          input: text,
          suggestion: suggestion,
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      console.error('Error testing AI suggestion:', error)
      res.status(500).json({ 
        error: 'AI test failed',
        message: error.message 
      })
    }
  }
)

// Get available AI models
router.get('/models', async (req, res) => {
  try {
    const ollamaStatus = await initializeOllama()
    
    const models = ollamaStatus.models || []
    const availableModels = models.map(model => ({
      name: model.name,
      size: model.size,
      modified: model.modified_at,
      capabilities: ['text_generation', 'conversation', 'interview_assistance']
    }))

    res.json({
      success: true,
      models: availableModels,
      defaultModel: 'llama3',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error fetching AI models:', error)
    res.status(500).json({ 
      error: 'Failed to fetch models',
      message: error.message 
    })
  }
})

// Generate contextual questions for practice
router.post('/practice-questions',
  [
    body('jobDescription').optional().isString(),
    body('difficulty').optional().isIn(['easy', 'medium', 'hard']),
    body('category').optional().isIn(['behavioral', 'technical', 'situational', 'general'])
  ],
  async (req, res) => {
    try {
      const { jobDescription, difficulty = 'medium', category = 'general' } = req.body

      // Generate practice questions based on parameters
      const questions = generatePracticeQuestions(jobDescription, difficulty, category)

      res.json({
        success: true,
        questions: questions.map((q, index) => ({
          id: index + 1,
          question: q.question,
          category: q.category,
          difficulty: q.difficulty,
          tips: q.tips
        })),
        parameters: {
          difficulty,
          category,
          hasJobDescription: !!jobDescription
        }
      })
    } catch (error) {
      console.error('Error generating practice questions:', error)
      res.status(500).json({ 
        error: 'Failed to generate practice questions',
        message: error.message 
      })
    }
  }
)

// Generate practice questions
function generatePracticeQuestions(jobDescription, difficulty, category) {
  const questionBank = {
    general: {
      easy: [
        {
          question: "Tell me about yourself.",
          tips: ["Structure: current role → experience → achievements → interest in position"]
        },
        {
          question: "Why are you interested in this position?",
          tips: ["Connect your goals with company mission", "Show research about the role"]
        }
      ],
      medium: [
        {
          question: "What is your greatest strength?",
          tips: ["Choose job-relevant strength", "Provide specific example", "Show impact"]
        },
        {
          question: "Describe a challenge you overcame.",
          tips: ["Use STAR method", "Focus on your actions", "Highlight learning"]
        }
      ],
      hard: [
        {
          question: "Where do you see yourself in 5 years?",
          tips: ["Align with company growth", "Show ambition but be realistic", "Emphasize learning"]
        }
      ]
    },
    behavioral: {
      easy: [
        {
          question: "Describe a time you worked in a team.",
          tips: ["Highlight collaboration", "Show your specific contribution", "Mention positive outcome"]
        }
      ],
      medium: [
        {
          question: "Tell me about a time you had to deal with a difficult colleague.",
          tips: ["Focus on resolution", "Show emotional intelligence", "Emphasize professionalism"]
        }
      ],
      hard: [
        {
          question: "Describe a time you failed and how you handled it.",
          tips: ["Be honest but brief", "Focus on learning", "Show growth mindset"]
        }
      ]
    },
    technical: {
      easy: [
        {
          question: "What programming languages are you most comfortable with?",
          tips: ["Be honest about proficiency", "Give examples of projects", "Mention learning approach"]
        }
      ],
      medium: [
        {
          question: "How do you approach debugging a complex problem?",
          tips: ["Describe systematic approach", "Mention tools and techniques", "Show problem-solving skills"]
        }
      ],
      hard: [
        {
          question: "Design a system to handle millions of users.",
          tips: ["Start with requirements", "Consider scalability", "Discuss trade-offs"]
        }
      ]
    }
  }

  const selectedQuestions = questionBank[category]?.[difficulty] || questionBank.general.medium
  
  return selectedQuestions.map(q => ({
    ...q,
    category,
    difficulty
  }))
}

export default router
