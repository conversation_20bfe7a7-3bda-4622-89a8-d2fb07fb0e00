import express from 'express'
import multer from 'multer'
import { body, validationResult } from 'express-validator'
// PDF parsing removed - will handle text files only for now
import { resumeDb } from '../services/database.js'
import { parseResumeContent } from '../services/resumeParser.js'

const router = express.Router()

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/plain') {
      cb(null, true)
    } else {
      cb(new Error('Only TXT files are supported currently'), false)
    }
  }
})

// Upload and parse resume
router.post('/upload',
  upload.single('resume'),
  [
    body('userName').notEmpty().withMessage('User name is required')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() })
      }

      if (!req.file) {
        return res.status(400).json({ error: 'No resume file provided' })
      }

      const { userName } = req.body
      const file = req.file

      let content = ''

      // Extract text based on file type
      if (file.mimetype === 'text/plain') {
        content = file.buffer.toString('utf-8')
      } else {
        return res.status(400).json({ error: 'Only text files are supported currently. PDF support will be added soon.' })
      }

      if (!content.trim()) {
        return res.status(400).json({ error: 'Could not extract text from resume' })
      }

      // Parse resume content
      const parsedData = await parseResumeContent(content)

      // Check if user already has a resume
      const existingResume = await resumeDb.getByUserName(userName)

      let resumeId
      if (existingResume) {
        // Update existing resume
        await resumeDb.update(existingResume.id, {
          content,
          parsed_data: parsedData
        })
        resumeId = existingResume.id
      } else {
        // Create new resume record
        resumeId = await resumeDb.create({
          user_name: userName,
          filename: file.originalname,
          content,
          parsed_data: parsedData
        })
      }

      res.json({
        success: true,
        resume: {
          id: resumeId,
          filename: file.originalname,
          size: file.size,
          parsedData,
          uploadedAt: new Date().toISOString()
        }
      })
    } catch (error) {
      console.error('Error uploading resume:', error)
      res.status(500).json({ 
        error: 'Failed to upload resume',
        message: error.message 
      })
    }
  }
)

// Get user's resume
router.get('/:userName', async (req, res) => {
  try {
    const { userName } = req.params

    if (!userName) {
      return res.status(400).json({ error: 'User name is required' })
    }

    const resume = await resumeDb.getByUserName(userName)

    if (!resume) {
      return res.status(404).json({ error: 'Resume not found' })
    }

    res.json({
      success: true,
      resume: {
        id: resume.id,
        filename: resume.filename,
        parsedData: resume.parsed_data,
        createdAt: resume.created_at,
        updatedAt: resume.updated_at
      }
    })
  } catch (error) {
    console.error('Error fetching resume:', error)
    res.status(500).json({ 
      error: 'Failed to fetch resume',
      message: error.message 
    })
  }
})

// Delete user's resume
router.delete('/:userName', async (req, res) => {
  try {
    const { userName } = req.params

    if (!userName) {
      return res.status(400).json({ error: 'User name is required' })
    }

    const resume = await resumeDb.getByUserName(userName)

    if (!resume) {
      return res.status(404).json({ error: 'Resume not found' })
    }

    await resumeDb.delete(resume.id)

    res.json({
      success: true,
      message: 'Resume deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting resume:', error)
    res.status(500).json({ 
      error: 'Failed to delete resume',
      message: error.message 
    })
  }
})

// Parse resume text (without uploading)
router.post('/parse',
  [
    body('content').notEmpty().withMessage('Resume content is required')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() })
      }

      const { content } = req.body

      // Parse resume content
      const parsedData = await parseResumeContent(content)

      res.json({
        success: true,
        parsedData
      })
    } catch (error) {
      console.error('Error parsing resume:', error)
      res.status(500).json({ 
        error: 'Failed to parse resume',
        message: error.message 
      })
    }
  }
)

// Get resume analysis and suggestions
router.get('/:userName/analysis', async (req, res) => {
  try {
    const { userName } = req.params

    const resume = await resumeDb.getByUserName(userName)

    if (!resume) {
      return res.status(404).json({ error: 'Resume not found' })
    }

    const analysis = analyzeResume(resume.parsed_data, resume.content)

    res.json({
      success: true,
      analysis
    })
  } catch (error) {
    console.error('Error analyzing resume:', error)
    res.status(500).json({ 
      error: 'Failed to analyze resume',
      message: error.message 
    })
  }
})

// Analyze resume for strengths and improvements
function analyzeResume(parsedData, content) {
  const analysis = {
    score: 7, // Base score
    strengths: [],
    improvements: [],
    suggestions: [],
    keywords: [],
    sections: {
      contact: false,
      summary: false,
      experience: false,
      education: false,
      skills: false
    }
  }

  // Check for essential sections
  if (parsedData.contact && Object.keys(parsedData.contact).length > 0) {
    analysis.sections.contact = true
    analysis.strengths.push('Complete contact information')
  } else {
    analysis.improvements.push('Add complete contact information')
  }

  if (parsedData.summary && parsedData.summary.length > 50) {
    analysis.sections.summary = true
    analysis.strengths.push('Professional summary included')
    analysis.score += 0.5
  } else {
    analysis.improvements.push('Add a professional summary')
    analysis.suggestions.push('Include a 2-3 sentence summary highlighting your key qualifications')
  }

  if (parsedData.experience && parsedData.experience.length > 0) {
    analysis.sections.experience = true
    analysis.strengths.push(`${parsedData.experience.length} work experience entries`)
    analysis.score += 1
    
    // Check for quantified achievements
    const hasNumbers = content.match(/\d+%|\$\d+|\d+\+/g)
    if (hasNumbers && hasNumbers.length > 2) {
      analysis.strengths.push('Quantified achievements')
      analysis.score += 0.5
    } else {
      analysis.improvements.push('Add quantified achievements')
      analysis.suggestions.push('Include numbers, percentages, or metrics to show your impact')
    }
  } else {
    analysis.improvements.push('Add work experience')
  }

  if (parsedData.education && parsedData.education.length > 0) {
    analysis.sections.education = true
    analysis.strengths.push('Education information included')
    analysis.score += 0.5
  }

  if (parsedData.skills && parsedData.skills.length > 0) {
    analysis.sections.skills = true
    analysis.strengths.push(`${parsedData.skills.length} skills listed`)
    analysis.keywords = parsedData.skills
    analysis.score += 0.5
    
    if (parsedData.skills.length > 10) {
      analysis.improvements.push('Consider reducing number of skills')
      analysis.suggestions.push('Focus on your most relevant and strongest skills (8-12 maximum)')
    }
  } else {
    analysis.improvements.push('Add relevant skills')
  }

  // Check resume length
  const wordCount = content.split(/\s+/).length
  if (wordCount > 800) {
    analysis.improvements.push('Resume may be too long')
    analysis.suggestions.push('Aim for 1-2 pages maximum, focus on most relevant information')
  } else if (wordCount < 200) {
    analysis.improvements.push('Resume may be too short')
    analysis.suggestions.push('Add more details about your experience and achievements')
  } else {
    analysis.strengths.push('Appropriate length')
  }

  // Final score adjustment
  analysis.score = Math.min(10, Math.max(1, analysis.score))

  return analysis
}

export default router
