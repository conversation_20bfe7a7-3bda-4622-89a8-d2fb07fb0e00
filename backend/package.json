{"name": "vocapilot-backend", "version": "1.0.0", "description": "VocaPilot Backend API", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"axios": "^1.5.0", "compromise": "^14.10.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "natural": "^6.5.0", "node-cron": "^3.0.2", "socket.io": "^4.7.2", "better-sqlite3": "^8.7.0", "ws": "^8.13.0", "languagetool-api": "^1.0.0", "node-fetch": "^3.3.2", "form-data": "^4.0.0"}, "devDependencies": {"jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "keywords": ["interview", "copilot", "ai", "real-time", "speech-to-text", "api"], "author": "VocaPilot Team", "license": "MIT"}