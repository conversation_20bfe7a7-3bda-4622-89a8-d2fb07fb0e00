# VocaPilot Backend Environment Configuration
# Copy this file to .env and update the values as needed

# Application Environment
NODE_ENV=development
PORT=5000
HOST=0.0.0.0

# Frontend Configuration
FRONTEND_URL=http://localhost:3000

# Database Configuration
DATABASE_PATH=./vocapilot.db
DATABASE_BACKUP_ENABLED=true
DATABASE_BACKUP_INTERVAL=86400000  # 24 hours in milliseconds

# AI Services
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=llama3
OLLAMA_TIMEOUT=30000  # 30 seconds
WHISPER_SERVICE_URL=http://localhost:8000
WHISPER_TIMEOUT=30000  # 30 seconds

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
SESSION_SECRET=your-session-secret-key
CORS_ORIGIN=http://localhost:3000
HELMET_ENABLED=true

# File Upload
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=./uploads
ALLOWED_FILE_TYPES=application/pdf,text/plain

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_MESSAGE="Too many requests from this IP, please try again later."

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined
LOG_FILE=./logs/vocapilot.log
LOG_MAX_SIZE=10485760  # 10MB
LOG_MAX_FILES=5

# Performance
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6
KEEP_ALIVE_TIMEOUT=5000
HEADERS_TIMEOUT=60000

# Features
ANALYTICS_ENABLED=false
DEBUG_MODE=false
OFFLINE_MODE=true
WEBSOCKET_ENABLED=true

# Session Management
SESSION_CLEANUP_INTERVAL=3600000  # 1 hour
SESSION_MAX_AGE=86400000  # 24 hours
MAX_SESSIONS_PER_USER=10

# Audio Processing
AUDIO_CHUNK_SIZE=1024
AUDIO_SAMPLE_RATE=16000
AUDIO_MAX_DURATION=3600  # 1 hour in seconds

# AI Configuration
AI_RESPONSE_MAX_LENGTH=500
AI_CONFIDENCE_THRESHOLD=0.7
AI_CONTEXT_WINDOW=2048
AI_TEMPERATURE=0.7

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=false
PROMETHEUS_PORT=9090

# External Services (Optional)
SENTRY_DSN=
REDIS_URL=
ELASTICSEARCH_URL=
