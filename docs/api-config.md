# 🔧 VocaPilot API Configuration

## Overview

VocaPilot integrates multiple AI services and APIs to provide comprehensive speech analysis and feedback. This document outlines all API configurations, endpoints, fallback strategies, and integration details.

## Service Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │───►│   Backend       │───►│  AI Services    │
│   (React)       │    │   (Node.js)     │    │  (Python/API)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ WebSocket       │    │ REST APIs       │    │ External APIs   │
│ Real-time       │    │ HTTP/HTTPS      │    │ Third-party     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Core Services

### 1. Whisper Speech-to-Text Service

**Service URL**: `http://localhost:8001`
**Technology**: Python FastAPI + Whisper.cpp
**Purpose**: Real-time audio transcription

#### Configuration
```python
# whisper-service/app.py
WHISPER_CONFIG = {
    'model_size': 'base',  # tiny, base, small, medium, large
    'language': 'auto',    # Auto-detect or specify
    'task': 'transcribe',  # transcribe or translate
    'temperature': 0.0,    # Sampling temperature
    'best_of': 1,         # Number of candidates
    'beam_size': 5,       # Beam search size
    'patience': 1.0,      # Beam search patience
    'length_penalty': 1.0, # Length penalty
    'suppress_tokens': [-1], # Tokens to suppress
    'initial_prompt': None,  # Initial prompt
    'condition_on_previous_text': True,
    'fp16': True,         # Use FP16 precision
    'compression_ratio_threshold': 2.4,
    'logprob_threshold': -1.0,
    'no_speech_threshold': 0.6
}
```

#### API Endpoints
```javascript
// Transcription endpoint
POST /transcribe
Content-Type: multipart/form-data
Body: {
  audio: File,
  language?: string,
  task?: 'transcribe' | 'translate'
}

// Health check
GET /health
Response: {
  status: 'healthy' | 'unhealthy',
  model_loaded: boolean,
  gpu_available: boolean
}

// Supported languages
GET /languages
Response: {
  languages: string[],
  auto_detect: boolean
}
```

#### Fallback Strategy
```javascript
// Vosk fallback implementation
const transcribeAudio = async (audioBlob) => {
  try {
    // Primary: Whisper service
    const result = await whisperTranscribe(audioBlob)
    return result
  } catch (error) {
    console.warn('Whisper unavailable, falling back to Vosk')
    // Fallback: Vosk (client-side)
    return await voskTranscribe(audioBlob)
  }
}
```

### 2. Coqui TTS Service

**Service URL**: `http://localhost:8002`
**Technology**: Python FastAPI + Coqui TTS
**Purpose**: Text-to-speech synthesis

#### Configuration
```python
# tts-service/app.py
TTS_CONFIG = {
    'models': {
        'en': {
            'female': 'tts_models/en/ljspeech/tacotron2-DDC',
            'male': 'tts_models/en/ljspeech/glow-tts'
        },
        'fr': {
            'female': 'tts_models/fr/mai/tacotron2-DDC',
            'male': 'tts_models/fr/mai/glow-tts'
        },
        'es': {
            'female': 'tts_models/es/mai/tacotron2-DDC',
            'male': 'tts_models/es/mai/glow-tts'
        }
    },
    'device': 'cuda' if torch.cuda.is_available() else 'cpu',
    'sample_rate': 22050,
    'audio_format': 'wav'
}
```

#### API Endpoints
```javascript
// Speech synthesis
POST /synthesize
Content-Type: application/x-www-form-urlencoded
Body: {
  text: string,
  language: 'en' | 'fr' | 'es' | 'de',
  voice_type: 'female' | 'male',
  speed: number (0.5-2.0)
}
Response: audio/wav

// AI response synthesis (with text cleaning)
POST /synthesize-stream
Body: {
  text: string,
  language: string,
  voice_type: string
}

// Available voices
GET /voices
Response: {
  supported_voices: object,
  current_device: string
}
```

### 3. LanguageTool Grammar Service

**Service URL**: `https://api.languagetool.org/v2` (External)
**Technology**: REST API
**Purpose**: Grammar and style checking

#### Configuration
```javascript
// backend/services/grammar.js
const LANGUAGETOOL_CONFIG = {
  url: process.env.LANGUAGETOOL_URL || 'https://api.languagetool.org/v2',
  timeout: 5000,
  maxTextLength: 20000,
  enabledRules: [],
  disabledRules: ['WHITESPACE_RULE'],
  enabledCategories: ['GRAMMAR', 'TYPOS', 'STYLE'],
  disabledCategories: []
}
```

#### API Integration
```javascript
// Grammar analysis
const analyzeGrammar = async (text, language) => {
  try {
    const response = await axios.post(`${LANGUAGETOOL_URL}/check`, {
      text: text,
      language: language,
      enabledOnly: false
    }, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      timeout: 5000
    })
    
    return processGrammarResults(response.data)
  } catch (error) {
    // Fallback to basic analysis
    return basicGrammarAnalysis(text)
  }
}
```

#### Fallback Strategy
```javascript
// Basic grammar analysis when LanguageTool is unavailable
const basicGrammarAnalysis = (text) => {
  return {
    errors: [],
    totalErrors: 0,
    fallback: true,
    basicMetrics: {
      wordCount: text.split(/\s+/).length,
      sentenceCount: text.split(/[.!?]+/).length,
      readabilityScore: calculateBasicReadability(text)
    }
  }
}
```

### 4. Ollama LLM Service

**Service URL**: `http://localhost:11434`
**Technology**: Ollama (Local LLM)
**Purpose**: AI response generation

#### Configuration
```javascript
// backend/services/ai.js
const OLLAMA_CONFIG = {
  baseURL: process.env.OLLAMA_URL || 'http://localhost:11434',
  model: 'llama3:8b',
  temperature: 0.7,
  max_tokens: 500,
  top_p: 0.9,
  frequency_penalty: 0.0,
  presence_penalty: 0.0,
  timeout: 30000
}
```

#### API Integration
```javascript
// Generate AI response
const generateResponse = async (prompt, context) => {
  try {
    const response = await axios.post(`${OLLAMA_URL}/api/generate`, {
      model: OLLAMA_CONFIG.model,
      prompt: buildPrompt(prompt, context),
      stream: false,
      options: {
        temperature: OLLAMA_CONFIG.temperature,
        top_p: OLLAMA_CONFIG.top_p,
        max_tokens: OLLAMA_CONFIG.max_tokens
      }
    }, {
      timeout: OLLAMA_CONFIG.timeout
    })
    
    return response.data.response
  } catch (error) {
    throw new Error(`LLM generation failed: ${error.message}`)
  }
}
```

## Backend API Endpoints

### Grammar Analysis Routes

```javascript
// Grammar analysis
POST /api/grammar/analyze
Body: { text: string, language?: string }
Response: {
  success: boolean,
  errors: GrammarError[],
  totalErrors: number,
  errorsByCategory: object
}

// Speech pattern analysis
POST /api/grammar/speech-patterns
Body: { text: string, language?: string }
Response: {
  success: boolean,
  metrics: SpeechMetrics,
  fillerWords: FillerWord[],
  feedback: Feedback[],
  suggestions: Suggestion[]
}

// Confidence scoring
POST /api/grammar/confidence-score
Body: { text: string, fillerCount?: number }
Response: {
  success: boolean,
  confidenceScore: number,
  fillerCount: number
}
```

### TTS Routes

```javascript
// Text synthesis
POST /api/tts/synthesize
Body: { text: string, language?: string, voiceType?: string, speed?: number }
Response: audio/wav

// AI response synthesis
POST /api/tts/ai-response
Body: { text: string, language?: string, voiceType?: string }
Response: audio/wav

// Service health
GET /api/tts/health
Response: {
  success: boolean,
  available: boolean,
  service: string
}
```

### Pronunciation Routes

```javascript
// Pronunciation analysis
POST /api/pronunciation/analyze
Body: {
  spokenText: string,
  referenceText: string,
  language?: string,
  audioMetrics?: object
}
Response: {
  success: boolean,
  analysis: PronunciationAnalysis
}

// Practice sentences
GET /api/pronunciation/practice-sentences
Query: { language?: string, difficulty?: string, count?: number }
Response: {
  success: boolean,
  sentences: string[]
}
```

### Analytics Routes

```javascript
// Record session analytics
POST /api/analytics/record
Body: { sessionId: number, analytics: SessionAnalytics }
Response: { success: boolean, analyticsId: number }

// User progress
GET /api/analytics/progress/:userName
Query: { timeframe?: '7d' | '30d' | '90d' }
Response: {
  success: boolean,
  progress: ProgressData[]
}

// Dashboard data
GET /api/analytics/dashboard/:userName
Query: { timeframe?: string }
Response: {
  success: boolean,
  dashboard: DashboardData
}
```

## Environment Configuration

### Backend Environment Variables

```bash
# .env file
NODE_ENV=development
PORT=5000

# AI Services
OLLAMA_URL=http://localhost:11434
WHISPER_SERVICE_URL=http://localhost:8001
TTS_SERVICE_URL=http://localhost:8002

# External APIs
LANGUAGETOOL_URL=https://api.languagetool.org/v2

# Database
DATABASE_PATH=./data/vocapilot.db

# Security
JWT_SECRET=your-jwt-secret-here
CORS_ORIGIN=http://localhost:3000
```

### Frontend Environment Variables

```bash
# .env file
REACT_APP_API_URL=http://localhost:5000
REACT_APP_WS_URL=ws://localhost:5000
REACT_APP_ENVIRONMENT=development
```

## Error Handling & Fallbacks

### Service Availability Checks

```javascript
// Health check implementation
const checkServiceHealth = async (serviceUrl) => {
  try {
    const response = await axios.get(`${serviceUrl}/health`, { timeout: 3000 })
    return response.data.status === 'healthy'
  } catch (error) {
    return false
  }
}

// Service fallback chain
const getTranscription = async (audio) => {
  const services = [
    { name: 'whisper', url: WHISPER_SERVICE_URL, handler: whisperTranscribe },
    { name: 'vosk', url: null, handler: voskTranscribe },
    { name: 'basic', url: null, handler: basicTranscribe }
  ]
  
  for (const service of services) {
    try {
      if (service.url && !(await checkServiceHealth(service.url))) {
        continue
      }
      return await service.handler(audio)
    } catch (error) {
      console.warn(`${service.name} failed, trying next service`)
    }
  }
  
  throw new Error('All transcription services failed')
}
```

### Rate Limiting

```javascript
// Express rate limiting
import rateLimit from 'express-rate-limit'

const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
})

const strictLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 20, // Stricter limit for resource-intensive endpoints
  message: 'Rate limit exceeded for this endpoint'
})

// Apply to routes
app.use('/api/', apiLimiter)
app.use('/api/tts/', strictLimiter)
app.use('/api/pronunciation/', strictLimiter)
```

## Performance Optimization

### Caching Strategy

```javascript
// Redis caching for expensive operations
import Redis from 'redis'

const redis = Redis.createClient()

const cacheGrammarAnalysis = async (text, language) => {
  const cacheKey = `grammar:${language}:${hashText(text)}`
  
  // Check cache first
  const cached = await redis.get(cacheKey)
  if (cached) {
    return JSON.parse(cached)
  }
  
  // Perform analysis
  const result = await analyzeGrammar(text, language)
  
  // Cache result for 1 hour
  await redis.setex(cacheKey, 3600, JSON.stringify(result))
  
  return result
}
```

### Request Optimization

```javascript
// Batch processing for multiple requests
const batchAnalyze = async (texts, language) => {
  const batchSize = 5
  const results = []
  
  for (let i = 0; i < texts.length; i += batchSize) {
    const batch = texts.slice(i, i + batchSize)
    const batchResults = await Promise.all(
      batch.map(text => analyzeGrammar(text, language))
    )
    results.push(...batchResults)
  }
  
  return results
}
```

---

This API configuration ensures robust, scalable, and fault-tolerant integration of all VocaPilot services with comprehensive fallback strategies and performance optimizations.
