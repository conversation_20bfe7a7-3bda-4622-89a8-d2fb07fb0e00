# 🛠️ VocaPilot Setup Guide

This guide will help you set up VocaPilot on your local machine or deploy it to production.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: 5GB free space
- **Internet**: Required for initial setup and model downloads

### Software Dependencies
- **Node.js**: Version 18.0 or higher
- **Docker**: Latest version with Docker Compose
- **Git**: For cloning the repository
- **Python**: 3.8+ (for Whisper service)

## 🚀 Installation Methods

### Method 1: Docker (Recommended)

This is the easiest way to get VocaPilot running with all dependencies.

```bash
# Clone the repository
git clone https://github.com/vocapilot/vocapilot.git
cd vocapilot

# Start all services with Docker Compose
docker-compose up --build

# Wait for all services to start (first run takes 5-10 minutes)
# VocaPilot will be available at http://localhost:3000
```

**What this does:**
- Builds and starts all services (Frontend, Backend, Whisper, <PERSON>llama)
- Downloads required AI models automatically
- Sets up the database and networking
- Configures all environment variables

### Method 2: Manual Setup (Development)

For developers who want to run services individually or customize the setup.

#### Step 1: Clone and Install Dependencies

```bash
# Clone repository
git clone https://github.com/vocapilot/vocapilot.git
cd vocapilot

# Install root dependencies
npm install

# Install frontend dependencies
cd frontend && npm install && cd ..

# Install backend dependencies
cd backend && npm install && cd ..

# Install Whisper service dependencies
cd whisper-service && pip install -r requirements.txt && cd ..
```

#### Step 2: Setup Ollama (AI Service)

```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama service
ollama serve

# In a new terminal, pull the Llama 3 model
ollama pull llama3

# Verify installation
ollama list
```

#### Step 3: Setup Whisper Service

```bash
# Navigate to whisper service
cd whisper-service

# Install Python dependencies
pip install -r requirements.txt

# Download Whisper model (this may take a few minutes)
python -c "import whisper; whisper.load_model('base')"

# Start the service
python app.py
```

#### Step 4: Start Backend

```bash
# Navigate to backend
cd backend

# Create environment file
cp .env.example .env

# Start the backend server
npm run dev
```

#### Step 5: Start Frontend

```bash
# Navigate to frontend
cd frontend

# Create environment file
cp .env.example .env

# Start the development server
npm run dev
```

## 🔧 Configuration

### Environment Variables

#### Frontend (.env)
```env
VITE_API_URL=http://localhost:5000
VITE_WS_URL=http://localhost:5000
VITE_APP_NAME=VocaPilot
VITE_APP_VERSION=1.0.0
```

#### Backend (.env)
```env
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:3000

# AI Services
OLLAMA_URL=http://localhost:11434
WHISPER_SERVICE_URL=http://localhost:8000

# Database
DATABASE_PATH=./vocapilot.db

# Security
JWT_SECRET=your-secret-key-here
CORS_ORIGIN=http://localhost:3000
```

#### Whisper Service (.env)
```env
HOST=0.0.0.0
PORT=8000
MODEL_SIZE=base
DEVICE=auto
```

### Model Configuration

#### Whisper Models
Choose based on your hardware and accuracy needs:

| Model | Size | VRAM | Speed | Accuracy |
|-------|------|------|-------|----------|
| tiny | 39 MB | ~1 GB | Fastest | Basic |
| base | 74 MB | ~1 GB | Fast | Good |
| small | 244 MB | ~2 GB | Medium | Better |
| medium | 769 MB | ~5 GB | Slow | Great |
| large | 1550 MB | ~10 GB | Slowest | Best |

To change the model, edit `whisper-service/app.py`:
```python
# Change this line
whisper_model = whisper.load_model("base")  # Change "base" to your preferred model
```

#### Ollama Models
Available models for AI responses:

```bash
# Lightweight models (4GB+ RAM)
ollama pull llama3:8b
ollama pull mistral:7b

# Larger models (16GB+ RAM)
ollama pull llama3:70b
ollama pull codellama:34b

# List installed models
ollama list
```

## 🌐 Production Deployment

### Docker Production Setup

```bash
# Clone repository
git clone https://github.com/vocapilot/vocapilot.git
cd vocapilot

# Copy production environment files
cp .env.production .env
cp frontend/.env.production frontend/.env
cp backend/.env.production backend/.env

# Build and start production services
docker-compose -f docker-compose.prod.yml up --build -d

# Check service status
docker-compose ps
```

### Manual Production Setup

#### 1. Server Requirements
- **CPU**: 4+ cores
- **RAM**: 8GB+ (16GB recommended)
- **Storage**: 50GB+ SSD
- **Network**: Stable internet connection

#### 2. Install Dependencies
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 3. Configure Reverse Proxy (Nginx)
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    location /api/ {
        proxy_pass http://localhost:5000/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    location /socket.io/ {
        proxy_pass http://localhost:5000/socket.io/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 4. SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🧪 Testing Installation

### Health Checks
```bash
# Check all services
curl http://localhost:3000  # Frontend
curl http://localhost:5000/health  # Backend
curl http://localhost:8000/health  # Whisper
curl http://localhost:11434/api/tags  # Ollama

# Test audio transcription
curl -X POST -F "audio=@test-audio.wav" http://localhost:8000/transcribe

# Test AI generation
curl -X POST -H "Content-Type: application/json" \
  -d '{"text":"Tell me about yourself"}' \
  http://localhost:5000/api/ai/test
```

### Browser Compatibility
- **Chrome**: 90+ (Recommended)
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

**Required Browser Features:**
- WebRTC (for microphone access)
- WebSockets (for real-time communication)
- ES6+ JavaScript support

## 🔍 Troubleshooting

### Common Issues

#### 1. Microphone Permission Denied
```bash
# Check browser permissions
# Chrome: chrome://settings/content/microphone
# Firefox: about:preferences#privacy

# For HTTPS requirement in production
# Ensure SSL certificate is properly configured
```

#### 2. Whisper Model Download Fails
```bash
# Manual download
cd whisper-service
python -c "
import whisper
import os
os.makedirs('models', exist_ok=True)
model = whisper.load_model('base', download_root='./models')
print('Model downloaded successfully')
"
```

#### 3. Ollama Connection Issues
```bash
# Check Ollama status
ollama list

# Restart Ollama
sudo systemctl restart ollama

# Check logs
journalctl -u ollama -f
```

#### 4. Database Issues
```bash
# Reset database
rm backend/vocapilot.db

# Restart backend to recreate
cd backend && npm restart
```

#### 5. Port Conflicts
```bash
# Check what's using ports
sudo lsof -i :3000  # Frontend
sudo lsof -i :5000  # Backend
sudo lsof -i :8000  # Whisper
sudo lsof -i :11434 # Ollama

# Kill processes if needed
sudo kill -9 <PID>
```

### Performance Optimization

#### 1. Whisper Performance
```python
# In whisper-service/app.py
# Use GPU if available
device = "cuda" if torch.cuda.is_available() else "cpu"

# Optimize for speed
transcribe_options = {
    "fp16": torch.cuda.is_available(),
    "language": "en",  # Skip auto-detection
    "task": "transcribe"
}
```

#### 2. Memory Usage
```bash
# Monitor memory usage
docker stats

# Limit container memory
docker-compose.yml:
  services:
    whisper:
      mem_limit: 2g
    ollama:
      mem_limit: 8g
```

## 📞 Getting Help

If you encounter issues:

1. **Check the logs**:
   ```bash
   docker-compose logs -f  # All services
   docker-compose logs frontend  # Specific service
   ```

2. **Search existing issues**: [GitHub Issues](https://github.com/vocapilot/vocapilot/issues)

3. **Create a new issue** with:
   - Operating system and version
   - Node.js and Docker versions
   - Complete error messages
   - Steps to reproduce

4. **Join our community**: [Discord](https://discord.gg/vocapilot)

## ✅ Next Steps

Once VocaPilot is running:

1. **Complete the setup wizard** at http://localhost:3000/setup
2. **Upload your resume** and configure your profile
3. **Test the microphone** and audio permissions
4. **Start your first practice session**
5. **Explore the features** and provide feedback

Happy interviewing! 🎉
