{"name": "vocapilot", "version": "1.0.0", "description": "Your voice. Your guide. In real time. - A free, open-source, real-time AI interview & meeting copilot", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "cd frontend && npm run build", "start": "cd backend && npm start", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test"}, "keywords": ["interview", "copilot", "ai", "real-time", "speech-to-text", "whisper", "llm", "open-source"], "author": "VocaPilot Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0"}, "dependencies": {"axios": "^1.11.0", "socket.io-client": "^4.8.1"}}