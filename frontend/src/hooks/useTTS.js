import { useState, useRef, useCallback } from 'react'
import axios from 'axios'

/**
 * Text-to-Speech Hook
 * Provides TTS functionality for AI responses and pronunciation training
 */
export const useTTS = () => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const [supportedVoices, setSupportedVoices] = useState({})
  
  const audioRef = useRef(null)
  const audioContextRef = useRef(null)

  /**
   * Initialize TTS service
   */
  const initializeTTS = useCallback(async () => {
    try {
      const response = await axios.get('/api/tts/voices')
      if (response.data.success) {
        setSupportedVoices(response.data.voices)
      }
    } catch (error) {
      console.error('Failed to initialize TTS:', error)
      setError('TTS service not available')
    }
  }, [])

  /**
   * Synthesize and play text
   */
  const speak = useCallback(async (text, options = {}) => {
    if (!text || text.trim().length === 0) {
      setError('No text to speak')
      return false
    }

    setIsLoading(true)
    setError(null)

    try {
      const {
        language = 'en',
        voiceType = 'female',
        speed = 1.0,
        autoPlay = true
      } = options

      const response = await axios.post('/api/tts/synthesize', {
        text: text.trim(),
        language,
        voiceType,
        speed
      }, {
        responseType: 'arraybuffer',
        timeout: 30000
      })

      // Create audio blob
      const audioBlob = new Blob([response.data], { type: 'audio/wav' })
      const audioUrl = URL.createObjectURL(audioBlob)

      // Create and configure audio element
      if (audioRef.current) {
        audioRef.current.pause()
        URL.revokeObjectURL(audioRef.current.src)
      }

      audioRef.current = new Audio(audioUrl)
      
      // Set up event listeners
      audioRef.current.onloadstart = () => setIsLoading(true)
      audioRef.current.oncanplay = () => setIsLoading(false)
      audioRef.current.onplay = () => setIsPlaying(true)
      audioRef.current.onpause = () => setIsPlaying(false)
      audioRef.current.onended = () => {
        setIsPlaying(false)
        URL.revokeObjectURL(audioUrl)
      }
      audioRef.current.onerror = (e) => {
        setError('Audio playback failed')
        setIsPlaying(false)
        setIsLoading(false)
        URL.revokeObjectURL(audioUrl)
      }

      if (autoPlay) {
        await audioRef.current.play()
      }

      return true
    } catch (error) {
      console.error('TTS synthesis failed:', error)
      setError(error.response?.data?.error || 'Speech synthesis failed')
      return false
    } finally {
      setIsLoading(false)
    }
  }, [])

  /**
   * Speak AI response with special formatting
   */
  const speakAIResponse = useCallback(async (responseText, language = 'en', voiceType = 'female') => {
    try {
      const response = await axios.post('/api/tts/ai-response', {
        text: responseText,
        language,
        voiceType
      }, {
        responseType: 'arraybuffer',
        timeout: 30000
      })

      const audioBlob = new Blob([response.data], { type: 'audio/wav' })
      const audioUrl = URL.createObjectURL(audioBlob)

      if (audioRef.current) {
        audioRef.current.pause()
        URL.revokeObjectURL(audioRef.current.src)
      }

      audioRef.current = new Audio(audioUrl)
      
      audioRef.current.onplay = () => setIsPlaying(true)
      audioRef.current.onpause = () => setIsPlaying(false)
      audioRef.current.onended = () => {
        setIsPlaying(false)
        URL.revokeObjectURL(audioUrl)
      }

      await audioRef.current.play()
      return true
    } catch (error) {
      console.error('AI response TTS failed:', error)
      setError('Failed to speak AI response')
      return false
    }
  }, [])

  /**
   * Stop current playback
   */
  const stop = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current.currentTime = 0
      setIsPlaying(false)
    }
  }, [])

  /**
   * Pause current playback
   */
  const pause = useCallback(() => {
    if (audioRef.current && !audioRef.current.paused) {
      audioRef.current.pause()
    }
  }, [])

  /**
   * Resume playback
   */
  const resume = useCallback(async () => {
    if (audioRef.current && audioRef.current.paused) {
      try {
        await audioRef.current.play()
      } catch (error) {
        console.error('Resume playback failed:', error)
        setError('Failed to resume playback')
      }
    }
  }, [])

  /**
   * Check if TTS service is available
   */
  const checkAvailability = useCallback(async () => {
    try {
      const response = await axios.get('/api/tts/health')
      return response.data.available
    } catch (error) {
      return false
    }
  }, [])

  /**
   * Estimate speech duration
   */
  const estimateDuration = useCallback(async (text, wordsPerMinute = 150) => {
    try {
      const response = await axios.post('/api/tts/estimate-duration', {
        text,
        wordsPerMinute
      })
      return response.data.estimatedDuration
    } catch (error) {
      console.error('Duration estimation failed:', error)
      return 0
    }
  }, [])

  /**
   * Split long text for TTS
   */
  const splitText = useCallback(async (text, maxLength = 500) => {
    try {
      const response = await axios.post('/api/tts/split-text', {
        text,
        maxLength
      })
      return response.data.chunks
    } catch (error) {
      console.error('Text splitting failed:', error)
      return [text]
    }
  }, [])

  /**
   * Speak multiple chunks sequentially
   */
  const speakChunks = useCallback(async (chunks, options = {}) => {
    for (let i = 0; i < chunks.length; i++) {
      const success = await speak(chunks[i], { ...options, autoPlay: true })
      if (!success) {
        return false
      }
      
      // Wait for current chunk to finish before starting next
      if (audioRef.current && i < chunks.length - 1) {
        await new Promise(resolve => {
          audioRef.current.onended = resolve
        })
      }
    }
    return true
  }, [speak])

  /**
   * Get current playback position
   */
  const getCurrentTime = useCallback(() => {
    return audioRef.current ? audioRef.current.currentTime : 0
  }, [])

  /**
   * Get total duration
   */
  const getDuration = useCallback(() => {
    return audioRef.current ? audioRef.current.duration : 0
  }, [])

  /**
   * Set playback position
   */
  const setCurrentTime = useCallback((time) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time
    }
  }, [])

  /**
   * Set volume (0-1)
   */
  const setVolume = useCallback((volume) => {
    if (audioRef.current) {
      audioRef.current.volume = Math.max(0, Math.min(1, volume))
    }
  }, [])

  /**
   * Cleanup
   */
  const cleanup = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause()
      if (audioRef.current.src) {
        URL.revokeObjectURL(audioRef.current.src)
      }
      audioRef.current = null
    }
    setIsPlaying(false)
    setIsLoading(false)
    setError(null)
  }, [])

  return {
    // State
    isPlaying,
    isLoading,
    error,
    supportedVoices,
    
    // Core functions
    speak,
    speakAIResponse,
    stop,
    pause,
    resume,
    
    // Utility functions
    initializeTTS,
    checkAvailability,
    estimateDuration,
    splitText,
    speakChunks,
    
    // Playback controls
    getCurrentTime,
    getDuration,
    setCurrentTime,
    setVolume,
    
    // Cleanup
    cleanup
  }
}
