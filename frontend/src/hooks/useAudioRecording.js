import { useState, useRef, useCallback, useEffect } from 'react'
import { useAppStore } from '../store/appStore'

export const useAudioRecording = () => {
  const [audioPermission, setAudioPermission] = useState(null)
  const [audioStream, setAudioStream] = useState(null)
  const [audioLevel, setAudioLevel] = useState(0)
  const [isRecording, setIsRecording] = useState(false)

  const mediaRecorderRef = useRef(null)
  const audioContextRef = useRef(null)
  const analyserRef = useRef(null)
  const animationFrameRef = useRef(null)
  const chunksRef = useRef([])

  const { selectedLanguage, setTranscript, currentSession } = useAppStore()

  // Initialize audio context and analyzer
  const initializeAudioAnalysis = useCallback((stream) => {
    try {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)()
      analyserRef.current = audioContextRef.current.createAnalyser()
      
      const source = audioContextRef.current.createMediaStreamSource(stream)
      source.connect(analyserRef.current)
      
      analyserRef.current.fftSize = 256
      const bufferLength = analyserRef.current.frequencyBinCount
      const dataArray = new Uint8Array(bufferLength)
      
      const updateAudioLevel = () => {
        if (analyserRef.current && isRecording) {
          analyserRef.current.getByteFrequencyData(dataArray)
          
          // Calculate average volume
          const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength
          const normalizedLevel = (average / 255) * 100
          
          setAudioLevel(normalizedLevel)
          animationFrameRef.current = requestAnimationFrame(updateAudioLevel)
        }
      }
      
      updateAudioLevel()
    } catch (error) {
      console.error('Error initializing audio analysis:', error)
    }
  }, [isRecording])

  // Request microphone permission
  const requestPermission = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000
        } 
      })
      
      setAudioPermission('granted')
      setAudioStream(stream)
      return stream
    } catch (error) {
      console.error('Error requesting audio permission:', error)
      setAudioPermission('denied')
      throw error
    }
  }, [])

  // Start recording
  const startRecording = useCallback(async () => {
    try {
      let stream = audioStream
      
      if (!stream) {
        stream = await requestPermission()
      }
      
      if (!stream) {
        throw new Error('No audio stream available')
      }

      // Initialize MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      })
      
      mediaRecorderRef.current = mediaRecorder
      chunksRef.current = []
      
      // Handle data available
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data)
          
          // Send audio chunk for real-time transcription
          if (currentSession) {
            sendAudioChunk(event.data)
          }
        }
      }
      
      // Handle recording stop
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(chunksRef.current, { type: 'audio/webm' })
        processAudioBlob(audioBlob)
      }
      
      // Start recording
      mediaRecorder.start(1000) // Collect data every second
      setIsRecording(true)
      
      // Initialize audio analysis
      initializeAudioAnalysis(stream)
      
    } catch (error) {
      console.error('Error starting recording:', error)
      throw error
    }
  }, [audioStream, requestPermission, initializeAudioAnalysis, currentSession])

  // Stop recording
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
      
      // Stop audio analysis
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
      
      setAudioLevel(0)
    }
  }, [isRecording])

  // Send audio chunk for real-time processing
  const sendAudioChunk = useCallback(async (audioBlob) => {
    try {
      if (!currentSession) {
        console.warn('No current session, skipping audio processing')
        return
      }

      // Create FormData for audio upload
      const formData = new FormData()
      formData.append('audio', audioBlob, 'audio.webm')
      formData.append('language', selectedLanguage)

      // Send to Whisper service for real-time transcription
      const response = await fetch('http://localhost:8001/transcribe-realtime', {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        const result = await response.json()
        const transcribedText = result.text?.trim()

        if (transcribedText && transcribedText.length > 0) {
          console.log('Real-time transcription:', transcribedText)

          // Update transcript in store
          setTranscript(prev => {
            const newTranscript = prev ? `${prev} ${transcribedText}` : transcribedText

            // Send to backend for AI processing via WebSocket
            if (window.socket && window.socket.connected) {
              window.socket.emit('audio-transcript', {
                sessionId: currentSession.id,
                text: transcribedText,
                timestamp: new Date().toISOString(),
                confidence: result.confidence || 0.8
              })
            }

            return newTranscript
          })
        }
      } else {
        console.error('Transcription failed:', response.status)
      }

    } catch (error) {
      console.error('Error processing audio chunk:', error)
    }
  }, [selectedLanguage, setTranscript, currentSession])

  // Process complete audio blob (mock for frontend-only)
  const processAudioBlob = useCallback(async (audioBlob) => {
    try {
      console.log('Mock: Processing final audio blob')
    } catch (error) {
      console.error('Error processing final audio:', error)
    }
  }, [selectedLanguage])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
      
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
      
      if (audioStream) {
        audioStream.getTracks().forEach(track => track.stop())
      }
    }
  }, [audioStream])

  // Check initial permission status
  useEffect(() => {
    const checkPermission = async () => {
      try {
        const result = await navigator.permissions.query({ name: 'microphone' })
        setAudioPermission(result.state)
        
        result.addEventListener('change', () => {
          setAudioPermission(result.state)
        })
      } catch (error) {
        console.error('Error checking microphone permission:', error)
      }
    }
    
    checkPermission()
  }, [])

  return {
    audioPermission,
    audioStream,
    audioLevel,
    isRecording,
    startRecording,
    stopRecording,
    requestPermission
  }
}
