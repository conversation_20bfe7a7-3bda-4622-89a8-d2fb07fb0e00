import { useState, useEffect, useRef, useCallback } from 'react'
import { io } from 'socket.io-client'

const SOCKET_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000'

export const useWebSocket = () => {
  const [socket, setSocket] = useState(null)
  const [isConnected, setIsConnected] = useState(false)
  const [connectionError, setConnectionError] = useState(null)
  const [reconnectAttempts, setReconnectAttempts] = useState(0)

  const socketRef = useRef(null)
  const reconnectTimeoutRef = useRef(null)
  const maxReconnectAttempts = 5

  // Initialize socket connection
  const initializeSocket = useCallback(() => {
    try {
      const newSocket = io(SOCKET_URL, {
        transports: ['websocket', 'polling'],
        timeout: 20000,
        reconnection: true,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        maxReconnectionAttempts: maxReconnectAttempts
      })

      // Connection event handlers
      newSocket.on('connect', () => {
        console.log('✅ WebSocket connected')
        setIsConnected(true)
        setConnectionError(null)
        setReconnectAttempts(0)
      })

      newSocket.on('disconnect', (reason) => {
        console.log('❌ WebSocket disconnected:', reason)
        setIsConnected(false)
        
        if (reason === 'io server disconnect') {
          // Server initiated disconnect, try to reconnect
          newSocket.connect()
        }
      })

      newSocket.on('connect_error', (error) => {
        console.error('❌ WebSocket connection error:', error)
        setConnectionError(error.message)
        setIsConnected(false)
        
        // Implement exponential backoff for reconnection
        const currentAttempts = reconnectAttempts + 1
        setReconnectAttempts(currentAttempts)
        
        if (currentAttempts < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, currentAttempts), 30000)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`🔄 Attempting to reconnect (${currentAttempts}/${maxReconnectAttempts})...`)
            newSocket.connect()
          }, delay)
        } else {
          console.error('❌ Max reconnection attempts reached')
          setConnectionError('Failed to connect after multiple attempts')
        }
      })

      newSocket.on('reconnect', (attemptNumber) => {
        console.log(`✅ WebSocket reconnected after ${attemptNumber} attempts`)
        setIsConnected(true)
        setConnectionError(null)
        setReconnectAttempts(0)
      })

      newSocket.on('reconnect_error', (error) => {
        console.error('❌ WebSocket reconnection error:', error)
        setConnectionError(error.message)
      })

      newSocket.on('reconnect_failed', () => {
        console.error('❌ WebSocket reconnection failed')
        setConnectionError('Failed to reconnect to server')
        setIsConnected(false)
      })

      // Store socket reference
      socketRef.current = newSocket
      setSocket(newSocket)

      // Make socket available globally for other components
      window.socket = newSocket

      return newSocket
    } catch (error) {
      console.error('Error initializing socket:', error)
      setConnectionError(error.message)
      return null
    }
  }, [reconnectAttempts])

  // Send message through socket
  const sendMessage = useCallback((event, data) => {
    if (socketRef.current && isConnected) {
      try {
        socketRef.current.emit(event, data)
        return true
      } catch (error) {
        console.error('Error sending message:', error)
        return false
      }
    } else {
      console.warn('Socket not connected, message not sent:', event, data)
      return false
    }
  }, [isConnected])

  // Manually reconnect
  const reconnect = useCallback(() => {
    if (socketRef.current) {
      setReconnectAttempts(0)
      setConnectionError(null)
      socketRef.current.connect()
    } else {
      initializeSocket()
    }
  }, [initializeSocket])

  // Disconnect socket
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect()
      setSocket(null)
      setIsConnected(false)
      socketRef.current = null
      window.socket = null
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }
  }, [])

  // Initialize socket on mount
  useEffect(() => {
    const newSocket = initializeSocket()
    
    return () => {
      if (newSocket) {
        newSocket.disconnect()
      }
      
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
    }
  }, []) // Empty dependency array - only run on mount

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  // Add event listener helper
  const addEventListener = useCallback((event, handler) => {
    if (socketRef.current) {
      socketRef.current.on(event, handler)
      
      // Return cleanup function
      return () => {
        if (socketRef.current) {
          socketRef.current.off(event, handler)
        }
      }
    }
    return () => {}
  }, [])

  // Remove event listener helper
  const removeEventListener = useCallback((event, handler) => {
    if (socketRef.current) {
      socketRef.current.off(event, handler)
    }
  }, [])

  // Get connection status
  const getConnectionStatus = useCallback(() => {
    if (isConnected) return 'connected'
    if (connectionError) return 'error'
    if (reconnectAttempts > 0) return 'reconnecting'
    return 'disconnected'
  }, [isConnected, connectionError, reconnectAttempts])

  return {
    socket: socketRef.current,
    isConnected,
    connectionError,
    reconnectAttempts,
    connectionStatus: getConnectionStatus(),
    sendMessage,
    reconnect,
    disconnect,
    addEventListener,
    removeEventListener
  }
}
