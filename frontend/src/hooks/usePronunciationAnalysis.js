import { useState, useCallback } from 'react'
import axios from 'axios'

/**
 * Pronunciation Analysis Hook
 * Provides pronunciation assessment and feedback for speech training
 */
export const usePronunciationAnalysis = () => {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [pronunciationScore, setPronunciationScore] = useState(0)
  const [accuracyScore, setAccuracyScore] = useState(0)
  const [fluencyScore, setFluencyScore] = useState(0)
  const [intonationScore, setIntonationScore] = useState(0)
  const [wordStressScore, setWordStressScore] = useState(0)
  const [phoneticScore, setPhoneticScore] = useState(0)
  const [feedback, setFeedback] = useState([])
  const [improvements, setImprovements] = useState([])
  const [detailedAnalysis, setDetailedAnalysis] = useState(null)
  const [error, setError] = useState(null)

  /**
   * Analyze pronunciation quality
   */
  const analyzePronunciation = useCallback(async (spokenText, referenceText, language = 'en', audioMetrics = {}) => {
    if (!spokenText || !referenceText) {
      setError('Both spoken and reference text are required')
      return null
    }

    setIsAnalyzing(true)
    setError(null)

    try {
      const response = await axios.post('/api/pronunciation/analyze', {
        spokenText: spokenText.trim(),
        referenceText: referenceText.trim(),
        language,
        audioMetrics
      })

      if (response.data.success) {
        const analysis = response.data.analysis
        
        // Update state with analysis results
        setPronunciationScore(analysis.overallScore || 0)
        setAccuracyScore(analysis.accuracy || 0)
        setFluencyScore(analysis.fluency?.score || 0)
        setIntonationScore(analysis.intonation?.score || 0)
        setWordStressScore(analysis.wordStress?.accuracy || 0)
        setPhoneticScore(analysis.phoneticAccuracy?.similarity || 0)
        setFeedback(analysis.feedback || [])
        setImprovements(analysis.improvements || [])
        setDetailedAnalysis(analysis)

        return response.data
      } else {
        setError('Pronunciation analysis failed')
        return null
      }
    } catch (error) {
      console.error('Pronunciation analysis error:', error)
      setError(error.response?.data?.error || 'Pronunciation analysis failed')
      return null
    } finally {
      setIsAnalyzing(false)
    }
  }, [])

  /**
   * Calculate pronunciation accuracy only
   */
  const calculateAccuracy = useCallback(async (spokenText, referenceText) => {
    if (!spokenText || !referenceText) {
      return 0
    }

    try {
      const response = await axios.post('/api/pronunciation/accuracy', {
        spokenText: spokenText.trim(),
        referenceText: referenceText.trim()
      })

      if (response.data.success) {
        const accuracy = response.data.accuracy
        setAccuracyScore(accuracy)
        return accuracy
      }
      return 0
    } catch (error) {
      console.error('Accuracy calculation error:', error)
      return 0
    }
  }, [])

  /**
   * Analyze speech fluency
   */
  const analyzeFluency = useCallback(async (spokenText, audioMetrics = {}) => {
    if (!spokenText) {
      return null
    }

    try {
      const response = await axios.post('/api/pronunciation/fluency', {
        spokenText: spokenText.trim(),
        audioMetrics
      })

      if (response.data.success) {
        const fluency = response.data.fluency
        setFluencyScore(fluency.score || 0)
        return fluency
      }
      return null
    } catch (error) {
      console.error('Fluency analysis error:', error)
      return null
    }
  }, [])

  /**
   * Analyze word stress patterns
   */
  const analyzeWordStress = useCallback(async (spokenText, referenceText, language = 'en') => {
    if (!spokenText || !referenceText) {
      return null
    }

    try {
      const response = await axios.post('/api/pronunciation/word-stress', {
        spokenText: spokenText.trim(),
        referenceText: referenceText.trim(),
        language
      })

      if (response.data.success) {
        const wordStress = response.data.wordStress
        setWordStressScore(wordStress.accuracy || 0)
        return wordStress
      }
      return null
    } catch (error) {
      console.error('Word stress analysis error:', error)
      return null
    }
  }, [])

  /**
   * Analyze phonetic accuracy
   */
  const analyzePhonetic = useCallback(async (spokenText, referenceText) => {
    if (!spokenText || !referenceText) {
      return null
    }

    try {
      const response = await axios.post('/api/pronunciation/phonetic', {
        spokenText: spokenText.trim(),
        referenceText: referenceText.trim()
      })

      if (response.data.success) {
        const phonetic = response.data.phonetic
        setPhoneticScore(phonetic.similarity || 0)
        return phonetic
      }
      return null
    } catch (error) {
      console.error('Phonetic analysis error:', error)
      return null
    }
  }, [])

  /**
   * Get practice sentences for pronunciation training
   */
  const getPracticeSentences = useCallback(async (language = 'en', difficulty = 'medium', count = 10) => {
    try {
      const response = await axios.get('/api/pronunciation/practice-sentences', {
        params: { language, difficulty, count }
      })

      if (response.data.success) {
        return response.data.sentences
      }
      return []
    } catch (error) {
      console.error('Error getting practice sentences:', error)
      return []
    }
  }, [])

  /**
   * Perform quick pronunciation assessment
   */
  const quickAssessment = useCallback(async (spokenText, referenceText, language = 'en') => {
    if (!spokenText || !referenceText) {
      return null
    }

    try {
      // Run accuracy and fluency analysis in parallel for quick feedback
      const [accuracyResult, fluencyResult] = await Promise.all([
        calculateAccuracy(spokenText, referenceText),
        analyzeFluency(spokenText)
      ])

      const quickResult = {
        accuracy: accuracyResult,
        fluency: fluencyResult?.score || 0,
        overallScore: Math.round((accuracyResult + (fluencyResult?.score || 0)) / 2),
        timestamp: new Date().toISOString()
      }

      return quickResult
    } catch (error) {
      console.error('Quick assessment error:', error)
      return null
    }
  }, [calculateAccuracy, analyzeFluency])

  /**
   * Get pronunciation feedback summary
   */
  const getFeedbackSummary = useCallback(() => {
    if (!detailedAnalysis) {
      return null
    }

    const summary = {
      overallScore: pronunciationScore,
      strengths: [],
      weaknesses: [],
      recommendations: []
    }

    // Identify strengths
    if (accuracyScore >= 85) summary.strengths.push('Excellent word accuracy')
    if (fluencyScore >= 85) summary.strengths.push('Very fluent speech')
    if (intonationScore >= 85) summary.strengths.push('Good intonation patterns')
    if (wordStressScore >= 85) summary.strengths.push('Correct word stress')

    // Identify weaknesses
    if (accuracyScore < 70) summary.weaknesses.push('Word pronunciation needs improvement')
    if (fluencyScore < 70) summary.weaknesses.push('Speech fluency could be better')
    if (intonationScore < 70) summary.weaknesses.push('Intonation patterns need work')
    if (wordStressScore < 70) summary.weaknesses.push('Word stress placement needs attention')

    // Generate recommendations
    if (accuracyScore < 80) {
      summary.recommendations.push('Practice individual word pronunciation')
    }
    if (fluencyScore < 80) {
      summary.recommendations.push('Work on speaking more smoothly without hesitation')
    }
    if (intonationScore < 80) {
      summary.recommendations.push('Practice varying your voice pitch and emphasis')
    }

    return summary
  }, [pronunciationScore, accuracyScore, fluencyScore, intonationScore, wordStressScore, detailedAnalysis])

  /**
   * Get score color for UI display
   */
  const getScoreColor = useCallback((score) => {
    if (score >= 85) return 'text-green-600'
    if (score >= 70) return 'text-blue-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }, [])

  /**
   * Get score rating
   */
  const getScoreRating = useCallback((score) => {
    if (score >= 90) return 'Excellent'
    if (score >= 80) return 'Good'
    if (score >= 70) return 'Fair'
    if (score >= 60) return 'Needs Improvement'
    return 'Poor'
  }, [])

  /**
   * Format feedback for display
   */
  const formatFeedback = useCallback((feedbackArray) => {
    if (!Array.isArray(feedbackArray)) return []

    return feedbackArray.map(item => ({
      ...item,
      icon: item.type === 'success' ? '🎉' :
            item.type === 'warning' ? '⚠️' :
            item.type === 'error' ? '❌' : 'ℹ️',
      colorClass: item.priority === 'high' ? 'border-red-200 bg-red-50' :
                  item.priority === 'medium' ? 'border-yellow-200 bg-yellow-50' :
                  item.priority === 'positive' ? 'border-green-200 bg-green-50' :
                  'border-blue-200 bg-blue-50'
    }))
  }, [])

  /**
   * Clear all analysis data
   */
  const clearAnalysis = useCallback(() => {
    setPronunciationScore(0)
    setAccuracyScore(0)
    setFluencyScore(0)
    setIntonationScore(0)
    setWordStressScore(0)
    setPhoneticScore(0)
    setFeedback([])
    setImprovements([])
    setDetailedAnalysis(null)
    setError(null)
  }, [])

  /**
   * Get comprehensive analysis summary
   */
  const getAnalysisSummary = useCallback(() => {
    return {
      overallScore: pronunciationScore,
      accuracy: accuracyScore,
      fluency: fluencyScore,
      intonation: intonationScore,
      wordStress: wordStressScore,
      phonetic: phoneticScore,
      feedbackCount: feedback.length,
      improvementCount: improvements.length,
      hasAnalysis: detailedAnalysis !== null,
      rating: getScoreRating(pronunciationScore)
    }
  }, [pronunciationScore, accuracyScore, fluencyScore, intonationScore, wordStressScore, phoneticScore, feedback, improvements, detailedAnalysis, getScoreRating])

  return {
    // State
    isAnalyzing,
    pronunciationScore,
    accuracyScore,
    fluencyScore,
    intonationScore,
    wordStressScore,
    phoneticScore,
    feedback,
    improvements,
    detailedAnalysis,
    error,

    // Core analysis functions
    analyzePronunciation,
    calculateAccuracy,
    analyzeFluency,
    analyzeWordStress,
    analyzePhonetic,

    // Utility functions
    getPracticeSentences,
    quickAssessment,
    getFeedbackSummary,
    getScoreColor,
    getScoreRating,
    formatFeedback,
    clearAnalysis,
    getAnalysisSummary
  }
}
