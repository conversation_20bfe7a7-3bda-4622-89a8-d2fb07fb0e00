import { useState, useCallback } from 'react'
import axios from 'axios'

/**
 * Grammar Analysis Hook
 * Provides real-time grammar checking and speech pattern analysis
 */
export const useGrammarAnalysis = () => {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [grammarErrors, setGrammarErrors] = useState([])
  const [speechMetrics, setSpeechMetrics] = useState(null)
  const [fillerWords, setFillerWords] = useState([])
  const [confidenceScore, setConfidenceScore] = useState(0)
  const [feedback, setFeedback] = useState([])
  const [suggestions, setSuggestions] = useState([])
  const [error, setError] = useState(null)

  /**
   * Analyze text for grammar errors
   */
  const analyzeGrammar = useCallback(async (text, language = 'en') => {
    if (!text || text.trim().length === 0) {
      setError('No text to analyze')
      return null
    }

    setIsAnalyzing(true)
    setError(null)

    try {
      const response = await axios.post('/api/grammar/analyze', {
        text: text.trim(),
        language
      })

      if (response.data.success) {
        setGrammarErrors(response.data.errors || [])
        return response.data
      } else {
        setError('Grammar analysis failed')
        return null
      }
    } catch (error) {
      console.error('Grammar analysis error:', error)
      setError(error.response?.data?.error || 'Grammar analysis failed')
      return null
    } finally {
      setIsAnalyzing(false)
    }
  }, [])

  /**
   * Analyze speech patterns and provide feedback
   */
  const analyzeSpeechPatterns = useCallback(async (text, language = 'en') => {
    if (!text || text.trim().length === 0) {
      setError('No text to analyze')
      return null
    }

    setIsAnalyzing(true)
    setError(null)

    try {
      const response = await axios.post('/api/grammar/speech-patterns', {
        text: text.trim(),
        language
      })

      if (response.data.success) {
        setSpeechMetrics(response.data.metrics)
        setFillerWords(response.data.fillerWords || [])
        setFeedback(response.data.feedback || [])
        setSuggestions(response.data.suggestions || [])
        setConfidenceScore(response.data.metrics?.confidenceScore || 0)
        return response.data
      } else {
        setError('Speech pattern analysis failed')
        return null
      }
    } catch (error) {
      console.error('Speech pattern analysis error:', error)
      setError(error.response?.data?.error || 'Speech pattern analysis failed')
      return null
    } finally {
      setIsAnalyzing(false)
    }
  }, [])

  /**
   * Detect filler words in text
   */
  const detectFillerWords = useCallback(async (text, language = 'en') => {
    if (!text || text.trim().length === 0) {
      return []
    }

    try {
      const response = await axios.post('/api/grammar/filler-words', {
        text: text.trim(),
        language
      })

      if (response.data.success) {
        const detectedFillers = response.data.fillerWords || []
        setFillerWords(detectedFillers)
        return detectedFillers
      }
      return []
    } catch (error) {
      console.error('Filler word detection error:', error)
      return []
    }
  }, [])

  /**
   * Calculate confidence score
   */
  const calculateConfidenceScore = useCallback(async (text, fillerCount = null) => {
    if (!text || text.trim().length === 0) {
      return 0
    }

    try {
      const requestData = { text: text.trim() }
      if (fillerCount !== null) {
        requestData.fillerCount = fillerCount
      }

      const response = await axios.post('/api/grammar/confidence-score', requestData)

      if (response.data.success) {
        const score = response.data.confidenceScore
        setConfidenceScore(score)
        return score
      }
      return 0
    } catch (error) {
      console.error('Confidence score calculation error:', error)
      return 0
    }
  }, [])

  /**
   * Perform comprehensive analysis (grammar + speech patterns)
   */
  const performComprehensiveAnalysis = useCallback(async (text, language = 'en') => {
    if (!text || text.trim().length === 0) {
      setError('No text to analyze')
      return null
    }

    setIsAnalyzing(true)
    setError(null)

    try {
      // Run both analyses in parallel
      const [grammarResult, speechResult] = await Promise.all([
        analyzeGrammar(text, language),
        analyzeSpeechPatterns(text, language)
      ])

      const comprehensiveResult = {
        grammar: grammarResult,
        speechPatterns: speechResult,
        overallScore: speechResult?.metrics?.confidenceScore || 0,
        timestamp: new Date().toISOString()
      }

      return comprehensiveResult
    } catch (error) {
      console.error('Comprehensive analysis error:', error)
      setError('Comprehensive analysis failed')
      return null
    } finally {
      setIsAnalyzing(false)
    }
  }, [analyzeGrammar, analyzeSpeechPatterns])

  /**
   * Get real-time feedback for live transcription
   */
  const getRealTimeFeedback = useCallback(async (text, language = 'en') => {
    if (!text || text.trim().length < 10) {
      return null // Don't analyze very short text
    }

    try {
      // Quick analysis for real-time feedback
      const fillers = await detectFillerWords(text, language)
      const confidence = await calculateConfidenceScore(text, fillers.length)
      
      const quickFeedback = {
        fillerCount: fillers.length,
        fillerWords: fillers,
        confidenceScore: confidence,
        wordCount: text.trim().split(/\s+/).length,
        timestamp: new Date().toISOString()
      }

      return quickFeedback
    } catch (error) {
      console.error('Real-time feedback error:', error)
      return null
    }
  }, [detectFillerWords, calculateConfidenceScore])

  /**
   * Get improvement suggestions based on analysis
   */
  const getImprovementSuggestions = useCallback((analysisResult) => {
    if (!analysisResult) return []

    const suggestions = []

    // Grammar-based suggestions
    if (analysisResult.grammar?.totalErrors > 0) {
      suggestions.push({
        type: 'grammar',
        priority: 'high',
        title: 'Grammar Improvement',
        description: `Found ${analysisResult.grammar.totalErrors} grammar issues. Review and correct them.`,
        action: 'Review grammar errors'
      })
    }

    // Filler word suggestions
    if (analysisResult.speechPatterns?.metrics?.fillerWordPercentage > 3) {
      suggestions.push({
        type: 'filler',
        priority: 'medium',
        title: 'Reduce Filler Words',
        description: `${analysisResult.speechPatterns.metrics.fillerWordPercentage.toFixed(1)}% filler words detected. Practice pausing instead.`,
        action: 'Practice deliberate pauses'
      })
    }

    // Confidence suggestions
    if (analysisResult.speechPatterns?.metrics?.confidenceScore < 70) {
      suggestions.push({
        type: 'confidence',
        priority: 'high',
        title: 'Build Confidence',
        description: 'Your speech patterns suggest hesitation. Practice speaking with more authority.',
        action: 'Practice confident speech'
      })
    }

    // Sentence structure suggestions
    if (analysisResult.speechPatterns?.metrics?.averageWordsPerSentence > 20) {
      suggestions.push({
        type: 'structure',
        priority: 'medium',
        title: 'Shorter Sentences',
        description: 'Your sentences are quite long. Break them into shorter, clearer statements.',
        action: 'Practice concise communication'
      })
    }

    return suggestions
  }, [])

  /**
   * Format feedback for display
   */
  const formatFeedbackForDisplay = useCallback((feedback) => {
    if (!feedback || !Array.isArray(feedback)) return []

    return feedback.map(item => ({
      ...item,
      icon: item.type === 'success' ? '✅' : 
            item.type === 'warning' ? '⚠️' : 
            item.type === 'error' ? '❌' : 'ℹ️',
      color: item.severity === 'high' ? 'red' :
             item.severity === 'medium' ? 'yellow' :
             item.severity === 'positive' ? 'green' : 'blue'
    }))
  }, [])

  /**
   * Clear all analysis data
   */
  const clearAnalysis = useCallback(() => {
    setGrammarErrors([])
    setSpeechMetrics(null)
    setFillerWords([])
    setConfidenceScore(0)
    setFeedback([])
    setSuggestions([])
    setError(null)
  }, [])

  /**
   * Get analysis summary
   */
  const getAnalysisSummary = useCallback(() => {
    return {
      grammarErrorCount: grammarErrors.length,
      fillerWordCount: fillerWords.length,
      confidenceScore,
      hasErrors: grammarErrors.length > 0,
      hasFillers: fillerWords.length > 0,
      needsImprovement: confidenceScore < 70,
      overallRating: confidenceScore >= 85 ? 'Excellent' :
                     confidenceScore >= 70 ? 'Good' :
                     confidenceScore >= 60 ? 'Fair' : 'Needs Improvement'
    }
  }, [grammarErrors, fillerWords, confidenceScore])

  return {
    // State
    isAnalyzing,
    grammarErrors,
    speechMetrics,
    fillerWords,
    confidenceScore,
    feedback,
    suggestions,
    error,

    // Core analysis functions
    analyzeGrammar,
    analyzeSpeechPatterns,
    detectFillerWords,
    calculateConfidenceScore,
    performComprehensiveAnalysis,

    // Real-time functions
    getRealTimeFeedback,

    // Utility functions
    getImprovementSuggestions,
    formatFeedbackForDisplay,
    clearAnalysis,
    getAnalysisSummary
  }
}
