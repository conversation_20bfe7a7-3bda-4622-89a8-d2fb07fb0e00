import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import { useThemeStore } from './store/themeStore'
import { useAppStore } from './store/appStore'
import { useEffect } from 'react'

// Pages
import Setup from './pages/Setup'
import LiveCopilot from './pages/LiveCopilot'
import History from './pages/History'
import Landing from './pages/Landing'
import Analytics from './pages/Analytics'
import PronunciationTrainer from './pages/PronunciationTrainer'

// Components
import Navbar from './components/Navbar'
import Footer from './components/Footer'

// Advanced AI Components
import CelestialDashboard from './components/UI/CelestialDashboard'
import NeuroAIGuidance from './components/NeuroAI/NeuroAIGuidance'
import GestureControls from './components/Gesture/GestureControls'
import ARCopilotPanel from './components/AR/ARCopilotPanel'
import SmartModeDetector from './components/Smart/SmartModeDetector'

function App() {
  const { theme } = useThemeStore()
  const { uiTheme } = useAppStore()

  useEffect(() => {
    // Apply theme to document
    if (theme === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [theme])

  return (
    <Router>
      <CelestialDashboard>
        <div className="min-h-screen transition-colors">
          <Navbar />

          <main className="flex-1">
            <Routes>
              <Route path="/" element={<Landing />} />
              <Route path="/setup" element={<Setup />} />
              <Route path="/copilot" element={<LiveCopilot />} />
              <Route path="/history" element={<History />} />
              <Route path="/analytics" element={<Analytics />} />
              <Route path="/pronunciation" element={<PronunciationTrainer />} />
            </Routes>
          </main>

          <Footer />

          {/* Advanced AI Features - Always Active */}
          <NeuroAIGuidance />
          <GestureControls />
          <ARCopilotPanel />
          <SmartModeDetector />

          {/* Toast notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: uiTheme.mode === 'celestial' ? '#1E1B4B' :
                           uiTheme.mode === 'nebula' ? '#581C87' : '#0F172A',
                color: '#fff',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
              },
            }}
          />
        </div>
      </CelestialDashboard>
    </Router>
  )
}

export default App
