import React, { useState, useEffect } from 'react'
import { 
  Calendar, 
  Clock, 
  Download, 
  Trash2, 
  Eye, 
  TrendingUp,
  MessageSquare,
  Lightbulb,
  Filter,
  Search,
  BarChart3
} from 'lucide-react'
import { useAppStore } from '../store/appStore'
// Mock react-hot-toast for frontend-only mode
const toast = {
  success: (message) => console.log('✅ Success:', message),
  error: (message) => console.log('❌ Error:', message),
  loading: (message) => console.log('⏳ Loading:', message)
}

const History = () => {
  const { userName } = useAppStore()
  const [sessions, setSessions] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedSession, setSelectedSession] = useState(null)
  const [showDetails, setShowDetails] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState('date')
  const [filterBy, setFilterBy] = useState('all')

  useEffect(() => {
    if (userName) {
      fetchSessions()
    }
  }, [userName])

  const fetchSessions = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/session/user/${userName}`)
      const data = await response.json()
      
      if (data.success) {
        setSessions(data.sessions)
      }
    } catch (error) {
      console.error('Error fetching sessions:', error)
      toast.error('Failed to load session history')
    } finally {
      setLoading(false)
    }
  }

  const deleteSession = async (sessionId) => {
    if (!confirm('Are you sure you want to delete this session?')) {
      return
    }

    try {
      const response = await fetch(`/api/session/${sessionId}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        setSessions(sessions.filter(s => s.id !== sessionId))
        toast.success('Session deleted successfully')
      }
    } catch (error) {
      console.error('Error deleting session:', error)
      toast.error('Failed to delete session')
    }
  }

  const exportSession = async (sessionId, format = 'json') => {
    try {
      const response = await fetch(`/api/session/${sessionId}/export?format=${format}`)
      
      if (format === 'json') {
        const data = await response.json()
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
        downloadBlob(blob, `session-${sessionId}.json`)
      } else {
        const text = await response.text()
        const blob = new Blob([text], { type: 'text/plain' })
        downloadBlob(blob, `session-${sessionId}.txt`)
      }
      
      toast.success('Session exported successfully')
    } catch (error) {
      console.error('Error exporting session:', error)
      toast.error('Failed to export session')
    }
  }

  const downloadBlob = (blob, filename) => {
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const viewSessionDetails = async (sessionId) => {
    try {
      const response = await fetch(`/api/session/${sessionId}`)
      const data = await response.json()
      
      if (data.success) {
        setSelectedSession(data.session)
        setShowDetails(true)
      }
    } catch (error) {
      console.error('Error fetching session details:', error)
      toast.error('Failed to load session details')
    }
  }

  const formatDuration = (seconds) => {
    if (!seconds) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const filteredSessions = sessions
    .filter(session => {
      if (searchTerm) {
        return session.jobDescription?.toLowerCase().includes(searchTerm.toLowerCase()) ||
               session.language.toLowerCase().includes(searchTerm.toLowerCase())
      }
      return true
    })
    .filter(session => {
      if (filterBy === 'all') return true
      if (filterBy === 'completed') return session.endTime
      if (filterBy === 'incomplete') return !session.endTime
      return true
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.createdAt) - new Date(a.createdAt)
        case 'duration':
          return (b.duration || 0) - (a.duration || 0)
        case 'language':
          return a.language.localeCompare(b.language)
        default:
          return 0
      }
    })

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">Loading session history...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Session History
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Review your past interview sessions and track your progress
          </p>
        </div>

        {/* Filters and Search */}
        <div className="card p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search sessions..."
                  className="input pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Sort */}
            <div className="flex gap-4">
              <select
                className="input"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="date">Sort by Date</option>
                <option value="duration">Sort by Duration</option>
                <option value="language">Sort by Language</option>
              </select>

              {/* Filter */}
              <select
                className="input"
                value={filterBy}
                onChange={(e) => setFilterBy(e.target.value)}
              >
                <option value="all">All Sessions</option>
                <option value="completed">Completed</option>
                <option value="incomplete">Incomplete</option>
              </select>
            </div>
          </div>
        </div>

        {/* Sessions List */}
        {filteredSessions.length === 0 ? (
          <div className="card p-12 text-center">
            <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No Sessions Found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {searchTerm || filterBy !== 'all' 
                ? 'Try adjusting your search or filter criteria'
                : 'Start your first interview session to see it here'
              }
            </p>
            <button
              onClick={() => window.location.href = '/setup'}
              className="btn btn-primary"
            >
              Start New Session
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredSessions.map((session) => (
              <div key={session.id} className="card p-6 hover:shadow-lg transition-shadow">
                {/* Session Header */}
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      Session #{session.id}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {formatDate(session.startTime)}
                    </p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    session.endTime 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  }`}>
                    {session.endTime ? 'Completed' : 'Incomplete'}
                  </span>
                </div>

                {/* Session Stats */}
                <div className="space-y-3 mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600 dark:text-gray-400">Duration</span>
                    </div>
                    <span className="font-medium">{formatDuration(session.duration)}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <MessageSquare className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600 dark:text-gray-400">Language</span>
                    </div>
                    <span className="font-medium uppercase">{session.language}</span>
                  </div>
                </div>

                {/* Job Description Preview */}
                {session.jobDescription && (
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                      {session.jobDescription}
                    </p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={() => viewSessionDetails(session.id)}
                    className="btn btn-ghost btn-sm flex items-center space-x-1"
                  >
                    <Eye className="w-4 h-4" />
                    <span>View</span>
                  </button>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => exportSession(session.id, 'txt')}
                      className="btn btn-ghost btn-sm p-2"
                      title="Export as TXT"
                    >
                      <Download className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={() => deleteSession(session.id)}
                      className="btn btn-ghost btn-sm p-2 text-red-600 hover:text-red-700"
                      title="Delete Session"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Session Details Modal */}
        {showDetails && selectedSession && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                {/* Modal Header */}
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    Session #{selectedSession.id} Details
                  </h2>
                  <button
                    onClick={() => setShowDetails(false)}
                    className="btn btn-ghost p-2"
                  >
                    ×
                  </button>
                </div>

                {/* Session Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div className="space-y-4">
                    <div>
                      <label className="label">Start Time</label>
                      <p className="text-gray-900 dark:text-white">
                        {formatDate(selectedSession.startTime)}
                      </p>
                    </div>
                    
                    <div>
                      <label className="label">Duration</label>
                      <p className="text-gray-900 dark:text-white">
                        {formatDuration(selectedSession.duration)}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="label">Language</label>
                      <p className="text-gray-900 dark:text-white uppercase">
                        {selectedSession.language}
                      </p>
                    </div>
                    
                    <div>
                      <label className="label">Status</label>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        selectedSession.endTime 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                      }`}>
                        {selectedSession.endTime ? 'Completed' : 'Incomplete'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Job Description */}
                {selectedSession.jobDescription && (
                  <div className="mb-6">
                    <label className="label">Job Description</label>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <p className="text-gray-900 dark:text-white">
                        {selectedSession.jobDescription}
                      </p>
                    </div>
                  </div>
                )}

                {/* Transcripts */}
                {selectedSession.transcripts && selectedSession.transcripts.length > 0 && (
                  <div className="mb-6">
                    <label className="label">Transcript</label>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-h-60 overflow-y-auto">
                      {selectedSession.transcripts.map((transcript, index) => (
                        <div key={index} className="mb-2">
                          <span className="text-xs text-gray-500">
                            {new Date(transcript.timestamp).toLocaleTimeString()}
                          </span>
                          <p className="text-gray-900 dark:text-white">
                            {transcript.text}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* AI Suggestions */}
                {selectedSession.suggestions && selectedSession.suggestions.length > 0 && (
                  <div className="mb-6">
                    <label className="label">AI Suggestions</label>
                    <div className="space-y-3 max-h-60 overflow-y-auto">
                      {selectedSession.suggestions.map((suggestion, index) => (
                        <div key={index} className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                          <p className="text-blue-900 dark:text-blue-100 text-sm">
                            {suggestion.suggestion}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-blue-600 dark:text-blue-400">
                              Confidence: {Math.round((suggestion.confidence || 0.8) * 100)}%
                            </span>
                            <span className="text-xs text-gray-500">
                              {new Date(suggestion.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Modal Actions */}
                <div className="flex justify-end space-x-4">
                  <button
                    onClick={() => exportSession(selectedSession.id, 'json')}
                    className="btn btn-outline flex items-center space-x-2"
                  >
                    <Download className="w-4 h-4" />
                    <span>Export JSON</span>
                  </button>
                  
                  <button
                    onClick={() => exportSession(selectedSession.id, 'txt')}
                    className="btn btn-primary flex items-center space-x-2"
                  >
                    <Download className="w-4 h-4" />
                    <span>Export TXT</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default History
