import React, { useState, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
// Mock react-dropzone for frontend-only mode
const useDropzone = ({ onDrop, accept, maxFiles }) => {
  return {
    getRootProps: () => ({
      onClick: () => {
        const input = document.createElement('input')
        input.type = 'file'
        input.accept = Object.keys(accept || {}).join(',')
        input.onchange = (e) => {
          const files = Array.from(e.target.files)
          onDrop(files)
        }
        input.click()
      }
    }),
    getInputProps: () => ({ style: { display: 'none' } }),
    isDragActive: false
  }
}
import { 
  Upload, 
  FileText, 
  User, 
  Globe, 
  Mic, 
  Volume2, 
  ArrowRight,
  CheckCircle,
  AlertCircle,
  X
} from 'lucide-react'
import { useAppStore } from '../store/appStore'
// Real toast implementation
import toast from 'react-hot-toast'

const Setup = () => {
  const navigate = useNavigate()
  const {
    resume,
    jobDescription,
    selectedLanguage,
    userName,
    setResume,
    setJobDescription,
    setSelectedLanguage,
    setUserName,
    setAudioPermission,
    setSelectedMicrophone,
    setSelectedSpeaker,
    setCurrentSession
  } = useAppStore()

  const [currentStep, setCurrentStep] = useState(1)
  const [audioDevices, setAudioDevices] = useState({ microphones: [], speakers: [] })
  const [permissionStatus, setPermissionStatus] = useState(null)

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
    { code: 'es', name: 'Spanish', flag: '🇪🇸' },
    { code: 'ar', name: 'Arabic', flag: '🇸🇦' },
    { code: 'de', name: 'German', flag: '🇩🇪' },
  ]

  // File upload handler
  const onDrop = useCallback((acceptedFiles) => {
    const file = acceptedFiles[0]
    if (file) {
      if (file.type === 'application/pdf' || file.type === 'text/plain') {
        setResume(file)
        toast.success('Resume uploaded successfully!')
      } else {
        toast.error('Please upload a PDF or text file')
      }
    }
  }, [setResume])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'text/plain': ['.txt']
    },
    maxFiles: 1
  })

  // Audio permission handler
  const requestAudioPermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      setPermissionStatus('granted')
      setAudioPermission('granted')
      
      // Get available devices
      const devices = await navigator.mediaDevices.enumerateDevices()
      const microphones = devices.filter(device => device.kind === 'audioinput')
      const speakers = devices.filter(device => device.kind === 'audiooutput')
      
      setAudioDevices({ microphones, speakers })
      
      // Set default devices
      if (microphones.length > 0) {
        setSelectedMicrophone(microphones[0].deviceId)
      }
      if (speakers.length > 0) {
        setSelectedSpeaker(speakers[0].deviceId)
      }
      
      // Stop the stream
      stream.getTracks().forEach(track => track.stop())
      
      toast.success('Audio permission granted!')
    } catch (error) {
      setPermissionStatus('denied')
      setAudioPermission('denied')
      toast.error('Audio permission denied. VocaPilot needs microphone access to work.')
    }
  }

  const handleNext = async () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1)
    } else {
      // Validate setup
      if (!userName.trim()) {
        toast.error('Please enter your name')
        return
      }
      if (permissionStatus !== 'granted') {
        toast.error('Please grant audio permission to continue')
        return
      }
      if (!jobDescription.trim()) {
        toast.error('Please enter a job description')
        return
      }

      // Create session
      try {
        const sessionData = {
          userName,
          jobDescription,
          language: selectedLanguage
        }

        console.log('Creating session with data:', sessionData)
        console.log('Audio permission:', permissionStatus)

        const response = await fetch('/api/session/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(sessionData),
        })

        const result = await response.json()
        console.log('Session creation result:', result)

        if (result.success) {
          setCurrentSession(result.session)
          toast.success('Setup completed successfully!')
          console.log('Navigating to /copilot with session:', result.session)
          navigate('/copilot')
        } else {
          toast.error('Failed to create session: ' + (result.error || 'Unknown error'))
        }
      } catch (error) {
        console.error('Setup completion error:', error)
        toast.error('Failed to complete setup: ' + error.message)
      }
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const isStepComplete = (step) => {
    switch (step) {
      case 1:
        return userName.trim().length > 0
      case 2:
        return resume !== null
      case 3:
        return jobDescription.trim().length > 0
      case 4:
        return permissionStatus === 'granted'
      default:
        return false
    }
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <User className="w-16 h-16 text-primary-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Welcome to VocaPilot
              </h2>
              <p className="text-gray-600 dark:text-gray-300">
                Let's start by getting to know you
              </p>
            </div>

            <div className="space-y-4">
              <div>
                <label className="label">Your Name</label>
                <input
                  type="text"
                  className="input"
                  placeholder="Enter your full name"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                />
              </div>

              <div>
                <label className="label">Preferred Language</label>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {languages.map((lang) => (
                    <button
                      key={lang.code}
                      onClick={() => setSelectedLanguage(lang.code)}
                      className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors ${
                        selectedLanguage === lang.code
                          ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                          : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                      }`}
                    >
                      <span className="text-2xl">{lang.flag}</span>
                      <span className="font-medium">{lang.name}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <FileText className="w-16 h-16 text-primary-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Upload Your Resume
              </h2>
              <p className="text-gray-600 dark:text-gray-300">
                Help VocaPilot provide personalized suggestions based on your experience
              </p>
            </div>

            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive
                  ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                  : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
              }`}
            >
              <input {...getInputProps()} />
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              {resume ? (
                <div className="space-y-2">
                  <p className="text-green-600 dark:text-green-400 font-medium">
                    ✓ {resume.name}
                  </p>
                  <p className="text-sm text-gray-500">
                    Click or drag to replace
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  <p className="text-gray-600 dark:text-gray-300">
                    Drag and drop your resume here, or click to browse
                  </p>
                  <p className="text-sm text-gray-500">
                    Supports PDF and TXT files
                  </p>
                </div>
              )}
            </div>

            <div className="text-sm text-gray-500 bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <strong>Privacy Note:</strong> Your resume is processed locally and never sent to external servers without your consent.
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Globe className="w-16 h-16 text-primary-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Job Description
              </h2>
              <p className="text-gray-600 dark:text-gray-300">
                Paste the job description to get tailored interview assistance
              </p>
            </div>

            <div>
              <label className="label">Job Description (Optional)</label>
              <textarea
                className="textarea min-h-[200px]"
                placeholder="Paste the job description here to get more relevant suggestions during your interview..."
                value={jobDescription}
                onChange={(e) => setJobDescription(e.target.value)}
              />
              <p className="text-sm text-gray-500 mt-2">
                This helps VocaPilot understand the role and provide better answers
              </p>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Mic className="w-16 h-16 text-primary-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Audio Setup
              </h2>
              <p className="text-gray-600 dark:text-gray-300">
                Grant microphone access for real-time transcription
              </p>
            </div>

            <div className="space-y-4">
              {permissionStatus === null && (
                <div className="text-center">
                  <button
                    onClick={requestAudioPermission}
                    className="btn btn-primary btn-lg flex items-center space-x-2 mx-auto"
                  >
                    <Mic className="w-5 h-5" />
                    <span>Grant Microphone Access</span>
                  </button>
                </div>
              )}

              {permissionStatus === 'granted' && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
                    <CheckCircle className="w-5 h-5" />
                    <span>Microphone access granted!</span>
                  </div>

                  {audioDevices.microphones.length > 0 && (
                    <div>
                      <label className="label">Microphone</label>
                      <select className="input">
                        {audioDevices.microphones.map((device) => (
                          <option key={device.deviceId} value={device.deviceId}>
                            {device.label || `Microphone ${device.deviceId.slice(0, 8)}`}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}

                  {audioDevices.speakers.length > 0 && (
                    <div>
                      <label className="label">Speaker (Optional)</label>
                      <select className="input">
                        {audioDevices.speakers.map((device) => (
                          <option key={device.deviceId} value={device.deviceId}>
                            {device.label || `Speaker ${device.deviceId.slice(0, 8)}`}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}
                </div>
              )}

              {permissionStatus === 'denied' && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
                    <AlertCircle className="w-5 h-5" />
                    <span>Microphone access denied</span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    VocaPilot needs microphone access to provide real-time transcription. 
                    Please enable it in your browser settings and try again.
                  </p>
                  <button
                    onClick={requestAudioPermission}
                    className="btn btn-outline"
                  >
                    Try Again
                  </button>
                </div>
              )}
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Step {currentStep} of 4
            </span>
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              {Math.round((currentStep / 4) * 100)}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / 4) * 100}%` }}
            />
          </div>
        </div>

        {/* Step Indicators */}
        <div className="flex justify-center mb-8">
          <div className="flex space-x-4">
            {[1, 2, 3, 4].map((step) => (
              <div
                key={step}
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step < currentStep
                    ? 'bg-green-500 text-white'
                    : step === currentStep
                    ? 'bg-primary-500 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
                }`}
              >
                {step < currentStep ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  step
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="card p-8 mb-8">
          {renderStep()}
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className="btn btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <button
            onClick={handleNext}
            className="btn btn-primary flex items-center space-x-2"
          >
            <span>{currentStep === 4 ? 'Start Copilot' : 'Next'}</span>
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )
}

export default Setup
