import React from 'react'
import { Link } from 'react-router-dom'
import { 
  Mic, 
  Brain, 
  Shield, 
  Zap, 
  Globe, 
  FileText, 
  TrendingUp, 
  Users,
  ArrowRight,
  Play,
  CheckCircle
} from 'lucide-react'

const Landing = () => {
  const features = [
    {
      icon: Mic,
      title: 'Real-time Audio Processing',
      description: 'Capture and transcribe speech with <2s latency using Whisper.cpp'
    },
    {
      icon: Brain,
      title: 'AI-Powered Suggestions',
      description: 'Get contextual answers tailored to your resume and job description'
    },
    {
      icon: TrendingUp,
      title: 'Tone & Speech Analysis',
      description: 'Real-time feedback on speech rate, confidence, and clarity'
    },
    {
      icon: Globe,
      title: 'Multilingual Support',
      description: 'Support for English, French, Spanish, Arabic, and German'
    },
    {
      icon: FileText,
      title: 'Resume Integration',
      description: 'Upload your resume for personalized interview assistance'
    },
    {
      icon: Shield,
      title: 'Privacy-First',
      description: 'Local-first processing with optional cloud components'
    }
  ]

  const benefits = [
    'Free and open-source forever',
    'No paid plans or hidden fees',
    'Privacy-respecting design',
    'Works offline when possible',
    'Real-time performance',
    'Professional interview support'
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-primary-50 to-primary-100 dark:from-gray-900 dark:to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              Your voice. Your guide.
              <span className="block gradient-text">In real time.</span>
            </h1>
            
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
              A free, open-source, real-time AI interview & meeting copilot designed to assist users 
              during live video interviews and calls, offering contextual answers, tone feedback, 
              and resume-based support.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Link
                to="/setup"
                className="btn btn-primary btn-lg flex items-center space-x-2"
              >
                <span>Get Started</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              
              <button className="btn btn-outline btn-lg flex items-center space-x-2">
                <Play className="w-5 h-5" />
                <span>Watch Demo</span>
              </button>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 dark:text-primary-400">
                  &lt;2s
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Transcription Latency
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 dark:text-primary-400">
                  5
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Languages Supported
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 dark:text-primary-400">
                  100%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Free & Open Source
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Powerful Features for Interview Success
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Everything you need to ace your next interview, powered by cutting-edge AI technology
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <div
                  key={index}
                  className="card p-6 hover:shadow-lg transition-shadow"
                >
                  <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-4">
                    <Icon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {feature.description}
                  </p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Why Choose VocaPilot?
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                Unlike other interview tools, VocaPilot is completely free, respects your privacy, 
                and works in real-time to give you the edge you need.
              </p>
              
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="w-5 h-5 text-success-500 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="relative">
              <div className="card p-8 bg-gradient-to-br from-primary-500 to-primary-700 text-white">
                <Users className="w-12 h-12 mb-4 opacity-80" />
                <h3 className="text-2xl font-bold mb-4">
                  Join the Community
                </h3>
                <p className="mb-6 opacity-90">
                  VocaPilot is built by developers, for developers. Join our growing community 
                  of contributors and users.
                </p>
                <div className="flex space-x-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">1k+</div>
                    <div className="text-sm opacity-80">GitHub Stars</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">500+</div>
                    <div className="text-sm opacity-80">Contributors</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">10k+</div>
                    <div className="text-sm opacity-80">Users</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
            Ready to Ace Your Next Interview?
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            Get started with VocaPilot in less than 5 minutes. No signup required.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/setup"
              className="btn btn-primary btn-lg flex items-center justify-center space-x-2"
            >
              <span>Start Your Setup</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
            
            <Link
              to="/copilot"
              className="btn btn-outline btn-lg flex items-center justify-center space-x-2"
            >
              <Mic className="w-5 h-5" />
              <span>Try Live Demo</span>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Landing
