import React, { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Mi<PERSON>,
  MicOff,
  Square,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Settings,
  TrendingUp,
  MessageSquare,
  Download,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { useAppStore } from '../store/appStore'
import { useAudioRecording } from '../hooks/useAudioRecording'
import { useWebSocket } from '../hooks/useWebSocket'
import LiveSuggestions from '../components/LiveSuggestions'
import GrammarFeedback from '../components/GrammarFeedback'
import TTSControls from '../components/TTSControls'
import TimeTravelReplay from '../components/TimeTravel/TimeTravelReplay'
import PersonaSimulator from '../components/Persona/PersonaSimulator'
import JobFitAnalyzer from '../components/JobFit/JobFitAnalyzer'
// Real toast implementation
import toast from 'react-hot-toast'

const LiveCopilot = () => {
  const navigate = useNavigate()
  const {
    userName,
    jobDescription,
    selectedLanguage,
    isRecording,
    transcript,
    suggestions,
    toneAnalysis,
    currentSession,
    audioPermission: storeAudioPermission,
    setIsRecording,
    setCurrentSession,
    addSuggestion,
    setToneAnalysis,
    resetSession
  } = useAppStore()

  const [sessionStarted, setSessionStarted] = useState(false)
  const [sessionDuration, setSessionDuration] = useState(0)
  const [showSettings, setShowSettings] = useState(false)
  const [audioLevel, setAudioLevel] = useState(0)
  const [connectionStatus, setConnectionStatus] = useState('disconnected')
  const [showGrammarFeedback, setShowGrammarFeedback] = useState(true)
  const [showTTSControls, setShowTTSControls] = useState(false)
  const [latestSuggestion, setLatestSuggestion] = useState('')

  // New advanced features state
  const [showTimeTravelReplay, setShowTimeTravelReplay] = useState(false)
  const [showPersonaSimulator, setShowPersonaSimulator] = useState(false)
  const [showJobFitAnalyzer, setShowJobFitAnalyzer] = useState(false)
  const [activeTab, setActiveTab] = useState('suggestions') // 'suggestions', 'persona', 'jobfit', 'replay'

  const sessionTimerRef = useRef(null)
  const transcriptRef = useRef(null)

  // Custom hooks
  const { 
    startRecording, 
    stopRecording, 
    audioPermission,
    audioStream 
  } = useAudioRecording()

  const { 
    socket, 
    isConnected, 
    sendMessage 
  } = useWebSocket()

  // Check if user has completed setup
  useEffect(() => {
    if (!userName || !storeAudioPermission || storeAudioPermission !== 'granted') {
      toast.error('Please complete setup first')
      navigate('/setup')
      return
    }

    // If user has completed setup but no current session, create one
    if (userName && jobDescription && !currentSession) {
      console.log('User has completed setup but no session exists, creating session...')
      initializeSession()
    }
  }, [userName, storeAudioPermission, jobDescription, currentSession, navigate])

  // Initialize session
  useEffect(() => {
    if (userName && !currentSession) {
      initializeSession()
    }
  }, [userName, currentSession])

  // Session timer
  useEffect(() => {
    if (sessionStarted) {
      sessionTimerRef.current = setInterval(() => {
        setSessionDuration(prev => prev + 1)
      }, 1000)
    } else {
      if (sessionTimerRef.current) {
        clearInterval(sessionTimerRef.current)
      }
    }

    return () => {
      if (sessionTimerRef.current) {
        clearInterval(sessionTimerRef.current)
      }
    }
  }, [sessionStarted])

  // Auto-scroll transcript
  useEffect(() => {
    if (transcriptRef.current) {
      transcriptRef.current.scrollTop = transcriptRef.current.scrollHeight
    }
  }, [transcript])

  // Update connection status based on WebSocket state
  useEffect(() => {
    if (socket && isConnected) {
      setConnectionStatus('connecting')
    } else if (!socket || !isConnected) {
      setConnectionStatus('disconnected')
    }
  }, [socket, isConnected])

  // WebSocket connection and session joining
  useEffect(() => {
    if (socket && isConnected && currentSession) {
      console.log('WebSocket connected, joining session:', currentSession.id)

      // Join session room
      sendMessage('join-session', {
        sessionId: currentSession.id,
        userName
      })
    }
  }, [socket, isConnected, currentSession, userName, sendMessage])

  // WebSocket event handlers
  useEffect(() => {
    if (socket) {
      socket.on('session-joined', (data) => {
        console.log('Session joined successfully:', data)
        setConnectionStatus('connected')
        toast.success('Connected to VocaPilot')
      })

      socket.on('transcript-update', (data) => {
        // Transcript updates are handled by the store
      })

      socket.on('ai-suggestion', (data) => {
        addSuggestion({
          id: data.id,
          text: data.suggestion,
          context: data.context,
          confidence: data.confidence,
          timestamp: data.timestamp
        })

        // Store latest suggestion for TTS
        setLatestSuggestion(data.suggestion)
      })

      socket.on('tone-analysis', (data) => {
        setToneAnalysis(data)
      })

      socket.on('error', (error) => {
        console.error('WebSocket error:', error)
        setConnectionStatus('disconnected')
        toast.error(error.message || 'Connection error occurred')
      })

      socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error)
        setConnectionStatus('disconnected')
        toast.error('Failed to connect to server')
      })

      socket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason)
        setConnectionStatus('disconnected')
        toast.error('Disconnected from server')
      })

      return () => {
        socket.off('session-joined')
        socket.off('transcript-update')
        socket.off('ai-suggestion')
        socket.off('tone-analysis')
        socket.off('error')
        socket.off('connect_error')
        socket.off('disconnect')
      }
    }
  }, [socket, addSuggestion, setToneAnalysis])

  const initializeSession = async () => {
    try {
      const response = await fetch('/api/session/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userName,
          jobDescription,
          language: selectedLanguage
        })
      })

      const data = await response.json()
      if (data.success) {
        console.log('Session created successfully:', data.session)
        setCurrentSession(data.session)
        // WebSocket joining will be handled by the useEffect above
      }
    } catch (error) {
      console.error('Error initializing session:', error)
      toast.error('Failed to initialize session')
    }
  }

  const handleStartSession = async () => {
    try {
      await startRecording()
      setIsRecording(true)
      setSessionStarted(true)
      
      if (socket && currentSession) {
        sendMessage('start-recording', { sessionId: currentSession.id })
      }
      
      toast.success('Session started! VocaPilot is listening...')
    } catch (error) {
      console.error('Error starting session:', error)
      toast.error('Failed to start recording')
    }
  }

  const handleStopSession = async () => {
    try {
      await stopRecording()
      setIsRecording(false)
      setSessionStarted(false)
      
      if (socket && currentSession) {
        sendMessage('stop-recording', { sessionId: currentSession.id })
      }
      
      toast.success('Session stopped')
    } catch (error) {
      console.error('Error stopping session:', error)
      toast.error('Failed to stop recording')
    }
  }

  const handleEndSession = async () => {
    if (isRecording) {
      await handleStopSession()
    }
    
    // Update session with end time and duration
    if (currentSession) {
      try {
        await fetch(`/api/session/${currentSession.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            endTime: new Date().toISOString(),
            duration: sessionDuration,
            transcript
          })
        })
      } catch (error) {
        console.error('Error updating session:', error)
      }
    }
    
    resetSession()
    navigate('/history')
  }

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-500'
      case 'connecting': return 'text-yellow-500'
      default: return 'text-red-500'
    }
  }

  if (!userName) {
    return null // Will redirect to setup
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Live Copilot
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Real-time interview assistance for {userName}
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Connection Status */}
              <div className={`flex items-center space-x-2 ${getConnectionStatusColor()}`}>
                <div className="w-2 h-2 rounded-full bg-current animate-pulse" />
                <span className="text-sm font-medium capitalize">{connectionStatus}</span>
              </div>
              
              {/* Session Timer */}
              <div className="text-2xl font-mono font-bold text-gray-900 dark:text-white">
                {formatDuration(sessionDuration)}
              </div>
              
              {/* Feature Toggles */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setShowGrammarFeedback(!showGrammarFeedback)}
                  className={`btn btn-ghost p-2 ${showGrammarFeedback ? 'text-blue-600' : 'text-gray-400'}`}
                  title="Toggle Grammar Feedback"
                >
                  <MessageSquare className="w-5 h-5" />
                </button>

                <button
                  onClick={() => setShowTTSControls(!showTTSControls)}
                  className={`btn btn-ghost p-2 ${showTTSControls ? 'text-green-600' : 'text-gray-400'}`}
                  title="Toggle AI Voice"
                >
                  <Volume2 className="w-5 h-5" />
                </button>

                <button
                  onClick={() => setShowSettings(!showSettings)}
                  className="btn btn-ghost p-2"
                  title="Settings"
                >
                  <Settings className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Control Panel */}
          <div className="lg:col-span-2 space-y-6">
            {/* Recording Controls */}
            <div className="card p-6">
              <div className="text-center">
                <div className="mb-6">
                  <button
                    onClick={sessionStarted ? handleStopSession : handleStartSession}
                    disabled={!currentSession || connectionStatus !== 'connected'}
                    className={`w-24 h-24 rounded-full flex items-center justify-center text-white text-2xl transition-all ${
                      isRecording
                        ? 'bg-red-500 hover:bg-red-600 animate-pulse'
                        : 'bg-primary-500 hover:bg-primary-600'
                    } disabled:opacity-50 disabled:cursor-not-allowed`}
                  >
                    {isRecording ? <Square className="w-8 h-8" /> : <Mic className="w-8 h-8" />}
                  </button>
                </div>
                
                <div className="space-y-2">
                  <p className="text-lg font-medium text-gray-900 dark:text-white">
                    {isRecording ? 'Recording...' : 'Ready to Record'}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {isRecording 
                      ? 'VocaPilot is listening and providing suggestions'
                      : 'Click the microphone to start your interview session'
                    }
                  </p>
                </div>

                {/* Audio Level Indicator */}
                {isRecording && (
                  <div className="mt-4">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-primary-500 h-2 rounded-full transition-all duration-100"
                        style={{ width: `${audioLevel}%` }}
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Audio Level</p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex justify-center space-x-4 mt-6">
                  <button
                    onClick={handleEndSession}
                    disabled={!sessionStarted && !transcript}
                    className="btn btn-outline disabled:opacity-50"
                  >
                    End Session
                  </button>
                  
                  <button
                    onClick={() => navigate('/history')}
                    className="btn btn-ghost"
                  >
                    View History
                  </button>
                </div>
              </div>
            </div>

            {/* Live Transcript */}
            <div className="card p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Live Transcript
                </h3>
                <div className="flex items-center space-x-2">
                  <MessageSquare className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-500">
                    {selectedLanguage.toUpperCase()}
                  </span>
                </div>
              </div>
              
              <div 
                ref={transcriptRef}
                className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 h-64 overflow-y-auto"
              >
                {transcript ? (
                  <p className="text-gray-900 dark:text-white whitespace-pre-wrap">
                    {transcript}
                  </p>
                ) : (
                  <p className="text-gray-500 italic">
                    Transcript will appear here as you speak...
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Advanced Features Tabs */}
            <div className="card p-4">
              <div className="flex space-x-2 mb-4">
                <button
                  onClick={() => setActiveTab('suggestions')}
                  className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === 'suggestions'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  💡 AI Suggestions
                </button>
                <button
                  onClick={() => setActiveTab('persona')}
                  className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === 'persona'
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  🎭 Persona
                </button>
                <button
                  onClick={() => setActiveTab('jobfit')}
                  className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === 'jobfit'
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  🎯 Job Fit
                </button>
                <button
                  onClick={() => setShowTimeTravelReplay(true)}
                  className="px-3 py-1 rounded-lg text-sm font-medium bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 transition-all"
                >
                  🕰️ Replay
                </button>
              </div>

              {/* Tab Content */}
              {activeTab === 'suggestions' && (
                <LiveSuggestions
                  suggestions={suggestions}
                  isRecording={isRecording}
                />
              )}

              {activeTab === 'persona' && (
                <PersonaSimulator />
              )}

              {activeTab === 'jobfit' && (
                <JobFitAnalyzer />
              )}
            </div>

            {/* TTS Controls for AI Responses */}
            {latestSuggestion && showTTSControls && (
              <TTSControls
                text={latestSuggestion}
                language={selectedLanguage}
                voiceType="female"
                showSettings={false}
                className="border-blue-200 bg-blue-50"
              />
            )}

            {/* Grammar Feedback */}
            {showGrammarFeedback && transcript && (
              <GrammarFeedback
                text={transcript}
                language={selectedLanguage}
                isRealTime={true}
              />
            )}

            {/* Tone Analysis */}
            {toneAnalysis && (
              <div className="card p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <TrendingUp className="w-5 h-5 text-green-500" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Speech Analysis
                  </h3>
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Pace</span>
                    <span className={`text-sm font-medium ${
                      toneAnalysis.pace === 'normal' ? 'text-green-600' : 'text-yellow-600'
                    }`}>
                      {toneAnalysis.pace}
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Clarity</span>
                    <span className={`text-sm font-medium ${
                      toneAnalysis.clarity === 'clear' ? 'text-green-600' : 'text-yellow-600'
                    }`}>
                      {toneAnalysis.clarity}
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Confidence</span>
                    <span className={`text-sm font-medium ${
                      toneAnalysis.emotion === 'confident' ? 'text-green-600' : 'text-blue-600'
                    }`}>
                      {toneAnalysis.emotion}
                    </span>
                  </div>
                  
                  {toneAnalysis.recommendations && toneAnalysis.recommendations.length > 0 && (
                    <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                      <p className="text-xs font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                        Recommendations:
                      </p>
                      <ul className="text-xs text-yellow-700 dark:text-yellow-300 space-y-1">
                        {toneAnalysis.recommendations.slice(0, 2).map((rec, index) => (
                          <li key={index}>• {rec}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Session Info */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Session Info
              </h3>
              
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Language</span>
                  <span className="font-medium">{selectedLanguage.toUpperCase()}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Duration</span>
                  <span className="font-medium">{formatDuration(sessionDuration)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Suggestions</span>
                  <span className="font-medium">{suggestions.length}</span>
                </div>
                
                {jobDescription && (
                  <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                    <p className="text-gray-600 dark:text-gray-400 mb-2">Job Context:</p>
                    <p className="text-xs text-gray-500 line-clamp-3">
                      {jobDescription}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Time Travel Replay Modal */}
      <TimeTravelReplay
        isOpen={showTimeTravelReplay}
        onClose={() => setShowTimeTravelReplay(false)}
      />
    </div>
  )
}

export default LiveCopilot
