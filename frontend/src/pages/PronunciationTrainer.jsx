import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  <PERSON>,
  Mic,
  MicOff,
  RotateCcw,
  Star,
  Target,
  Volume2,
  ChevronRight,
  Award,
  TrendingUp
} from 'lucide-react'
import { useAppStore } from '../store/appStore'
import { useAudioRecording } from '../hooks/useAudioRecording'
import { usePronunciationAnalysis } from '../hooks/usePronunciationAnalysis'
import { useTTS } from '../hooks/useTTS'
import TTSControls from '../components/TTSControls'
import toast from 'react-hot-toast'

const PronunciationTrainer = () => {
  const { selectedLanguage } = useAppStore()
  const { startRecording, stopRecording, isRecording } = useAudioRecording()
  const {
    pronunciationScore,
    accuracyScore,
    fluencyScore,
    feedback,
    improvements,
    isAnalyzing,
    analyzePronunciation,
    getPracticeSentences,
    getScoreColor,
    getScoreRating,
    clearAnalysis
  } = usePronunciationAnalysis()

  const [currentSentence, setCurrentSentence] = useState('')
  const [spokenText, setSpokenText] = useState('')
  const [practiceSentences, setPracticeSentences] = useState([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [difficulty, setDifficulty] = useState('medium')
  const [sessionStats, setSessionStats] = useState({
    completed: 0,
    averageScore: 0,
    bestScore: 0
  })
  const [showResults, setShowResults] = useState(false)
  const [isLoadingSentences, setIsLoadingSentences] = useState(false)

  // Load practice sentences on mount
  useEffect(() => {
    loadPracticeSentences()
  }, [selectedLanguage, difficulty])

  const loadPracticeSentences = async () => {
    setIsLoadingSentences(true)
    try {
      const sentences = await getPracticeSentences(selectedLanguage, difficulty, 10)
      setPracticeSentences(sentences)
      if (sentences.length > 0) {
        setCurrentSentence(sentences[0])
        setCurrentIndex(0)
      }
    } catch (error) {
      toast.error('Failed to load practice sentences')
    } finally {
      setIsLoadingSentences(false)
    }
  }

  const handleStartRecording = async () => {
    try {
      clearAnalysis()
      setSpokenText('')
      setShowResults(false)
      await startRecording()
      toast.success('Recording started - speak the sentence clearly')
    } catch (error) {
      toast.error('Failed to start recording')
    }
  }

  const handleStopRecording = async () => {
    try {
      await stopRecording()
      // Simulate getting transcribed text (in real implementation, this would come from the recording hook)
      const mockTranscript = "This is a mock transcript for demonstration"
      setSpokenText(mockTranscript)
      
      // Analyze pronunciation
      if (mockTranscript && currentSentence) {
        await analyzePronunciation(mockTranscript, currentSentence, selectedLanguage)
        setShowResults(true)
        
        // Update session stats
        setSessionStats(prev => ({
          completed: prev.completed + 1,
          averageScore: Math.round((prev.averageScore * prev.completed + pronunciationScore) / (prev.completed + 1)),
          bestScore: Math.max(prev.bestScore, pronunciationScore)
        }))
      }
    } catch (error) {
      toast.error('Failed to stop recording')
    }
  }

  const handleNextSentence = () => {
    if (currentIndex < practiceSentences.length - 1) {
      const nextIndex = currentIndex + 1
      setCurrentIndex(nextIndex)
      setCurrentSentence(practiceSentences[nextIndex])
      setSpokenText('')
      setShowResults(false)
      clearAnalysis()
    } else {
      // End of session
      toast.success('Session completed! Great job!')
    }
  }

  const handleRetry = () => {
    setSpokenText('')
    setShowResults(false)
    clearAnalysis()
  }

  const getScoreStars = (score) => {
    const stars = Math.round(score / 20) // Convert 0-100 to 0-5 stars
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-5 h-5 ${
          i < stars ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ))
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-6">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Pronunciation Trainer
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Practice pronunciation with AI-powered feedback
          </p>
        </div>

        {/* Settings */}
        <div className="card p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Practice Settings
            </h2>
            <div className="flex items-center space-x-4">
              <select
                value={difficulty}
                onChange={(e) => setDifficulty(e.target.value)}
                className="input"
              >
                <option value="easy">Easy</option>
                <option value="medium">Medium</option>
                <option value="hard">Hard</option>
              </select>
              <button
                onClick={loadPracticeSentences}
                disabled={isLoadingSentences}
                className="btn btn-outline"
              >
                {isLoadingSentences ? 'Loading...' : 'New Sentences'}
              </button>
            </div>
          </div>

          {/* Progress */}
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
            <span>Sentence {currentIndex + 1} of {practiceSentences.length}</span>
            <span>Completed: {sessionStats.completed}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentIndex + 1) / practiceSentences.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Current Sentence */}
        <div className="card p-8 mb-6 text-center">
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
              Practice Sentence
            </h3>
            <p className="text-2xl font-medium text-gray-900 dark:text-white leading-relaxed">
              {currentSentence}
            </p>
          </div>

          {/* TTS Controls for Reference */}
          <div className="mb-6">
            <TTSControls
              text={currentSentence}
              language={selectedLanguage}
              voiceType="female"
              showSettings={false}
              className="max-w-md mx-auto"
            />
          </div>

          {/* Recording Controls */}
          <div className="flex items-center justify-center space-x-4">
            <button
              onClick={isRecording ? handleStopRecording : handleStartRecording}
              disabled={!currentSentence || isAnalyzing}
              className={`w-16 h-16 rounded-full flex items-center justify-center text-white text-xl transition-all ${
                isRecording
                  ? 'bg-red-500 hover:bg-red-600 animate-pulse'
                  : 'bg-blue-500 hover:bg-blue-600'
              } disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              {isRecording ? <MicOff className="w-6 h-6" /> : <Mic className="w-6 h-6" />}
            </button>

            {spokenText && (
              <button
                onClick={handleRetry}
                className="btn btn-outline"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Retry
              </button>
            )}
          </div>

          <p className="text-sm text-gray-500 mt-4">
            {isRecording 
              ? 'Recording... Speak the sentence clearly'
              : isAnalyzing
                ? 'Analyzing your pronunciation...'
                : 'Click the microphone to start recording'
            }
          </p>
        </div>

        {/* Results */}
        <AnimatePresence>
          {showResults && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Score Overview */}
              <div className="card p-6">
                <div className="text-center mb-6">
                  <div className="flex items-center justify-center space-x-1 mb-2">
                    {getScoreStars(pronunciationScore)}
                  </div>
                  <div className={`text-4xl font-bold ${getScoreColor(pronunciationScore)}`}>
                    {Math.round(pronunciationScore)}%
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    {getScoreRating(pronunciationScore)}
                  </p>
                </div>

                {/* Detailed Scores */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className={`text-2xl font-bold ${getScoreColor(accuracyScore)}`}>
                      {Math.round(accuracyScore)}%
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Accuracy</p>
                  </div>
                  <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className={`text-2xl font-bold ${getScoreColor(fluencyScore)}`}>
                      {Math.round(fluencyScore)}%
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Fluency</p>
                  </div>
                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {sessionStats.completed}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Completed</p>
                  </div>
                </div>
              </div>

              {/* Feedback */}
              {feedback.length > 0 && (
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Feedback
                  </h3>
                  <div className="space-y-3">
                    {feedback.map((item, index) => (
                      <div
                        key={index}
                        className={`p-3 rounded-lg border ${
                          item.type === 'success' ? 'bg-green-50 border-green-200' :
                          item.type === 'warning' ? 'bg-yellow-50 border-yellow-200' :
                          'bg-blue-50 border-blue-200'
                        }`}
                      >
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          {item.message}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Improvements */}
              {improvements.length > 0 && (
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Improvement Tips
                  </h3>
                  <div className="space-y-3">
                    {improvements.map((tip, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <TrendingUp className="w-5 h-5 text-blue-500 mt-0.5" />
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {tip.title}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {tip.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Next Button */}
              <div className="text-center">
                <button
                  onClick={handleNextSentence}
                  disabled={currentIndex >= practiceSentences.length - 1}
                  className="btn btn-primary"
                >
                  {currentIndex >= practiceSentences.length - 1 ? (
                    <>
                      <Award className="w-4 h-4 mr-2" />
                      Session Complete
                    </>
                  ) : (
                    <>
                      Next Sentence
                      <ChevronRight className="w-4 h-4 ml-2" />
                    </>
                  )}
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Session Stats */}
        {sessionStats.completed > 0 && (
          <div className="card p-6 mt-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Session Statistics
            </h3>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {sessionStats.completed}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Completed</p>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {sessionStats.averageScore}%
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Average</p>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {sessionStats.bestScore}%
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Best Score</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default PronunciationTrainer
