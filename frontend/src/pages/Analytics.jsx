import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js'
import { Line, Bar, Doughnut } from 'react-chartjs-2'
import {
  TrendingUp,
  Calendar,
  Clock,
  Target,
  Award,
  Download,
  RefreshCw,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react'
import { useAppStore } from '../store/appStore'
import { useAnalyticsStore } from '../store/analyticsStore'
import toast from 'react-hot-toast'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
)

const Analytics = () => {
  const { userName } = useAppStore()
  const {
    userStats,
    progressData,
    weeklySummary,
    insights,
    isLoadingDashboard,
    dashboardError,
    selectedTimeframe,
    setSelectedTimeframe,
    loadDashboard,
    exportAnalytics,
    getAnalyticsSummary,
    getProgressTrend
  } = useAnalyticsStore()

  const [activeTab, setActiveTab] = useState('overview')
  const [chartType, setChartType] = useState('line')

  // Load dashboard data on mount
  useEffect(() => {
    if (userName) {
      loadDashboard(userName, selectedTimeframe)
    }
  }, [userName, selectedTimeframe, loadDashboard])

  const handleRefresh = async () => {
    if (userName) {
      await loadDashboard(userName, selectedTimeframe)
      toast.success('Analytics refreshed')
    }
  }

  const handleExport = async (format) => {
    if (userName) {
      const success = await exportAnalytics(userName, format, selectedTimeframe)
      if (success) {
        toast.success(`Analytics exported as ${format.toUpperCase()}`)
      } else {
        toast.error('Export failed')
      }
    }
  }

  const summary = getAnalyticsSummary()
  const trend = getProgressTrend()

  // Chart configurations
  const progressChartData = {
    labels: progressData.map(day => new Date(day.date).toLocaleDateString()),
    datasets: [
      {
        label: 'Confidence Score',
        data: progressData.map(day => day.averageConfidence),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true
      },
      {
        label: 'Grammar Score',
        data: progressData.map(day => day.grammarScore),
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
        fill: true
      },
      {
        label: 'Fluency Score',
        data: progressData.map(day => day.fluencyScore),
        borderColor: 'rgb(168, 85, 247)',
        backgroundColor: 'rgba(168, 85, 247, 0.1)',
        tension: 0.4,
        fill: true
      }
    ]
  }

  const sessionTypeData = {
    labels: userStats?.sessionTypes?.map(type => type.type) || [],
    datasets: [
      {
        data: userStats?.sessionTypes?.map(type => type.count) || [],
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(168, 85, 247, 0.8)',
          'rgba(251, 191, 36, 0.8)'
        ],
        borderWidth: 0
      }
    ]
  }

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top'
      },
      title: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100
      }
    }
  }

  if (!userName) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Analytics Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Please complete setup to view your analytics
            </p>
          </div>
        </div>
      </div>
    )
  }

  if (isLoadingDashboard) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-300">Loading analytics...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (dashboardError) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Analytics Dashboard
            </h1>
            <div className="card p-6 max-w-md mx-auto">
              <p className="text-red-600 mb-4">{dashboardError}</p>
              <button
                onClick={handleRefresh}
                className="btn btn-primary"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Analytics Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Track your progress and improve your speaking skills
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Timeframe Selector */}
              <select
                value={selectedTimeframe}
                onChange={(e) => setSelectedTimeframe(e.target.value)}
                className="input"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
              </select>
              
              {/* Refresh Button */}
              <button
                onClick={handleRefresh}
                className="btn btn-ghost p-2"
                title="Refresh data"
              >
                <RefreshCw className="w-5 h-5" />
              </button>
              
              {/* Export Dropdown */}
              <div className="relative group">
                <button className="btn btn-outline">
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </button>
                <div className="absolute right-0 mt-2 w-32 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
                  <button
                    onClick={() => handleExport('json')}
                    className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    JSON
                  </button>
                  <button
                    onClick={() => handleExport('csv')}
                    className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    CSV
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        {summary && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="card p-6"
            >
              <div className="flex items-center">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <Activity className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Sessions
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {summary.totalSessions}
                  </p>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="card p-6"
            >
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-lg">
                  <Clock className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Time
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {Math.round(summary.totalTime / 60)}m
                  </p>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="card p-6"
            >
              <div className="flex items-center">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <Target className="w-6 h-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Avg Confidence
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {Math.round(summary.averageConfidence)}%
                  </p>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="card p-6"
            >
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${
                  trend.trend === 'improving' ? 'bg-green-100' :
                  trend.trend === 'declining' ? 'bg-red-100' : 'bg-yellow-100'
                }`}>
                  <TrendingUp className={`w-6 h-6 ${
                    trend.trend === 'improving' ? 'text-green-600' :
                    trend.trend === 'declining' ? 'text-red-600' : 'text-yellow-600'
                  }`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Progress
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white capitalize">
                    {trend.trend}
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Progress Chart */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="card p-6"
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Progress Over Time
                </h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setChartType('line')}
                    className={`p-2 rounded ${chartType === 'line' ? 'bg-blue-100 text-blue-600' : 'text-gray-400'}`}
                  >
                    <BarChart3 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setChartType('bar')}
                    className={`p-2 rounded ${chartType === 'bar' ? 'bg-blue-100 text-blue-600' : 'text-gray-400'}`}
                  >
                    <PieChart className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div className="h-80">
                {progressData.length > 0 ? (
                  chartType === 'line' ? (
                    <Line data={progressChartData} options={chartOptions} />
                  ) : (
                    <Bar data={progressChartData} options={chartOptions} />
                  )
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    No data available for the selected timeframe
                  </div>
                )}
              </div>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Session Types */}
            {userStats?.sessionTypes && userStats.sessionTypes.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="card p-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Session Types
                </h3>
                <div className="h-48">
                  <Doughnut data={sessionTypeData} />
                </div>
              </motion.div>
            )}

            {/* Weekly Summary */}
            {weeklySummary && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="card p-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  This Week
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Sessions</span>
                    <span className="font-medium">{weeklySummary.sessionsThisWeek}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Time</span>
                    <span className="font-medium">{Math.round(weeklySummary.totalTimeThisWeek / 60)}m</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Confidence</span>
                    <span className="font-medium">{Math.round(weeklySummary.averageConfidenceThisWeek)}%</span>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Insights */}
            {insights && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="card p-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Insights
                </h3>
                <div className="space-y-3">
                  {insights.insights?.slice(0, 3).map((insight, index) => (
                    <div key={index} className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-sm text-blue-700 dark:text-blue-300">{insight}</p>
                    </div>
                  ))}
                </div>
                
                {insights.recommendations && insights.recommendations.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                      Recommendations
                    </h4>
                    <div className="space-y-2">
                      {insights.recommendations.slice(0, 2).map((rec, index) => (
                        <div key={index} className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                          <p className="text-sm text-green-700 dark:text-green-300">{rec}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Analytics
