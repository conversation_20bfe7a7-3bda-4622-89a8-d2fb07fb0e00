import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Play, 
  Pause, 
  Square, 
  Volume2, 
  VolumeX, 
  Settings,
  Loader,
  AlertCircle
} from 'lucide-react'
import { useTTS } from '../hooks/useTTS'

const TTSControls = ({ 
  text, 
  language = 'en', 
  voiceType = 'female',
  speed = 1.0,
  autoPlay = false,
  showSettings = true,
  onPlayStart = null,
  onPlayEnd = null,
  onError = null,
  className = ''
}) => {
  const {
    isPlaying,
    isLoading,
    error,
    supportedVoices,
    speak,
    stop,
    pause,
    resume,
    initializeTTS,
    setVolume,
    getCurrentTime,
    getDuration
  } = useTTS()

  const [showAdvanced, setShowAdvanced] = useState(false)
  const [localSettings, setLocalSettings] = useState({
    language,
    voiceType,
    speed,
    volume: 1.0
  })
  const [progress, setProgress] = useState(0)

  // Initialize TTS on mount
  useEffect(() => {
    initializeTTS()
  }, [initializeTTS])

  // Update progress
  useEffect(() => {
    if (isPlaying) {
      const interval = setInterval(() => {
        const current = getCurrentTime()
        const duration = getDuration()
        if (duration > 0) {
          setProgress((current / duration) * 100)
        }
      }, 100)

      return () => clearInterval(interval)
    } else {
      setProgress(0)
    }
  }, [isPlaying, getCurrentTime, getDuration])

  // Handle play/pause events
  useEffect(() => {
    if (isPlaying && onPlayStart) {
      onPlayStart()
    } else if (!isPlaying && !isLoading && onPlayEnd) {
      onPlayEnd()
    }
  }, [isPlaying, isLoading, onPlayStart, onPlayEnd])

  // Handle errors
  useEffect(() => {
    if (error && onError) {
      onError(error)
    }
  }, [error, onError])

  // Auto-play when text changes
  useEffect(() => {
    if (autoPlay && text && text.trim().length > 0) {
      handlePlay()
    }
  }, [text, autoPlay])

  const handlePlay = async () => {
    if (!text || text.trim().length === 0) return

    const success = await speak(text, {
      language: localSettings.language,
      voiceType: localSettings.voiceType,
      speed: localSettings.speed
    })

    if (!success && onError) {
      onError('Failed to play audio')
    }
  }

  const handlePause = () => {
    if (isPlaying) {
      pause()
    } else {
      resume()
    }
  }

  const handleStop = () => {
    stop()
    setProgress(0)
  }

  const handleVolumeChange = (newVolume) => {
    setLocalSettings(prev => ({ ...prev, volume: newVolume }))
    setVolume(newVolume)
  }

  const handleSettingChange = (setting, value) => {
    setLocalSettings(prev => ({ ...prev, [setting]: value }))
  }

  const getVoiceOptions = () => {
    const voices = supportedVoices[localSettings.language] || {}
    return Object.keys(voices).map(type => ({
      value: type,
      label: type.charAt(0).toUpperCase() + type.slice(1)
    }))
  }

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg ${className}`}
      >
        <AlertCircle className="w-5 h-5 text-red-500" />
        <span className="text-red-700 text-sm">{error}</span>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 ${className}`}
    >
      {/* Main Controls */}
      <div className="flex items-center space-x-3">
        {/* Play/Pause Button */}
        <button
          onClick={isPlaying ? handlePause : handlePlay}
          disabled={isLoading || !text}
          className={`w-12 h-12 rounded-full flex items-center justify-center transition-all ${
            isLoading 
              ? 'bg-gray-100 cursor-not-allowed' 
              : isPlaying
                ? 'bg-blue-500 hover:bg-blue-600 text-white'
                : 'bg-green-500 hover:bg-green-600 text-white'
          } disabled:opacity-50`}
        >
          {isLoading ? (
            <Loader className="w-5 h-5 animate-spin" />
          ) : isPlaying ? (
            <Pause className="w-5 h-5" />
          ) : (
            <Play className="w-5 h-5 ml-0.5" />
          )}
        </button>

        {/* Stop Button */}
        <button
          onClick={handleStop}
          disabled={!isPlaying && !isLoading}
          className="w-10 h-10 rounded-full flex items-center justify-center bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
        >
          <Square className="w-4 h-4 text-gray-600" />
        </button>

        {/* Progress Bar */}
        <div className="flex-1 mx-4">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              className="bg-blue-500 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.1 }}
            />
          </div>
          {isPlaying && (
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>{Math.round(getCurrentTime())}s</span>
              <span>{Math.round(getDuration())}s</span>
            </div>
          )}
        </div>

        {/* Volume Control */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleVolumeChange(localSettings.volume > 0 ? 0 : 1)}
            className="p-2 rounded-full hover:bg-gray-100 transition-all"
          >
            {localSettings.volume > 0 ? (
              <Volume2 className="w-4 h-4 text-gray-600" />
            ) : (
              <VolumeX className="w-4 h-4 text-gray-600" />
            )}
          </button>
          
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={localSettings.volume}
            onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
            className="w-16 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>

        {/* Settings Button */}
        {showSettings && (
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="p-2 rounded-full hover:bg-gray-100 transition-all"
          >
            <Settings className="w-4 h-4 text-gray-600" />
          </button>
        )}
      </div>

      {/* Advanced Settings */}
      {showAdvanced && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Language Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Language
              </label>
              <select
                value={localSettings.language}
                onChange={(e) => handleSettingChange('language', e.target.value)}
                className="w-full input text-sm"
              >
                <option value="en">English</option>
                <option value="fr">French</option>
                <option value="es">Spanish</option>
                <option value="de">German</option>
              </select>
            </div>

            {/* Voice Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Voice
              </label>
              <select
                value={localSettings.voiceType}
                onChange={(e) => handleSettingChange('voiceType', e.target.value)}
                className="w-full input text-sm"
              >
                {getVoiceOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Speed Control */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Speed: {localSettings.speed}x
              </label>
              <input
                type="range"
                min="0.5"
                max="2.0"
                step="0.1"
                value={localSettings.speed}
                onChange={(e) => handleSettingChange('speed', parseFloat(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
            </div>
          </div>

          {/* Text Preview */}
          {text && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Text to Speak
              </label>
              <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg text-sm text-gray-700 dark:text-gray-300 max-h-20 overflow-y-auto">
                {text.length > 200 ? `${text.substring(0, 200)}...` : text}
              </div>
            </div>
          )}
        </motion.div>
      )}

      {/* Status Indicator */}
      {(isLoading || isPlaying) && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mt-3 flex items-center space-x-2 text-sm text-blue-600"
        >
          {isLoading && (
            <>
              <Loader className="w-4 h-4 animate-spin" />
              <span>Generating speech...</span>
            </>
          )}
          {isPlaying && !isLoading && (
            <>
              <div className="w-4 h-4 flex items-center justify-center">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              </div>
              <span>Playing audio</span>
            </>
          )}
        </motion.div>
      )}
    </motion.div>
  )
}

export default TTSControls
