import React, { useRef, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppStore } from '../../store/appStore';
import * as THREE from 'three';

const ARCopilotPanel = () => {
  const { arMode, updateARMode, suggestions, neuroAI, isRecording } = useAppStore();
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const rendererRef = useRef(null);
  const sceneRef = useRef(null);
  const cameraRef = useRef(null);
  const [isARSupported, setIsARSupported] = useState(false);
  const [arSession, setARSession] = useState(null);
  const [holographicElements, setHolographicElements] = useState([]);

  useEffect(() => {
    checkARSupport();
  }, []);

  useEffect(() => {
    if (arMode.enabled && isARSupported) {
      initializeAR();
    } else {
      cleanupAR();
    }
  }, [arMode.enabled, isARSupported]);

  const checkARSupport = async () => {
    if ('xr' in navigator) {
      try {
        const supported = await navigator.xr.isSessionSupported('immersive-ar');
        setIsARSupported(supported);
      } catch (error) {
        console.log('AR not supported:', error);
        // Fallback to WebRTC-based AR simulation
        setIsARSupported(true);
      }
    } else {
      // Fallback for browsers without WebXR
      setIsARSupported(true);
    }
  };

  const initializeAR = async () => {
    try {
      if ('xr' in navigator && await navigator.xr.isSessionSupported('immersive-ar')) {
        await initializeWebXR();
      } else {
        await initializeWebRTCAR();
      }
    } catch (error) {
      console.error('Failed to initialize AR:', error);
      await initializeWebRTCAR(); // Fallback
    }
  };

  const initializeWebXR = async () => {
    try {
      const session = await navigator.xr.requestSession('immersive-ar', {
        requiredFeatures: ['local'],
        optionalFeatures: ['dom-overlay'],
        domOverlay: { root: document.body }
      });

      setARSession(session);
      
      // Initialize Three.js for WebXR
      const renderer = new THREE.WebGLRenderer({ 
        canvas: canvasRef.current,
        alpha: true,
        antialias: true 
      });
      renderer.setSize(window.innerWidth, window.innerHeight);
      renderer.xr.enabled = true;
      renderer.xr.setSession(session);
      
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
      
      rendererRef.current = renderer;
      sceneRef.current = scene;
      cameraRef.current = camera;
      
      // Add holographic elements
      createHolographicElements();
      
      // Start render loop
      renderer.setAnimationLoop(renderAR);
      
    } catch (error) {
      console.error('WebXR initialization failed:', error);
      throw error;
    }
  };

  const initializeWebRTCAR = async () => {
    try {
      // Get camera stream
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { 
          facingMode: 'user',
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });
      
      videoRef.current.srcObject = stream;
      
      // Initialize Three.js overlay
      const renderer = new THREE.WebGLRenderer({ 
        canvas: canvasRef.current,
        alpha: true,
        antialias: true 
      });
      renderer.setSize(window.innerWidth, window.innerHeight);
      renderer.setClearColor(0x000000, 0); // Transparent background
      
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
      camera.position.z = 5;
      
      rendererRef.current = renderer;
      sceneRef.current = scene;
      cameraRef.current = camera;
      
      // Create holographic elements
      createHolographicElements();
      
      // Start render loop
      const animate = () => {
        requestAnimationFrame(animate);
        renderAR();
      };
      animate();
      
    } catch (error) {
      console.error('WebRTC AR initialization failed:', error);
    }
  };

  const createHolographicElements = () => {
    if (!sceneRef.current) return;
    
    const scene = sceneRef.current;
    
    // Clear existing elements
    holographicElements.forEach(element => {
      scene.remove(element);
    });
    
    const newElements = [];
    
    // AI Suggestion Cards
    suggestions.slice(0, 3).forEach((suggestion, index) => {
      const cardGeometry = new THREE.PlaneGeometry(2, 1);
      const cardMaterial = new THREE.MeshBasicMaterial({
        color: 0x3B82F6,
        transparent: true,
        opacity: 0.8,
        side: THREE.DoubleSide
      });
      
      const card = new THREE.Mesh(cardGeometry, cardMaterial);
      card.position.set(-3 + index * 3, 2 - index * 0.5, -2);
      card.userData = { type: 'suggestion', data: suggestion };
      
      // Add text texture (simplified)
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      canvas.width = 512;
      canvas.height = 256;
      
      context.fillStyle = '#1E40AF';
      context.fillRect(0, 0, canvas.width, canvas.height);
      
      context.fillStyle = 'white';
      context.font = '24px Arial';
      context.textAlign = 'center';
      context.fillText(suggestion.text.substring(0, 50) + '...', canvas.width / 2, canvas.height / 2);
      
      const texture = new THREE.CanvasTexture(canvas);
      card.material.map = texture;
      
      scene.add(card);
      newElements.push(card);
    });
    
    // Stress Level Indicator
    const stressGeometry = new THREE.RingGeometry(0.5, 0.7, 32);
    const stressColor = neuroAI.stressLevel > 0.7 ? 0xFF4444 : 
                      neuroAI.stressLevel > 0.4 ? 0xFFAA44 : 0x44FF44;
    const stressMaterial = new THREE.MeshBasicMaterial({
      color: stressColor,
      transparent: true,
      opacity: 0.6
    });
    
    const stressIndicator = new THREE.Mesh(stressGeometry, stressMaterial);
    stressIndicator.position.set(3, 3, -1);
    stressIndicator.userData = { type: 'stress', level: neuroAI.stressLevel };
    
    scene.add(stressIndicator);
    newElements.push(stressIndicator);
    
    // Confidence Meter
    const confidenceGeometry = new THREE.BoxGeometry(0.2, 2, 0.1);
    const confidenceMaterial = new THREE.MeshBasicMaterial({
      color: 0x00FF88,
      transparent: true,
      opacity: 0.8
    });
    
    const confidenceMeter = new THREE.Mesh(confidenceGeometry, confidenceMaterial);
    confidenceMeter.position.set(-3, 0, -1);
    confidenceMeter.scale.y = neuroAI.confidenceLevel;
    confidenceMeter.userData = { type: 'confidence', level: neuroAI.confidenceLevel };
    
    scene.add(confidenceMeter);
    newElements.push(confidenceMeter);
    
    // Timer Display
    if (isRecording) {
      const timerGeometry = new THREE.PlaneGeometry(1.5, 0.5);
      const timerMaterial = new THREE.MeshBasicMaterial({
        color: 0x8B5CF6,
        transparent: true,
        opacity: 0.9
      });
      
      const timer = new THREE.Mesh(timerGeometry, timerMaterial);
      timer.position.set(0, -3, -1);
      timer.userData = { type: 'timer' };
      
      scene.add(timer);
      newElements.push(timer);
    }
    
    setHolographicElements(newElements);
  };

  const renderAR = () => {
    if (!rendererRef.current || !sceneRef.current || !cameraRef.current) return;
    
    // Update holographic elements
    holographicElements.forEach(element => {
      // Add floating animation
      element.position.y += Math.sin(Date.now() * 0.001 + element.position.x) * 0.01;
      
      // Rotate suggestion cards
      if (element.userData.type === 'suggestion') {
        element.rotation.y = Math.sin(Date.now() * 0.001) * 0.1;
      }
      
      // Pulse stress indicator
      if (element.userData.type === 'stress') {
        const pulse = 1 + Math.sin(Date.now() * 0.005) * 0.1;
        element.scale.setScalar(pulse);
      }
      
      // Update confidence meter
      if (element.userData.type === 'confidence') {
        element.scale.y = THREE.MathUtils.lerp(element.scale.y, neuroAI.confidenceLevel, 0.1);
      }
    });
    
    rendererRef.current.render(sceneRef.current, cameraRef.current);
  };

  const cleanupAR = () => {
    if (arSession) {
      arSession.end();
      setARSession(null);
    }
    
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = videoRef.current.srcObject.getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    
    if (rendererRef.current) {
      rendererRef.current.dispose();
      rendererRef.current = null;
    }
    
    setHolographicElements([]);
  };

  const toggleAROverlay = () => {
    updateARMode({ overlayVisible: !arMode.overlayVisible });
  };

  const togglePeripheralUI = () => {
    updateARMode({ peripheralUI: !arMode.peripheralUI });
  };

  if (!isARSupported) {
    return (
      <div className="ar-not-supported p-4 bg-yellow-600/20 border border-yellow-600/30 rounded-lg">
        <div className="flex items-center space-x-2 text-yellow-400">
          <span>⚠️</span>
          <span>AR features are not supported on this device/browser</span>
        </div>
      </div>
    );
  }

  return (
    <div className="ar-copilot-panel relative">
      {/* AR Toggle Button */}
      <motion.button
        onClick={() => updateARMode({ enabled: !arMode.enabled })}
        className={`fixed top-4 right-20 z-50 p-3 rounded-full shadow-lg transition-all ${
          arMode.enabled 
            ? 'bg-purple-600 text-white' 
            : 'bg-gray-700 text-gray-300'
        }`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        🥽
      </motion.button>

      {/* AR Controls */}
      <AnimatePresence>
        {arMode.enabled && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            className="fixed top-16 right-4 z-50 bg-gray-900/80 backdrop-blur-sm rounded-lg p-4 space-y-2"
          >
            <button
              onClick={toggleAROverlay}
              className={`w-full p-2 rounded text-sm transition-colors ${
                arMode.overlayVisible 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-700 text-gray-300'
              }`}
            >
              {arMode.overlayVisible ? 'Hide' : 'Show'} Overlay
            </button>
            
            <button
              onClick={togglePeripheralUI}
              className={`w-full p-2 rounded text-sm transition-colors ${
                arMode.peripheralUI 
                  ? 'bg-green-600 text-white' 
                  : 'bg-gray-700 text-gray-300'
              }`}
            >
              Peripheral UI: {arMode.peripheralUI ? 'ON' : 'OFF'}
            </button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* AR Video Feed */}
      {arMode.enabled && (
        <div className="fixed inset-0 z-10">
          <video
            ref={videoRef}
            autoPlay
            muted
            playsInline
            className="w-full h-full object-cover"
          />
          
          {/* Three.js Canvas Overlay */}
          <canvas
            ref={canvasRef}
            className="absolute inset-0 w-full h-full pointer-events-none"
          />
        </div>
      )}

      {/* Peripheral UI Elements */}
      <AnimatePresence>
        {arMode.enabled && arMode.peripheralUI && (
          <>
            {/* Top Peripheral Info */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="fixed top-4 left-1/2 transform -translate-x-1/2 z-40 bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2 text-white text-sm"
            >
              Interview Progress: {isRecording ? 'Active' : 'Paused'} | 
              Confidence: {Math.round(neuroAI.confidenceLevel * 100)}% |
              Stress: {Math.round(neuroAI.stressLevel * 100)}%
            </motion.div>

            {/* Left Peripheral - Quick Tips */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="fixed left-4 top-1/2 transform -translate-y-1/2 z-40 bg-black/50 backdrop-blur-sm rounded-lg p-3 max-w-xs"
            >
              <h3 className="text-white font-semibold mb-2">💡 Quick Tips</h3>
              <div className="space-y-1 text-sm text-gray-300">
                <div>• Maintain eye contact</div>
                <div>• Speak clearly and confidently</div>
                <div>• Use specific examples</div>
              </div>
            </motion.div>

            {/* Right Peripheral - Emotional State */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="fixed right-4 top-1/2 transform -translate-y-1/2 z-40 bg-black/50 backdrop-blur-sm rounded-lg p-3"
            >
              <h3 className="text-white font-semibold mb-2">😊 Emotional State</h3>
              <div className="text-center">
                <div className="text-3xl mb-2">
                  {neuroAI.emotionalState === 'positive' ? '😊' :
                   neuroAI.emotionalState === 'stressed' ? '😰' : '😐'}
                </div>
                <div className="text-sm text-gray-300 capitalize">
                  {neuroAI.emotionalState}
                </div>
              </div>
            </motion.div>

            {/* Bottom Peripheral - Current Suggestion */}
            {suggestions.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-40 bg-black/50 backdrop-blur-sm rounded-lg p-4 max-w-2xl"
              >
                <div className="text-white">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-blue-400">🤖</span>
                    <span className="font-semibold">AI Suggestion</span>
                  </div>
                  <p className="text-sm">{suggestions[0]?.text}</p>
                </div>
              </motion.div>
            )}
          </>
        )}
      </AnimatePresence>

      {/* AR Status Indicator */}
      {arMode.enabled && (
        <div className="fixed bottom-4 right-4 z-50">
          <div className="flex items-center space-x-2 bg-purple-600 text-white px-3 py-1 rounded-full text-sm">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span>AR Active</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ARCopilotPanel;
