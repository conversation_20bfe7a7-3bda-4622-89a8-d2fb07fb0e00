import React, { useRef, useEffect, useState } from 'react';
import { Canvas, use<PERSON>rame, useThree } from '@react-three/fiber';
import { Points, PointMaterial, Sphere } from '@react-three/drei';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppStore } from '../../store/appStore';
import * as THREE from 'three';
import gsap from 'gsap';

// Starfield component
const Starfield = ({ count = 5000 }) => {
  const mesh = useRef();
  const [sphere] = useState(() => new THREE.SphereGeometry(1, 32, 32));
  
  const positions = React.useMemo(() => {
    const positions = new Float32Array(count * 3);
    for (let i = 0; i < count; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 2000;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 2000;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 2000;
    }
    return positions;
  }, [count]);

  useFrame((state, delta) => {
    if (mesh.current) {
      mesh.current.rotation.x -= delta / 10;
      mesh.current.rotation.y -= delta / 15;
    }
  });

  return (
    <group rotation={[0, 0, Math.PI / 4]}>
      <Points ref={mesh} positions={positions} stride={3} frustumCulled={false}>
        <PointMaterial
          transparent
          color="#ffffff"
          size={2}
          sizeAttenuation={true}
          depthWrite={false}
        />
      </Points>
    </group>
  );
};

// Constellation component for performance visualization
const PerformanceConstellation = ({ performance }) => {
  const groupRef = useRef();
  const { neuroAI, jobFitScore } = useAppStore();
  
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = state.clock.elapsedTime * 0.1;
    }
  });

  const constellationPoints = React.useMemo(() => {
    const points = [];
    const radius = 50;
    
    // Create constellation based on performance metrics
    const metrics = [
      { value: neuroAI.confidenceLevel, color: '#3B82F6' },
      { value: 1 - neuroAI.stressLevel, color: '#10B981' },
      { value: jobFitScore.currentScore / 100, color: '#8B5CF6' },
      { value: neuroAI.emotionalState === 'positive' ? 0.8 : 0.4, color: '#F59E0B' }
    ];
    
    metrics.forEach((metric, index) => {
      const angle = (index / metrics.length) * Math.PI * 2;
      const distance = radius * metric.value;
      
      points.push({
        position: [
          Math.cos(angle) * distance,
          Math.sin(angle) * distance,
          0
        ],
        color: metric.color,
        size: 5 + metric.value * 10
      });
    });
    
    return points;
  }, [neuroAI, jobFitScore]);

  return (
    <group ref={groupRef}>
      {constellationPoints.map((point, index) => (
        <mesh key={index} position={point.position}>
          <sphereGeometry args={[point.size, 16, 16]} />
          <meshBasicMaterial color={point.color} transparent opacity={0.8} />
        </mesh>
      ))}
    </group>
  );
};

// Floating AI Avatar
const AIAvatar = () => {
  const meshRef = useRef();
  const { neuroAI } = useAppStore();
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime) * 0.5;
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.2;
      
      // Change color based on emotional state
      const color = neuroAI.emotionalState === 'positive' ? '#10B981' :
                   neuroAI.emotionalState === 'stressed' ? '#EF4444' : '#3B82F6';
      meshRef.current.material.color.set(color);
    }
  });

  return (
    <mesh ref={meshRef} position={[0, 0, 0]}>
      <icosahedronGeometry args={[2, 1]} />
      <meshStandardMaterial 
        color="#3B82F6" 
        transparent 
        opacity={0.8}
        emissive="#1E40AF"
        emissiveIntensity={0.2}
      />
    </mesh>
  );
};

// Voice Waveform Visualization
const VoiceWaveform = ({ audioData = [] }) => {
  const groupRef = useRef();
  
  useFrame(() => {
    if (groupRef.current && audioData.length > 0) {
      groupRef.current.children.forEach((child, index) => {
        if (audioData[index]) {
          child.scale.y = 1 + audioData[index] * 5;
        }
      });
    }
  });

  const bars = React.useMemo(() => {
    return Array.from({ length: 64 }, (_, i) => i);
  }, []);

  return (
    <group ref={groupRef} position={[0, -30, 0]}>
      {bars.map((_, index) => (
        <mesh key={index} position={[(index - 32) * 2, 0, 0]}>
          <boxGeometry args={[1, 2, 1]} />
          <meshStandardMaterial 
            color="#8B5CF6" 
            transparent 
            opacity={0.7}
            emissive="#7C3AED"
            emissiveIntensity={0.3}
          />
        </mesh>
      ))}
    </group>
  );
};

const CelestialDashboard = ({ children }) => {
  const { uiTheme, neuroAI, isRecording } = useAppStore();
  const [audioData, setAudioData] = useState([]);
  const canvasRef = useRef();

  // Audio visualization setup
  useEffect(() => {
    if (isRecording && uiTheme.voiceWaveform) {
      setupAudioVisualization();
    }
  }, [isRecording, uiTheme.voiceWaveform]);

  const setupAudioVisualization = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const audioContext = new AudioContext();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(stream);
      
      analyser.fftSize = 128;
      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      
      microphone.connect(analyser);
      
      const updateAudioData = () => {
        analyser.getByteFrequencyData(dataArray);
        const normalizedData = Array.from(dataArray).map(value => value / 255);
        setAudioData(normalizedData);
        requestAnimationFrame(updateAudioData);
      };
      
      updateAudioData();
    } catch (error) {
      console.error('Failed to setup audio visualization:', error);
    }
  };

  const getThemeColors = () => {
    const themes = {
      celestial: {
        primary: '#1E1B4B',
        secondary: '#312E81',
        accent: '#3B82F6',
        glow: '#60A5FA'
      },
      nebula: {
        primary: '#581C87',
        secondary: '#7C2D92',
        accent: '#A855F7',
        glow: '#C084FC'
      },
      starfield: {
        primary: '#0F172A',
        secondary: '#1E293B',
        accent: '#0EA5E9',
        glow: '#38BDF8'
      }
    };
    
    return themes[uiTheme.mode] || themes.celestial;
  };

  const themeColors = getThemeColors();

  return (
    <div className="relative w-full h-screen overflow-hidden">
      {/* Three.js Background */}
      {uiTheme.starfieldEnabled && (
        <div className="absolute inset-0 z-0">
          <Canvas
            ref={canvasRef}
            camera={{ position: [0, 0, 100], fov: 75 }}
            style={{ background: `linear-gradient(135deg, ${themeColors.primary}, ${themeColors.secondary})` }}
          >
            <ambientLight intensity={0.5} />
            <pointLight position={[10, 10, 10]} />
            
            <Starfield count={3000} />
            <PerformanceConstellation />
            <AIAvatar />
            {uiTheme.voiceWaveform && <VoiceWaveform audioData={audioData} />}
          </Canvas>
        </div>
      )}

      {/* Gradient Overlay */}
      <div 
        className="absolute inset-0 z-10"
        style={{
          background: `radial-gradient(circle at 50% 50%, transparent 0%, ${themeColors.primary}40 100%)`
        }}
      />

      {/* Floating Particles */}
      {uiTheme.animations && (
        <div className="absolute inset-0 z-20 pointer-events-none">
          {Array.from({ length: 20 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full opacity-30"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -100, 0],
                opacity: [0.3, 0.8, 0.3],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      )}

      {/* Glow Effects */}
      {uiTheme.glowEffects && (
        <div className="absolute inset-0 z-30 pointer-events-none">
          <motion.div
            className="absolute top-1/4 left-1/4 w-64 h-64 rounded-full opacity-20"
            style={{
              background: `radial-gradient(circle, ${themeColors.glow}, transparent 70%)`,
              filter: 'blur(40px)',
            }}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
            }}
          />
          
          <motion.div
            className="absolute bottom-1/4 right-1/4 w-48 h-48 rounded-full opacity-15"
            style={{
              background: `radial-gradient(circle, ${themeColors.accent}, transparent 70%)`,
              filter: 'blur(30px)',
            }}
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.15, 0.3, 0.15],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: 1,
            }}
          />
        </div>
      )}

      {/* Performance Indicators */}
      <AnimatePresence>
        {isRecording && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute top-6 left-6 z-40"
          >
            <div className="bg-black/30 backdrop-blur-md rounded-2xl p-4 border border-white/10">
              <div className="flex items-center space-x-4">
                {/* Confidence Orb */}
                <div className="relative">
                  <div 
                    className="w-12 h-12 rounded-full border-2 border-white/20 flex items-center justify-center"
                    style={{
                      background: `conic-gradient(${themeColors.accent} ${neuroAI.confidenceLevel * 360}deg, transparent 0deg)`
                    }}
                  >
                    <div className="w-8 h-8 bg-black/50 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">
                        {Math.round(neuroAI.confidenceLevel * 100)}
                      </span>
                    </div>
                  </div>
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 text-xs text-white/70">
                    Confidence
                  </div>
                </div>

                {/* Stress Level */}
                <div className="relative">
                  <div className="w-12 h-12 rounded-full border-2 border-white/20 flex items-center justify-center overflow-hidden">
                    <motion.div
                      className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-red-500 to-yellow-400"
                      style={{ height: `${neuroAI.stressLevel * 100}%` }}
                      animate={{ height: `${neuroAI.stressLevel * 100}%` }}
                      transition={{ duration: 0.5 }}
                    />
                    <span className="text-white text-xs font-bold relative z-10">
                      {Math.round(neuroAI.stressLevel * 100)}
                    </span>
                  </div>
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 text-xs text-white/70">
                    Stress
                  </div>
                </div>

                {/* Emotional State */}
                <div className="text-center">
                  <div className="w-12 h-12 rounded-full bg-white/10 flex items-center justify-center text-2xl">
                    {neuroAI.emotionalState === 'positive' ? '😊' :
                     neuroAI.emotionalState === 'stressed' ? '😰' : '😐'}
                  </div>
                  <div className="text-xs text-white/70 mt-1 capitalize">
                    {neuroAI.emotionalState}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="relative z-50 h-full">
        {children}
      </div>

      {/* Breathing Animation Overlay */}
      <AnimatePresence>
        {neuroAI.stressLevel > 0.7 && isRecording && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 z-60 flex items-center justify-center pointer-events-none"
          >
            <motion.div
              className="w-32 h-32 rounded-full border-4 border-white/30"
              animate={{
                scale: [1, 1.3, 1],
                opacity: [0.3, 0.8, 0.3],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
              }}
            />
            <motion.div
              className="absolute w-24 h-24 rounded-full border-2 border-white/50"
              animate={{
                scale: [1.3, 1, 1.3],
                opacity: [0.8, 0.3, 0.8],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
              }}
            />
            <div className="absolute text-white text-center">
              <div className="text-lg font-semibold">Breathe</div>
              <div className="text-sm opacity-75">In... and out...</div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CelestialDashboard;
