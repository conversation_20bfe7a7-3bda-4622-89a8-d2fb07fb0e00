import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppStore } from '../../store/appStore';
import * as tf from '@tensorflow/tfjs';
import { FaceDetection } from '@mediapipe/face_mesh';
import { Hands } from '@mediapipe/hands';
import { Camera } from '@mediapipe/camera_utils';

const NeuroAIGuidance = () => {
  const { neuroAI, updateNeuroAI, isRecording } = useAppStore();
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [stressIndicators, setStressIndicators] = useState([]);
  const [breathingData, setBreathingData] = useState([]);
  const [eyeTrackingData, setEyeTrackingData] = useState([]);
  
  // MediaPipe instances
  const faceDetectionRef = useRef(null);
  const handsRef = useRef(null);
  const cameraRef = useRef(null);

  // Initialize MediaPipe and TensorFlow
  useEffect(() => {
    const initializeNeuroAI = async () => {
      try {
        // Initialize TensorFlow.js
        await tf.ready();
        
        // Initialize MediaPipe Face Detection
        const faceDetection = new FaceDetection({
          locateFile: (file) => `https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/${file}`
        });
        
        faceDetection.setOptions({
          maxNumFaces: 1,
          refineLandmarks: true,
          minDetectionConfidence: 0.5,
          minTrackingConfidence: 0.5
        });
        
        faceDetection.onResults(onFaceResults);
        faceDetectionRef.current = faceDetection;
        
        // Initialize MediaPipe Hands
        const hands = new Hands({
          locateFile: (file) => `https://cdn.jsdelivr.net/npm/@mediapipe/hands/${file}`
        });
        
        hands.setOptions({
          maxNumHands: 2,
          modelComplexity: 1,
          minDetectionConfidence: 0.5,
          minTrackingConfidence: 0.5
        });
        
        hands.onResults(onHandsResults);
        handsRef.current = hands;
        
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize NeuroAI:', error);
      }
    };

    initializeNeuroAI();
  }, []);

  // Start camera when recording begins
  useEffect(() => {
    if (isRecording && isInitialized && videoRef.current) {
      startCamera();
    } else if (!isRecording && cameraRef.current) {
      stopCamera();
    }
  }, [isRecording, isInitialized]);

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: 640, height: 480 }
      });
      
      videoRef.current.srcObject = stream;
      
      const camera = new Camera(videoRef.current, {
        onFrame: async () => {
          if (faceDetectionRef.current && handsRef.current) {
            await faceDetectionRef.current.send({ image: videoRef.current });
            await handsRef.current.send({ image: videoRef.current });
          }
        },
        width: 640,
        height: 480
      });
      
      camera.start();
      cameraRef.current = camera;
    } catch (error) {
      console.error('Failed to start camera:', error);
    }
  };

  const stopCamera = () => {
    if (cameraRef.current) {
      cameraRef.current.stop();
      cameraRef.current = null;
    }
    
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = videoRef.current.srcObject.getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
  };

  const onFaceResults = (results) => {
    if (results.multiFaceLandmarks && results.multiFaceLandmarks.length > 0) {
      const landmarks = results.multiFaceLandmarks[0];
      
      // Analyze eye movement patterns
      const leftEye = landmarks.slice(33, 42);
      const rightEye = landmarks.slice(362, 374);
      
      const eyeMovement = analyzeEyeMovement(leftEye, rightEye);
      setEyeTrackingData(prev => [...prev.slice(-19), eyeMovement]);
      
      // Detect stress indicators from facial features
      const stressLevel = detectStressFromFace(landmarks);
      
      // Analyze breathing patterns from facial movement
      const breathingPattern = analyzeBreathingFromFace(landmarks);
      setBreathingData(prev => [...prev.slice(-29), breathingPattern]);
      
      // Update NeuroAI state
      updateNeuroAI({
        stressLevel,
        eyeMovement,
        breathingPace: classifyBreathingPace(breathingData),
        emotionalState: classifyEmotionalState(landmarks),
        confidenceLevel: calculateConfidenceLevel(landmarks, eyeMovement)
      });
    }
  };

  const onHandsResults = (results) => {
    if (results.multiHandLandmarks && results.multiHandLandmarks.length > 0) {
      // Analyze hand gestures for stress indicators
      const handStress = analyzeHandStress(results.multiHandLandmarks);
      setStressIndicators(prev => [...prev.slice(-9), handStress]);
    }
  };

  const analyzeEyeMovement = (leftEye, rightEye) => {
    // Calculate eye aspect ratio and movement patterns
    const leftEAR = calculateEyeAspectRatio(leftEye);
    const rightEAR = calculateEyeAspectRatio(rightEye);
    
    return {
      leftEAR,
      rightEAR,
      blinkRate: (leftEAR + rightEAR) / 2,
      timestamp: Date.now()
    };
  };

  const calculateEyeAspectRatio = (eyeLandmarks) => {
    // Simplified EAR calculation
    const p1 = eyeLandmarks[1];
    const p2 = eyeLandmarks[5];
    const p3 = eyeLandmarks[2];
    const p4 = eyeLandmarks[4];
    const p5 = eyeLandmarks[0];
    const p6 = eyeLandmarks[3];
    
    const A = Math.sqrt(Math.pow(p2.x - p4.x, 2) + Math.pow(p2.y - p4.y, 2));
    const B = Math.sqrt(Math.pow(p3.x - p5.x, 2) + Math.pow(p3.y - p5.y, 2));
    const C = Math.sqrt(Math.pow(p1.x - p6.x, 2) + Math.pow(p1.y - p6.y, 2));
    
    return (A + B) / (2.0 * C);
  };

  const detectStressFromFace = (landmarks) => {
    // Analyze facial tension indicators
    const browFurrow = analyzeBrowFurrow(landmarks);
    const jawTension = analyzeJawTension(landmarks);
    const lipTension = analyzeLipTension(landmarks);
    
    return Math.min(1, (browFurrow + jawTension + lipTension) / 3);
  };

  const analyzeBrowFurrow = (landmarks) => {
    // Simplified brow analysis
    const leftBrow = landmarks.slice(70, 80);
    const rightBrow = landmarks.slice(296, 306);
    
    // Calculate brow position relative to eyes
    const browHeight = (leftBrow[0].y + rightBrow[0].y) / 2;
    const eyeHeight = (landmarks[33].y + landmarks[362].y) / 2;
    
    return Math.max(0, (eyeHeight - browHeight) * 10);
  };

  const analyzeJawTension = (landmarks) => {
    // Analyze jaw clenching
    const jawPoints = landmarks.slice(172, 180);
    const jawWidth = Math.abs(jawPoints[0].x - jawPoints[4].x);
    
    return Math.max(0, Math.min(1, (0.1 - jawWidth) * 20));
  };

  const analyzeLipTension = (landmarks) => {
    // Analyze lip compression
    const upperLip = landmarks.slice(12, 16);
    const lowerLip = landmarks.slice(15, 19);
    
    const lipDistance = Math.abs(upperLip[0].y - lowerLip[0].y);
    return Math.max(0, Math.min(1, (0.02 - lipDistance) * 50));
  };

  const analyzeBreathingFromFace = (landmarks) => {
    // Analyze subtle facial movements for breathing
    const noseBase = landmarks[2];
    const chinTip = landmarks[175];
    
    const faceHeight = Math.abs(noseBase.y - chinTip.y);
    return {
      amplitude: faceHeight,
      timestamp: Date.now()
    };
  };

  const classifyBreathingPace = (breathingData) => {
    if (breathingData.length < 10) return 'normal';
    
    const recentData = breathingData.slice(-10);
    const variance = calculateVariance(recentData.map(d => d.amplitude));
    
    if (variance > 0.001) return 'irregular';
    if (variance < 0.0001) return 'shallow';
    return 'normal';
  };

  const classifyEmotionalState = (landmarks) => {
    const mouthCorners = [landmarks[61], landmarks[291]];
    const mouthCenter = landmarks[13];
    
    const smileIndicator = (mouthCorners[0].y + mouthCorners[1].y) / 2 - mouthCenter.y;
    
    if (smileIndicator > 0.01) return 'positive';
    if (smileIndicator < -0.01) return 'stressed';
    return 'neutral';
  };

  const calculateConfidenceLevel = (landmarks, eyeMovement) => {
    const eyeContact = eyeMovement.blinkRate > 0.2 && eyeMovement.blinkRate < 0.4;
    const posture = landmarks[10].y < landmarks[152].y; // Head up
    
    let confidence = 0.5;
    if (eyeContact) confidence += 0.2;
    if (posture) confidence += 0.2;
    if (neuroAI.stressLevel < 0.3) confidence += 0.1;
    
    return Math.min(1, Math.max(0, confidence));
  };

  const analyzeHandStress = (handLandmarks) => {
    // Analyze hand tremor and fidgeting
    const indexFinger = handLandmarks[0][8];
    const thumb = handLandmarks[0][4];
    
    const distance = Math.sqrt(
      Math.pow(indexFinger.x - thumb.x, 2) + 
      Math.pow(indexFinger.y - thumb.y, 2)
    );
    
    return distance > 0.1 ? 0.8 : 0.2; // High stress if hands are far apart (fidgeting)
  };

  const calculateVariance = (data) => {
    const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
    const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
    return variance;
  };

  const getStressColor = () => {
    if (neuroAI.stressLevel < 0.3) return 'text-green-400';
    if (neuroAI.stressLevel < 0.7) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getConfidenceColor = () => {
    if (neuroAI.confidenceLevel > 0.7) return 'text-green-400';
    if (neuroAI.confidenceLevel > 0.4) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className="neuro-ai-guidance">
      {/* Hidden video element for processing */}
      <video
        ref={videoRef}
        className="hidden"
        autoPlay
        muted
        playsInline
      />
      
      {/* Hidden canvas for processing */}
      <canvas
        ref={canvasRef}
        className="hidden"
        width={640}
        height={480}
      />
      
      {/* NeuroAI Status Display */}
      <AnimatePresence>
        {isRecording && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed top-4 right-4 bg-gray-900/80 backdrop-blur-sm rounded-lg p-4 text-white z-50"
          >
            <div className="flex items-center space-x-4">
              <div className="flex flex-col space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                  <span className="text-sm">NeuroAI Active</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-xs">Stress:</span>
                  <span className={`text-xs font-bold ${getStressColor()}`}>
                    {Math.round(neuroAI.stressLevel * 100)}%
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-xs">Confidence:</span>
                  <span className={`text-xs font-bold ${getConfidenceColor()}`}>
                    {Math.round(neuroAI.confidenceLevel * 100)}%
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-xs">Breathing:</span>
                  <span className="text-xs">{neuroAI.breathingPace}</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-xs">Emotion:</span>
                  <span className="text-xs capitalize">{neuroAI.emotionalState}</span>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Stress Relief Suggestions */}
      <AnimatePresence>
        {neuroAI.stressLevel > 0.6 && isRecording && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            className="fixed bottom-4 right-4 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-4 text-white max-w-sm z-50"
          >
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-lg">🧘</span>
              <span className="font-semibold">Stress Detected</span>
            </div>
            <p className="text-sm mb-3">
              Take a deep breath. You're doing great! Try to relax your shoulders and maintain eye contact.
            </p>
            <motion.div
              className="w-full h-1 bg-white/20 rounded-full overflow-hidden"
              initial={{ width: 0 }}
              animate={{ width: '100%' }}
              transition={{ duration: 4 }}
            >
              <motion.div
                className="h-full bg-white rounded-full"
                initial={{ width: 0 }}
                animate={{ width: '100%' }}
                transition={{ duration: 4 }}
              />
            </motion.div>
            <p className="text-xs mt-1 opacity-75">Breathing exercise - 4 seconds</p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default NeuroAIGuidance;
