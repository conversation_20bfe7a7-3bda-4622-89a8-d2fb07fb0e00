import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppStore } from '../../store/appStore';
import { Line, Radar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  RadialLinearScale,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  RadialLinearScale,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const JobFitAnalyzer = () => {
  const { 
    jobFitScore, 
    updateJobFitScore, 
    jobDescription, 
    resume, 
    transcript, 
    neuroAI,
    isRecording 
  } = useAppStore();
  
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [skillsBreakdown, setSkillsBreakdown] = useState({});
  const [careerSuggestions, setCareerSuggestions] = useState([]);
  const [competencyMap, setCompetencyMap] = useState({});
  const analysisIntervalRef = useRef(null);

  // Real-time analysis
  useEffect(() => {
    if (isRecording && transcript) {
      if (analysisIntervalRef.current) {
        clearInterval(analysisIntervalRef.current);
      }
      
      analysisIntervalRef.current = setInterval(() => {
        performRealTimeAnalysis();
      }, 5000); // Analyze every 5 seconds
    } else {
      if (analysisIntervalRef.current) {
        clearInterval(analysisIntervalRef.current);
      }
    }

    return () => {
      if (analysisIntervalRef.current) {
        clearInterval(analysisIntervalRef.current);
      }
    };
  }, [isRecording, transcript]);

  const performRealTimeAnalysis = async () => {
    setIsAnalyzing(true);
    
    try {
      // Analyze job fit based on multiple factors
      const analysis = await analyzeJobFit();
      
      updateJobFitScore(analysis.overallScore);
      setSkillsBreakdown(analysis.skillsBreakdown);
      setCompetencyMap(analysis.competencyMap);
      setCareerSuggestions(analysis.careerSuggestions);
    } catch (error) {
      console.error('Job fit analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const analyzeJobFit = async () => {
    // Extract key requirements from job description
    const jobRequirements = extractJobRequirements(jobDescription);
    
    // Analyze candidate's responses and background
    const candidateProfile = analyzeCandidateProfile();
    
    // Calculate fit scores for different aspects
    const technicalFit = calculateTechnicalFit(jobRequirements, candidateProfile);
    const culturalFit = calculateCulturalFit(jobRequirements, candidateProfile);
    const experienceFit = calculateExperienceFit(jobRequirements, candidateProfile);
    const communicationFit = calculateCommunicationFit();
    
    // Overall score calculation
    const overallScore = Math.round(
      (technicalFit * 0.4 + culturalFit * 0.2 + experienceFit * 0.3 + communicationFit * 0.1)
    );
    
    return {
      overallScore,
      skillsBreakdown: {
        technical: technicalFit,
        cultural: culturalFit,
        experience: experienceFit,
        communication: communicationFit
      },
      competencyMap: generateCompetencyMap(jobRequirements, candidateProfile),
      careerSuggestions: generateCareerSuggestions(overallScore, candidateProfile)
    };
  };

  const extractJobRequirements = (description) => {
    const requirements = {
      skills: [],
      experience: [],
      education: [],
      keywords: []
    };
    
    if (!description) return requirements;
    
    const text = description.toLowerCase();
    
    // Extract technical skills
    const techSkills = [
      'javascript', 'python', 'java', 'react', 'node.js', 'sql', 'aws', 'docker',
      'kubernetes', 'machine learning', 'ai', 'data science', 'frontend', 'backend',
      'full-stack', 'devops', 'cloud', 'microservices', 'api', 'database'
    ];
    
    requirements.skills = techSkills.filter(skill => text.includes(skill));
    
    // Extract experience requirements
    const experienceMatch = text.match(/(\d+)\+?\s*years?\s*(of\s*)?experience/g);
    if (experienceMatch) {
      requirements.experience = experienceMatch.map(match => {
        const years = parseInt(match.match(/\d+/)[0]);
        return { years, context: match };
      });
    }
    
    // Extract education requirements
    const educationKeywords = ['bachelor', 'master', 'phd', 'degree', 'computer science', 'engineering'];
    requirements.education = educationKeywords.filter(edu => text.includes(edu));
    
    return requirements;
  };

  const analyzeCandidateProfile = () => {
    const profile = {
      skills: [],
      experience: [],
      communication: {},
      personality: {}
    };
    
    // Analyze resume
    if (resume) {
      const resumeText = typeof resume === 'string' ? resume : JSON.stringify(resume);
      profile.skills = extractSkillsFromText(resumeText);
      profile.experience = extractExperienceFromText(resumeText);
    }
    
    // Analyze interview responses
    if (transcript) {
      profile.communication = analyzeCommuncationStyle(transcript);
      profile.personality = analyzePersonalityTraits(transcript);
    }
    
    return profile;
  };

  const extractSkillsFromText = (text) => {
    const techSkills = [
      'javascript', 'python', 'java', 'react', 'node.js', 'sql', 'aws', 'docker',
      'kubernetes', 'machine learning', 'ai', 'data science', 'frontend', 'backend'
    ];
    
    return techSkills.filter(skill => 
      text.toLowerCase().includes(skill)
    ).map(skill => ({ name: skill, confidence: 0.8 }));
  };

  const extractExperienceFromText = (text) => {
    const experienceMatch = text.match(/(\d+)\+?\s*years?\s*(of\s*)?experience/gi);
    return experienceMatch ? experienceMatch.map(match => {
      const years = parseInt(match.match(/\d+/)[0]);
      return { years, context: match };
    }) : [];
  };

  const analyzeCommuncationStyle = (transcript) => {
    const words = transcript.split(' ');
    const sentences = transcript.split(/[.!?]+/);
    
    return {
      clarity: calculateClarity(transcript),
      confidence: neuroAI.confidenceLevel,
      articulation: words.length / sentences.length, // Average words per sentence
      vocabulary: new Set(words.map(w => w.toLowerCase())).size / words.length
    };
  };

  const analyzePersonalityTraits = (transcript) => {
    const text = transcript.toLowerCase();
    
    return {
      leadership: (text.match(/\b(lead|manage|team|project)\b/g) || []).length / 100,
      problemSolving: (text.match(/\b(solve|solution|challenge|problem)\b/g) || []).length / 100,
      collaboration: (text.match(/\b(team|collaborate|together|group)\b/g) || []).length / 100,
      innovation: (text.match(/\b(innovate|creative|new|improve)\b/g) || []).length / 100
    };
  };

  const calculateClarity = (text) => {
    // Simple clarity score based on filler words and sentence structure
    const fillerWords = ['um', 'uh', 'like', 'you know', 'actually', 'basically'];
    const words = text.toLowerCase().split(' ');
    const fillerCount = words.filter(word => fillerWords.includes(word)).length;
    
    return Math.max(0, 1 - (fillerCount / words.length) * 10);
  };

  const calculateTechnicalFit = (requirements, profile) => {
    if (requirements.skills.length === 0) return 75; // Default if no specific requirements
    
    const matchedSkills = requirements.skills.filter(reqSkill =>
      profile.skills.some(candidateSkill => 
        candidateSkill.name.toLowerCase().includes(reqSkill.toLowerCase())
      )
    );
    
    return Math.round((matchedSkills.length / requirements.skills.length) * 100);
  };

  const calculateCulturalFit = (requirements, profile) => {
    // Based on personality traits and communication style
    const personalityScore = Object.values(profile.personality || {}).reduce((sum, val) => sum + val, 0) / 4;
    const communicationScore = profile.communication?.confidence || 0.5;
    
    return Math.round((personalityScore * 0.6 + communicationScore * 0.4) * 100);
  };

  const calculateExperienceFit = (requirements, profile) => {
    if (requirements.experience.length === 0) return 70; // Default
    
    const requiredYears = Math.max(...requirements.experience.map(exp => exp.years));
    const candidateYears = Math.max(...(profile.experience.map(exp => exp.years) || [0]));
    
    if (candidateYears >= requiredYears) return 100;
    return Math.round((candidateYears / requiredYears) * 100);
  };

  const calculateCommunicationFit = () => {
    return Math.round(
      (neuroAI.confidenceLevel * 0.4 + 
       (1 - neuroAI.stressLevel) * 0.3 + 
       (neuroAI.emotionalState === 'positive' ? 0.8 : 0.5) * 0.3) * 100
    );
  };

  const generateCompetencyMap = (requirements, profile) => {
    return {
      'Technical Skills': calculateTechnicalFit(requirements, profile),
      'Communication': calculateCommunicationFit(),
      'Problem Solving': Math.round((profile.personality?.problemSolving || 0.5) * 100),
      'Leadership': Math.round((profile.personality?.leadership || 0.5) * 100),
      'Teamwork': Math.round((profile.personality?.collaboration || 0.5) * 100),
      'Innovation': Math.round((profile.personality?.innovation || 0.5) * 100)
    };
  };

  const generateCareerSuggestions = (score, profile) => {
    const suggestions = [];
    
    if (score >= 80) {
      suggestions.push({
        type: 'strength',
        title: 'Excellent Match!',
        description: 'You\'re a strong candidate for this role. Focus on showcasing your unique value proposition.',
        icon: '🎯'
      });
    } else if (score >= 60) {
      suggestions.push({
        type: 'improvement',
        title: 'Good Potential',
        description: 'Consider highlighting specific experiences that align with the role requirements.',
        icon: '📈'
      });
    } else {
      suggestions.push({
        type: 'development',
        title: 'Growth Opportunity',
        description: 'This role could be a stretch opportunity. Emphasize your learning agility and growth mindset.',
        icon: '🌱'
      });
    }
    
    // Add specific skill recommendations
    if (skillsBreakdown.technical < 70) {
      suggestions.push({
        type: 'skill',
        title: 'Technical Skills',
        description: 'Consider mentioning relevant projects or certifications to strengthen your technical profile.',
        icon: '💻'
      });
    }
    
    if (skillsBreakdown.communication < 70) {
      suggestions.push({
        type: 'communication',
        title: 'Communication Enhancement',
        description: 'Practice articulating your thoughts clearly and confidently. Consider using the STAR method.',
        icon: '🗣️'
      });
    }
    
    return suggestions;
  };

  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreGradient = (score) => {
    if (score >= 80) return 'from-green-500 to-emerald-600';
    if (score >= 60) return 'from-yellow-500 to-orange-600';
    return 'from-red-500 to-pink-600';
  };

  // Chart data
  const radarData = {
    labels: Object.keys(competencyMap),
    datasets: [
      {
        label: 'Your Profile',
        data: Object.values(competencyMap),
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 2,
        pointBackgroundColor: 'rgba(59, 130, 246, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(59, 130, 246, 1)'
      }
    ]
  };

  const scoreHistoryData = {
    labels: jobFitScore.scoreHistory.map((_, index) => `T${index + 1}`),
    datasets: [
      {
        label: 'Job Fit Score',
        data: jobFitScore.scoreHistory.map(entry => entry.score),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        labels: {
          color: 'white'
        }
      }
    },
    scales: {
      r: {
        angleLines: {
          color: 'rgba(255, 255, 255, 0.1)'
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)'
        },
        pointLabels: {
          color: 'white'
        },
        ticks: {
          color: 'white',
          backdropColor: 'transparent'
        }
      }
    }
  };

  return (
    <div className="job-fit-analyzer">
      {/* Main Score Display */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-gray-900/80 backdrop-blur-sm rounded-2xl p-6 mb-6"
      >
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-white">🎯 Real-Time Job Fit Analysis</h2>
          {isAnalyzing && (
            <div className="flex items-center space-x-2 text-blue-400">
              <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-sm">Analyzing...</span>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Overall Score */}
          <div className="text-center">
            <div className="relative w-32 h-32 mx-auto mb-4">
              <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  className="text-gray-700"
                  stroke="currentColor"
                  strokeWidth="3"
                  fill="transparent"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <path
                  className={`${getScoreColor(jobFitScore.currentScore)}`}
                  stroke="currentColor"
                  strokeWidth="3"
                  strokeLinecap="round"
                  fill="transparent"
                  strokeDasharray={`${jobFitScore.currentScore}, 100`}
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className={`text-3xl font-bold ${getScoreColor(jobFitScore.currentScore)}`}>
                  {jobFitScore.currentScore}%
                </span>
              </div>
            </div>
            <h3 className="text-white font-semibold">Overall Job Fit</h3>
            <p className="text-gray-400 text-sm">
              {jobFitScore.currentScore >= 80 ? 'Excellent Match' :
               jobFitScore.currentScore >= 60 ? 'Good Fit' : 'Potential Growth'}
            </p>
          </div>

          {/* Skills Breakdown */}
          <div>
            <h3 className="text-white font-semibold mb-3">Skills Breakdown</h3>
            <div className="space-y-3">
              {Object.entries(skillsBreakdown).map(([skill, score]) => (
                <div key={skill}>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-300 capitalize">{skill}</span>
                    <span className={getScoreColor(score)}>{score}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <motion.div
                      className={`h-2 rounded-full bg-gradient-to-r ${getScoreGradient(score)}`}
                      initial={{ width: 0 }}
                      animate={{ width: `${score}%` }}
                      transition={{ duration: 1, delay: 0.2 }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Competency Radar */}
          <div>
            <h3 className="text-white font-semibold mb-3">Competency Map</h3>
            <div className="h-48">
              <Radar data={radarData} options={chartOptions} />
            </div>
          </div>
        </div>
      </motion.div>

      {/* Career Suggestions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gray-900/80 backdrop-blur-sm rounded-2xl p-6 mb-6"
      >
        <h3 className="text-white font-semibold mb-4">💡 AI-Powered Recommendations</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {careerSuggestions.map((suggestion, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`p-4 rounded-lg border-l-4 ${
                suggestion.type === 'strength' ? 'bg-green-600/20 border-green-600' :
                suggestion.type === 'improvement' ? 'bg-yellow-600/20 border-yellow-600' :
                suggestion.type === 'development' ? 'bg-blue-600/20 border-blue-600' :
                'bg-purple-600/20 border-purple-600'
              }`}
            >
              <div className="flex items-start space-x-3">
                <span className="text-2xl">{suggestion.icon}</span>
                <div>
                  <h4 className="text-white font-medium">{suggestion.title}</h4>
                  <p className="text-gray-300 text-sm mt-1">{suggestion.description}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Score History */}
      {jobFitScore.scoreHistory.length > 1 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-900/80 backdrop-blur-sm rounded-2xl p-6"
        >
          <h3 className="text-white font-semibold mb-4">📈 Score Evolution</h3>
          <div className="h-64">
            <Line data={scoreHistoryData} options={{
              ...chartOptions,
              scales: {
                x: {
                  ticks: { color: 'white' },
                  grid: { color: 'rgba(255, 255, 255, 0.1)' }
                },
                y: {
                  ticks: { color: 'white' },
                  grid: { color: 'rgba(255, 255, 255, 0.1)' },
                  min: 0,
                  max: 100
                }
              }
            }} />
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default JobFitAnalyzer;
