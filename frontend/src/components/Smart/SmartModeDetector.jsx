import React, { useEffect, useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppStore } from '../../store/appStore';

const SmartModeDetector = () => {
  const { 
    smartMode, 
    updateSmartMode, 
    transcript, 
    jobDescription, 
    isRecording,
    currentSession 
  } = useAppStore();
  
  const [detectionConfidence, setDetectionConfidence] = useState(0);
  const [modeHistory, setModeHistory] = useState([]);
  const [platformDetected, setPlatformDetected] = useState(null);
  const detectionIntervalRef = useRef(null);

  // Platform detection patterns
  const platformPatterns = {
    'hackerrank': ['hackerrank.com', 'hacker rank', 'coding challenge'],
    'leetcode': ['leetcode.com', 'leet code', 'algorithm problem'],
    'coderpad': ['coderpad.io', 'coder pad', 'collaborative coding'],
    'zoom': ['zoom.us', 'zoom meeting', 'zoom call'],
    'teams': ['teams.microsoft.com', 'microsoft teams', 'teams meeting'],
    'meet': ['meet.google.com', 'google meet', 'meet call'],
    'aws': ['aws.amazon.com', 'amazon web services', 'aws console'],
    'azure': ['portal.azure.com', 'microsoft azure', 'azure portal'],
    'gcp': ['console.cloud.google.com', 'google cloud', 'gcp console']
  };

  // Interview type detection keywords
  const interviewTypeKeywords = {
    technical: [
      'algorithm', 'data structure', 'coding', 'programming', 'debug', 'optimize',
      'complexity', 'big o', 'recursion', 'dynamic programming', 'binary tree',
      'hash table', 'linked list', 'array', 'string manipulation', 'sql query',
      'database design', 'api design', 'system architecture', 'scalability',
      'microservices', 'load balancing', 'caching', 'distributed systems'
    ],
    behavioral: [
      'tell me about yourself', 'greatest strength', 'weakness', 'challenge',
      'conflict', 'teamwork', 'leadership', 'motivation', 'career goals',
      'why this company', 'why this role', 'experience', 'situation',
      'star method', 'accomplishment', 'failure', 'learn from', 'feedback'
    ],
    systemDesign: [
      'design a system', 'architecture', 'scalability', 'load balancer',
      'database sharding', 'microservices', 'api gateway', 'caching strategy',
      'cdn', 'message queue', 'event driven', 'distributed system',
      'high availability', 'fault tolerance', 'consistency', 'cap theorem'
    ],
    mock: [
      'practice interview', 'mock interview', 'simulation', 'rehearsal',
      'preparation', 'practice session', 'dry run', 'test run'
    ]
  };

  useEffect(() => {
    if (smartMode.autoDetection && isRecording) {
      startModeDetection();
    } else {
      stopModeDetection();
    }

    return () => stopModeDetection();
  }, [smartMode.autoDetection, isRecording]);

  useEffect(() => {
    if (transcript) {
      performRealTimeDetection();
    }
  }, [transcript]);

  useEffect(() => {
    detectPlatform();
  }, []);

  const startModeDetection = () => {
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
    }

    detectionIntervalRef.current = setInterval(() => {
      performPeriodicDetection();
    }, 10000); // Check every 10 seconds
  };

  const stopModeDetection = () => {
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
      detectionIntervalRef.current = null;
    }
  };

  const detectPlatform = () => {
    const currentUrl = window.location.href.toLowerCase();
    const userAgent = navigator.userAgent.toLowerCase();
    
    // Check URL patterns
    for (const [platform, patterns] of Object.entries(platformPatterns)) {
      for (const pattern of patterns) {
        if (currentUrl.includes(pattern) || userAgent.includes(pattern)) {
          setPlatformDetected(platform);
          return;
        }
      }
    }

    // Check for video call indicators
    navigator.mediaDevices.enumerateDevices().then(devices => {
      const hasCamera = devices.some(device => device.kind === 'videoinput');
      const hasMicrophone = devices.some(device => device.kind === 'audioinput');
      
      if (hasCamera && hasMicrophone) {
        setPlatformDetected('video-call');
      }
    });
  };

  const performRealTimeDetection = () => {
    const recentTranscript = transcript.slice(-1000); // Last 1000 characters
    const detectedMode = analyzeTranscriptForMode(recentTranscript);
    
    if (detectedMode && detectedMode.mode !== smartMode.currentMode) {
      const confidence = detectedMode.confidence;
      
      if (confidence > 0.7) {
        updateSmartMode({ 
          currentMode: detectedMode.mode,
          contextAwareness: true 
        });
        
        setModeHistory(prev => [...prev.slice(-9), {
          mode: detectedMode.mode,
          confidence,
          timestamp: Date.now(),
          trigger: 'real-time'
        }]);
        
        setDetectionConfidence(confidence);
      }
    }
  };

  const performPeriodicDetection = () => {
    const fullContext = {
      transcript: transcript,
      jobDescription: jobDescription,
      platform: platformDetected,
      sessionDuration: currentSession?.duration || 0
    };
    
    const detectedMode = analyzeFullContext(fullContext);
    
    if (detectedMode && detectedMode.confidence > 0.6) {
      updateSmartMode({ 
        currentMode: detectedMode.mode,
        adaptiveQuestions: true 
      });
      
      setModeHistory(prev => [...prev.slice(-9), {
        mode: detectedMode.mode,
        confidence: detectedMode.confidence,
        timestamp: Date.now(),
        trigger: 'periodic'
      }]);
      
      setDetectionConfidence(detectedMode.confidence);
    }
  };

  const analyzeTranscriptForMode = (text) => {
    if (!text || text.length < 50) return null;
    
    const lowerText = text.toLowerCase();
    const scores = {};
    
    // Calculate scores for each interview type
    Object.entries(interviewTypeKeywords).forEach(([mode, keywords]) => {
      let score = 0;
      let matchCount = 0;
      
      keywords.forEach(keyword => {
        const matches = (lowerText.match(new RegExp(keyword, 'g')) || []).length;
        if (matches > 0) {
          score += matches;
          matchCount++;
        }
      });
      
      // Normalize score
      scores[mode] = {
        rawScore: score,
        matchCount,
        normalizedScore: score / Math.max(text.length / 100, 1)
      };
    });
    
    // Find the mode with highest score
    const bestMode = Object.entries(scores).reduce((best, [mode, data]) => {
      if (data.normalizedScore > (best.score || 0)) {
        return { mode, score: data.normalizedScore, matchCount: data.matchCount };
      }
      return best;
    }, {});
    
    if (bestMode.mode && bestMode.score > 0.1) {
      return {
        mode: bestMode.mode,
        confidence: Math.min(bestMode.score * 2, 1) // Scale confidence
      };
    }
    
    return null;
  };

  const analyzeFullContext = (context) => {
    const { transcript, jobDescription, platform, sessionDuration } = context;
    
    // Platform-based detection
    if (platform) {
      if (['hackerrank', 'leetcode', 'coderpad'].includes(platform)) {
        return { mode: 'technical', confidence: 0.9 };
      }
      if (['aws', 'azure', 'gcp'].includes(platform)) {
        return { mode: 'systemDesign', confidence: 0.8 };
      }
    }
    
    // Job description analysis
    let jobBasedMode = null;
    if (jobDescription) {
      const jobText = jobDescription.toLowerCase();
      
      if (jobText.includes('senior') || jobText.includes('architect') || jobText.includes('lead')) {
        jobBasedMode = 'systemDesign';
      } else if (jobText.includes('developer') || jobText.includes('engineer')) {
        jobBasedMode = 'technical';
      }
    }
    
    // Transcript analysis
    const transcriptMode = analyzeTranscriptForMode(transcript);
    
    // Session duration analysis
    let durationFactor = 1;
    if (sessionDuration > 30 * 60 * 1000) { // More than 30 minutes
      durationFactor = 1.2; // Likely more complex interview
    }
    
    // Combine all factors
    const modes = ['technical', 'behavioral', 'systemDesign', 'mock'];
    const finalScores = {};
    
    modes.forEach(mode => {
      let score = 0;
      
      // Transcript contribution (40%)
      if (transcriptMode && transcriptMode.mode === mode) {
        score += transcriptMode.confidence * 0.4;
      }
      
      // Job description contribution (30%)
      if (jobBasedMode === mode) {
        score += 0.3;
      }
      
      // Platform contribution (20%)
      if (platform && getPlatformModeAffinity(platform, mode) > 0) {
        score += getPlatformModeAffinity(platform, mode) * 0.2;
      }
      
      // Duration contribution (10%)
      score *= durationFactor * 0.1 + 0.9;
      
      finalScores[mode] = score;
    });
    
    const bestMode = Object.entries(finalScores).reduce((best, [mode, score]) => {
      if (score > (best.score || 0)) {
        return { mode, score };
      }
      return best;
    }, {});
    
    if (bestMode.mode && bestMode.score > 0.3) {
      return {
        mode: bestMode.mode,
        confidence: Math.min(bestMode.score, 1)
      };
    }
    
    return null;
  };

  const getPlatformModeAffinity = (platform, mode) => {
    const affinities = {
      'hackerrank': { technical: 0.9, systemDesign: 0.3 },
      'leetcode': { technical: 0.9, systemDesign: 0.2 },
      'coderpad': { technical: 0.8, systemDesign: 0.6 },
      'aws': { systemDesign: 0.9, technical: 0.4 },
      'azure': { systemDesign: 0.9, technical: 0.4 },
      'gcp': { systemDesign: 0.9, technical: 0.4 },
      'zoom': { behavioral: 0.6, technical: 0.4, systemDesign: 0.4 },
      'teams': { behavioral: 0.6, technical: 0.4, systemDesign: 0.4 },
      'meet': { behavioral: 0.6, technical: 0.4, systemDesign: 0.4 }
    };
    
    return affinities[platform]?.[mode] || 0;
  };

  const getModeIcon = (mode) => {
    const icons = {
      technical: '💻',
      behavioral: '🗣️',
      systemDesign: '🏗️',
      mock: '🎭'
    };
    return icons[mode] || '🤖';
  };

  const getModeColor = (mode) => {
    const colors = {
      technical: 'from-blue-500 to-cyan-500',
      behavioral: 'from-green-500 to-emerald-500',
      systemDesign: 'from-purple-500 to-violet-500',
      mock: 'from-yellow-500 to-orange-500'
    };
    return colors[mode] || 'from-gray-500 to-gray-600';
  };

  const getModeDescription = (mode) => {
    const descriptions = {
      technical: 'Coding challenges, algorithms, and technical problem-solving',
      behavioral: 'Personal experiences, soft skills, and cultural fit questions',
      systemDesign: 'Architecture design, scalability, and system planning',
      mock: 'Practice session for interview preparation'
    };
    return descriptions[mode] || 'General interview assistance';
  };

  return (
    <div className="smart-mode-detector">
      {/* Mode Detection Status */}
      <AnimatePresence>
        {smartMode.autoDetection && isRecording && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed top-20 left-4 z-50 bg-gray-900/90 backdrop-blur-sm rounded-lg p-4 max-w-sm"
          >
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
              <span className="text-white font-semibold">Smart Mode Active</span>
            </div>
            
            <div className={`bg-gradient-to-r ${getModeColor(smartMode.currentMode)} rounded-lg p-3 text-white`}>
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-2xl">{getModeIcon(smartMode.currentMode)}</span>
                <div>
                  <div className="font-semibold capitalize">{smartMode.currentMode} Interview</div>
                  <div className="text-sm opacity-90">
                    Confidence: {Math.round(detectionConfidence * 100)}%
                  </div>
                </div>
              </div>
              <p className="text-sm opacity-90">
                {getModeDescription(smartMode.currentMode)}
              </p>
            </div>
            
            {platformDetected && (
              <div className="mt-3 p-2 bg-gray-800 rounded text-sm text-gray-300">
                <span className="font-medium">Platform:</span> {platformDetected}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mode Switch Notification */}
      <AnimatePresence>
        {modeHistory.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            className="fixed bottom-20 right-4 z-50 bg-blue-600 text-white rounded-lg p-4 max-w-sm"
          >
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-xl">🎯</span>
              <span className="font-semibold">Mode Detected</span>
            </div>
            <p className="text-sm">
              Switched to <strong className="capitalize">{smartMode.currentMode}</strong> mode
              {detectionConfidence > 0 && (
                <span className="opacity-90"> ({Math.round(detectionConfidence * 100)}% confidence)</span>
              )}
            </p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Manual Mode Override */}
      <div className="fixed bottom-4 left-4 z-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-gray-900/80 backdrop-blur-sm rounded-lg p-3"
        >
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-white text-sm font-medium">Interview Mode:</span>
          </div>
          
          <div className="flex space-x-1">
            {['technical', 'behavioral', 'systemDesign', 'mock'].map((mode) => (
              <button
                key={mode}
                onClick={() => updateSmartMode({ currentMode: mode, autoDetection: false })}
                className={`p-2 rounded text-xs transition-all ${
                  smartMode.currentMode === mode
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
                title={getModeDescription(mode)}
              >
                {getModeIcon(mode)}
              </button>
            ))}
          </div>
          
          <button
            onClick={() => updateSmartMode({ autoDetection: !smartMode.autoDetection })}
            className={`w-full mt-2 p-1 rounded text-xs transition-colors ${
              smartMode.autoDetection
                ? 'bg-green-600 text-white'
                : 'bg-gray-700 text-gray-300'
            }`}
          >
            Auto: {smartMode.autoDetection ? 'ON' : 'OFF'}
          </button>
        </motion.div>
      </div>

      {/* Detection History */}
      {modeHistory.length > 0 && (
        <div className="fixed bottom-32 left-4 z-40 bg-gray-900/80 backdrop-blur-sm rounded-lg p-3 max-w-xs">
          <h4 className="text-white text-sm font-medium mb-2">Detection History</h4>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {modeHistory.slice(-5).map((entry, index) => (
              <div key={index} className="flex items-center justify-between text-xs">
                <div className="flex items-center space-x-1">
                  <span>{getModeIcon(entry.mode)}</span>
                  <span className="text-gray-300 capitalize">{entry.mode}</span>
                </div>
                <div className="text-gray-400">
                  {Math.round(entry.confidence * 100)}%
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SmartModeDetector;
