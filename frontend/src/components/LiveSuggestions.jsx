import React, { useState, useEffect } from 'react'
import { <PERSON>bul<PERSON>, X, Co<PERSON>, Check, Zap } from 'lucide-react'
import toast from 'react-hot-toast'

const LiveSuggestions = ({ suggestions, isRecording }) => {
  const [copiedId, setCopiedId] = useState(null)
  const [latestSuggestion, setLatestSuggestion] = useState(null)
  const [showPopup, setShowPopup] = useState(false)

  // Show popup for new suggestions
  useEffect(() => {
    if (suggestions.length > 0) {
      const newest = suggestions[0]
      if (newest.id !== latestSuggestion?.id) {
        setLatestSuggestion(newest)
        setShowPopup(true)
        
        // Auto-hide popup after 8 seconds
        const timer = setTimeout(() => {
          setShowPopup(false)
        }, 8000)
        
        return () => clearTimeout(timer)
      }
    }
  }, [suggestions, latestSuggestion])

  const copyToClipboard = async (text, id) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedId(id)
      toast.success('Suggestion copied!')
      
      setTimeout(() => {
        setCopiedId(null)
      }, 2000)
    } catch (error) {
      toast.error('Failed to copy')
    }
  }

  const formatConfidence = (confidence) => {
    return Math.round((confidence || 0.8) * 100)
  }

  const getConfidenceColor = (confidence) => {
    const score = formatConfidence(confidence)
    if (score >= 80) return 'text-green-600 dark:text-green-400'
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  return (
    <div className="relative">
      {/* Live Popup for Latest Suggestion */}
      {showPopup && latestSuggestion && isRecording && (
        <div className="fixed top-4 right-4 z-50 max-w-md animate-slide-in-right">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-1 rounded-lg shadow-2xl">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Zap className="w-5 h-5 text-blue-500 animate-pulse" />
                  <span className="text-sm font-semibold text-blue-600 dark:text-blue-400">
                    Live AI Suggestion
                  </span>
                </div>
                <button
                  onClick={() => setShowPopup(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              
              <p className="text-gray-900 dark:text-gray-100 text-sm leading-relaxed mb-3">
                {latestSuggestion.text}
              </p>
              
              <div className="flex items-center justify-between">
                <span className={`text-xs font-medium ${getConfidenceColor(latestSuggestion.confidence)}`}>
                  {formatConfidence(latestSuggestion.confidence)}% confidence
                </span>
                
                <button
                  onClick={() => copyToClipboard(latestSuggestion.text, latestSuggestion.id)}
                  className="flex items-center space-x-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
                >
                  {copiedId === latestSuggestion.id ? (
                    <>
                      <Check className="w-3 h-3" />
                      <span>Copied!</span>
                    </>
                  ) : (
                    <>
                      <Copy className="w-3 h-3" />
                      <span>Copy</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Suggestions Panel */}
      <div className="card p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Lightbulb className="w-5 h-5 text-yellow-500" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            AI Suggestions
          </h3>
          {isRecording && (
            <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>Live</span>
            </div>
          )}
        </div>
        
        <div className="space-y-3 max-h-80 overflow-y-auto">
          {suggestions.length > 0 ? (
            suggestions.map((suggestion, index) => (
              <div 
                key={suggestion.id || index}
                className={`border rounded-lg p-3 transition-all duration-300 ${
                  index === 0 
                    ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 shadow-md' 
                    : 'bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700'
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <p className="text-sm text-gray-900 dark:text-gray-100 leading-relaxed flex-1">
                    {suggestion.text}
                  </p>
                  
                  <button
                    onClick={() => copyToClipboard(suggestion.text, suggestion.id)}
                    className="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                    title="Copy suggestion"
                  >
                    {copiedId === suggestion.id ? (
                      <Check className="w-4 h-4 text-green-500" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </button>
                </div>
                
                <div className="flex items-center justify-between text-xs">
                  <span className={`font-medium ${getConfidenceColor(suggestion.confidence)}`}>
                    Confidence: {formatConfidence(suggestion.confidence)}%
                  </span>
                  <span className="text-gray-500">
                    {new Date(suggestion.timestamp).toLocaleTimeString()}
                  </span>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <Lightbulb className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
              <p className="text-gray-500 dark:text-gray-400">
                {isRecording 
                  ? 'Listening for questions... AI suggestions will appear here.'
                  : 'Start recording to receive AI suggestions during your interview.'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default LiveSuggestions
