import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppStore } from '../../store/appStore';
import Lottie from 'lottie-react';

const PersonaSimulator = () => {
  const { currentPersona, availablePersonas, setCurrentPersona, jobDescription } = useAppStore();
  const [isPersonaActive, setIsPersonaActive] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState('');
  const [questionHistory, setQuestionHistory] = useState([]);
  const [personaResponse, setPersonaResponse] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  const speechSynthRef = useRef(null);

  // Enhanced persona data with detailed characteristics
  const enhancedPersonas = {
    'cto-fintech': {
      ...availablePersonas.find(p => p.id === 'cto-fintech'),
      voice: {
        rate: 0.9,
        pitch: 0.8,
        volume: 0.8
      },
      questionPatterns: [
        "Tell me about a time you had to scale a system under pressure.",
        "How would you design a payment processing system that handles millions of transactions?",
        "What's your approach to technical debt management?",
        "Explain how you would implement real-time fraud detection.",
        "How do you ensure security in financial applications?"
      ],
      responseStyle: 'analytical',
      followUpProbability: 0.8,
      interruptionStyle: 'technical-clarification',
      stressLevel: 'high',
      culturalContext: 'startup-fast-paced'
    },
    'hr-junior': {
      ...availablePersonas.find(p => p.id === 'hr-junior'),
      voice: {
        rate: 1.1,
        pitch: 1.2,
        volume: 0.9
      },
      questionPatterns: [
        "Tell me about yourself and what interests you about this role.",
        "How do you handle working in a team environment?",
        "What are your greatest strengths and weaknesses?",
        "Where do you see yourself in five years?",
        "Why are you interested in joining our company?"
      ],
      responseStyle: 'encouraging',
      followUpProbability: 0.6,
      interruptionStyle: 'supportive',
      stressLevel: 'low',
      culturalContext: 'corporate-friendly'
    },
    'ai-lead-google': {
      ...availablePersonas.find(p => p.id === 'ai-lead-google'),
      voice: {
        rate: 0.8,
        pitch: 0.9,
        volume: 0.7
      },
      questionPatterns: [
        "Design a recommendation system for YouTube that can handle billions of users.",
        "How would you approach building an AI system that's both accurate and fair?",
        "Explain the trade-offs between different machine learning architectures.",
        "How do you handle model drift in production ML systems?",
        "What's your approach to A/B testing ML models at scale?"
      ],
      responseStyle: 'challenging',
      followUpProbability: 0.9,
      interruptionStyle: 'deep-dive',
      stressLevel: 'very-high',
      culturalContext: 'tech-giant-rigorous'
    }
  };

  useEffect(() => {
    if (currentPersona && isPersonaActive) {
      startPersonaSession();
    }
  }, [currentPersona, isPersonaActive]);

  useEffect(() => {
    // Initialize speech synthesis
    if ('speechSynthesis' in window) {
      speechSynthRef.current = window.speechSynthesis;
    }
  }, []);

  const startPersonaSession = () => {
    const persona = enhancedPersonas[currentPersona.id];
    if (!persona) return;

    // Generate opening question based on persona and job description
    const openingQuestion = generatePersonaQuestion(persona, 'opening');
    setCurrentQuestion(openingQuestion);
    setQuestionHistory([openingQuestion]);
    
    // Simulate typing effect
    typePersonaResponse(persona, openingQuestion);
  };

  const generatePersonaQuestion = (persona, type = 'follow-up') => {
    const questions = persona.questionPatterns;
    
    if (type === 'opening') {
      // Customize opening based on job description
      if (jobDescription.toLowerCase().includes('frontend')) {
        return `Hello! I'm excited to learn about your frontend development experience. ${questions[0]}`;
      } else if (jobDescription.toLowerCase().includes('backend')) {
        return `Welcome! Let's dive into your backend engineering background. ${questions[0]}`;
      }
      return `Great to meet you! ${questions[0]}`;
    }
    
    // Generate follow-up questions
    const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
    return addPersonaFlavor(randomQuestion, persona);
  };

  const addPersonaFlavor = (question, persona) => {
    const flavorPrefixes = {
      'analytical': ['Interesting. ', 'Let me dig deeper. ', 'That\'s a good start. '],
      'encouraging': ['That sounds great! ', 'I love that approach. ', 'Wonderful! '],
      'challenging': ['Hmm, ', 'Let\'s see... ', 'I need more detail. ']
    };
    
    const prefixes = flavorPrefixes[persona.responseStyle] || [''];
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    
    return prefix + question;
  };

  const typePersonaResponse = (persona, text) => {
    setIsTyping(true);
    setPersonaResponse('');
    
    let index = 0;
    const typingSpeed = persona.responseStyle === 'challenging' ? 50 : 30;
    
    const typeInterval = setInterval(() => {
      if (index < text.length) {
        setPersonaResponse(prev => prev + text[index]);
        index++;
      } else {
        clearInterval(typeInterval);
        setIsTyping(false);
        
        // Speak the response if voice is enabled
        if (voiceEnabled) {
          speakPersonaResponse(text, persona);
        }
      }
    }, typingSpeed);
  };

  const speakPersonaResponse = (text, persona) => {
    if (!speechSynthRef.current) return;
    
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = persona.voice.rate;
    utterance.pitch = persona.voice.pitch;
    utterance.volume = persona.voice.volume;
    
    // Try to select a voice that matches the persona
    const voices = speechSynthRef.current.getVoices();
    const preferredVoice = voices.find(voice => 
      voice.lang.includes('en') && 
      (persona.id.includes('google') ? voice.name.includes('Google') : true)
    );
    
    if (preferredVoice) {
      utterance.voice = preferredVoice;
    }
    
    speechSynthRef.current.speak(utterance);
  };

  const handlePersonaInteraction = (userResponse) => {
    if (!currentPersona) return;
    
    const persona = enhancedPersonas[currentPersona.id];
    
    // Simulate persona thinking and generating follow-up
    setTimeout(() => {
      if (Math.random() < persona.followUpProbability) {
        const followUp = generatePersonaQuestion(persona, 'follow-up');
        setCurrentQuestion(followUp);
        setQuestionHistory(prev => [...prev, followUp]);
        typePersonaResponse(persona, followUp);
      }
    }, 2000 + Math.random() * 3000); // Random delay for realism
  };

  const getPersonaAvatar = (persona) => {
    const avatarStyles = {
      'cto-fintech': 'bg-gradient-to-br from-blue-600 to-purple-600',
      'hr-junior': 'bg-gradient-to-br from-green-500 to-blue-500',
      'ai-lead-google': 'bg-gradient-to-br from-red-500 to-yellow-500'
    };
    
    return avatarStyles[persona.id] || 'bg-gradient-to-br from-gray-500 to-gray-700';
  };

  const getDifficultyColor = (difficulty) => {
    const colors = {
      'easy': 'text-green-400',
      'hard': 'text-yellow-400',
      'expert': 'text-red-400'
    };
    return colors[difficulty] || 'text-gray-400';
  };

  const getPersonalityIcon = (personality) => {
    const icons = {
      'analytical': '🔬',
      'friendly': '😊',
      'challenging': '🎯'
    };
    return icons[personality] || '🤖';
  };

  return (
    <div className="persona-simulator">
      {/* Persona Selection */}
      {!currentPersona && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-900 rounded-2xl p-6 mb-6"
        >
          <h3 className="text-white text-xl font-bold mb-4">
            🎭 Choose Your Interviewer Persona
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {availablePersonas.map((persona) => {
              const enhanced = enhancedPersonas[persona.id];
              return (
                <motion.div
                  key={persona.id}
                  className="bg-gray-800 rounded-lg p-4 cursor-pointer hover:bg-gray-700 transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setCurrentPersona(persona)}
                >
                  <div className={`w-16 h-16 rounded-full ${getPersonaAvatar(persona)} flex items-center justify-center text-2xl mb-3`}>
                    {persona.avatar}
                  </div>
                  <h4 className="text-white font-semibold mb-2">{persona.name}</h4>
                  <div className="flex items-center space-x-2 mb-2">
                    <span>{getPersonalityIcon(persona.personality)}</span>
                    <span className="text-gray-300 text-sm capitalize">{persona.personality}</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span>📊</span>
                    <span className={`text-sm font-medium ${getDifficultyColor(persona.difficulty)}`}>
                      {persona.difficulty.toUpperCase()}
                    </span>
                  </div>
                  <p className="text-gray-400 text-sm">{persona.questionStyle.replace('-', ' ')}</p>
                </motion.div>
              );
            })}
          </div>
        </motion.div>
      )}

      {/* Active Persona Interface */}
      {currentPersona && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-gray-900 rounded-2xl overflow-hidden"
        >
          {/* Persona Header */}
          <div className={`${getPersonaAvatar(currentPersona)} p-6 text-white`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-3xl">
                  {currentPersona.avatar}
                </div>
                <div>
                  <h3 className="text-xl font-bold">{currentPersona.name}</h3>
                  <p className="opacity-90">{currentPersona.personality} • {currentPersona.difficulty}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setVoiceEnabled(!voiceEnabled)}
                  className={`p-2 rounded-lg transition-colors ${
                    voiceEnabled ? 'bg-white/20' : 'bg-white/10'
                  }`}
                >
                  {voiceEnabled ? '🔊' : '🔇'}
                </button>
                
                <button
                  onClick={() => setIsPersonaActive(!isPersonaActive)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    isPersonaActive 
                      ? 'bg-red-500 hover:bg-red-600' 
                      : 'bg-green-500 hover:bg-green-600'
                  }`}
                >
                  {isPersonaActive ? 'End Session' : 'Start Interview'}
                </button>
                
                <button
                  onClick={() => setCurrentPersona(null)}
                  className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                >
                  ✕
                </button>
              </div>
            </div>
          </div>

          {/* Conversation Area */}
          {isPersonaActive && (
            <div className="p-6">
              {/* Current Question */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-gray-800 rounded-lg p-4 mb-4"
              >
                <div className="flex items-start space-x-3">
                  <div className={`w-10 h-10 rounded-full ${getPersonaAvatar(currentPersona)} flex items-center justify-center text-lg flex-shrink-0`}>
                    {currentPersona.avatar}
                  </div>
                  <div className="flex-1">
                    <div className="text-white">
                      {personaResponse}
                      {isTyping && (
                        <motion.span
                          animate={{ opacity: [1, 0] }}
                          transition={{ duration: 0.5, repeat: Infinity }}
                          className="ml-1"
                        >
                          |
                        </motion.span>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Response Input */}
              <div className="bg-gray-800 rounded-lg p-4">
                <textarea
                  placeholder="Type your response here..."
                  className="w-full bg-gray-700 text-white rounded-lg p-3 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={4}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handlePersonaInteraction(e.target.value);
                      e.target.value = '';
                    }
                  }}
                />
                <div className="flex justify-between items-center mt-3">
                  <div className="text-gray-400 text-sm">
                    Press Enter to respond, Shift+Enter for new line
                  </div>
                  <div className="flex items-center space-x-2">
                    {isTyping && (
                      <div className="flex items-center space-x-1 text-gray-400 text-sm">
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        <span>Interviewer is typing...</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Session Stats */}
              <div className="grid grid-cols-3 gap-4 mt-4">
                <div className="bg-gray-800 rounded-lg p-3 text-center">
                  <div className="text-2xl font-bold text-blue-400">{questionHistory.length}</div>
                  <div className="text-gray-400 text-sm">Questions Asked</div>
                </div>
                <div className="bg-gray-800 rounded-lg p-3 text-center">
                  <div className="text-2xl font-bold text-green-400">
                    {enhancedPersonas[currentPersona.id]?.stressLevel === 'low' ? 'Low' : 
                     enhancedPersonas[currentPersona.id]?.stressLevel === 'high' ? 'High' : 'Very High'}
                  </div>
                  <div className="text-gray-400 text-sm">Difficulty Level</div>
                </div>
                <div className="bg-gray-800 rounded-lg p-3 text-center">
                  <div className="text-2xl font-bold text-purple-400">
                    {Math.round(enhancedPersonas[currentPersona.id]?.followUpProbability * 100)}%
                  </div>
                  <div className="text-gray-400 text-sm">Follow-up Rate</div>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export default PersonaSimulator;
