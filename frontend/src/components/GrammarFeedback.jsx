import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Info, 
  TrendingUp, 
  MessageSquare,
  Target,
  Zap
} from 'lucide-react'
import { useGrammarAnalysis } from '../hooks/useGrammarAnalysis'

const GrammarFeedback = ({ 
  text, 
  language = 'en', 
  isRealTime = false, 
  onAnalysisComplete = null 
}) => {
  const {
    isAnalyzing,
    grammarErrors,
    speechMetrics,
    fillerWords,
    confidenceScore,
    feedback,
    suggestions,
    error,
    performComprehensiveAnalysis,
    getRealTimeFeedback,
    formatFeedbackForDisplay,
    getAnalysisSummary
  } = useGrammarAnalysis()

  const [showDetails, setShowDetails] = useState(false)
  const [lastAnalyzedText, setLastAnalyzedText] = useState('')

  // Perform analysis when text changes
  useEffect(() => {
    if (!text || text.trim().length === 0) return

    const analyzeText = async () => {
      if (isRealTime) {
        // Real-time analysis for shorter feedback
        if (text.length > lastAnalyzedText.length + 20) {
          const feedback = await getRealTimeFeedback(text, language)
          if (feedback && onAnalysisComplete) {
            onAnalysisComplete(feedback)
          }
          setLastAnalyzedText(text)
        }
      } else {
        // Comprehensive analysis
        const result = await performComprehensiveAnalysis(text, language)
        if (result && onAnalysisComplete) {
          onAnalysisComplete(result)
        }
      }
    }

    const debounceTimer = setTimeout(analyzeText, isRealTime ? 2000 : 1000)
    return () => clearTimeout(debounceTimer)
  }, [text, language, isRealTime, lastAnalyzedText, getRealTimeFeedback, performComprehensiveAnalysis, onAnalysisComplete])

  const summary = getAnalysisSummary()
  const formattedFeedback = formatFeedbackForDisplay(feedback)

  const getScoreColor = (score) => {
    if (score >= 85) return 'text-green-600'
    if (score >= 70) return 'text-blue-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBackground = (score) => {
    if (score >= 85) return 'bg-green-100 border-green-200'
    if (score >= 70) return 'bg-blue-100 border-blue-200'
    if (score >= 60) return 'bg-yellow-100 border-yellow-200'
    return 'bg-red-100 border-red-200'
  }

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="card p-4 border-red-200 bg-red-50"
      >
        <div className="flex items-center space-x-2 text-red-600">
          <XCircle className="w-5 h-5" />
          <span className="text-sm font-medium">Grammar Analysis Error</span>
        </div>
        <p className="text-red-600 text-sm mt-1">{error}</p>
      </motion.div>
    )
  }

  if (!text || text.trim().length === 0) {
    return (
      <div className="card p-4 border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-2 text-gray-500">
          <MessageSquare className="w-5 h-5" />
          <span className="text-sm">Start speaking to see grammar feedback...</span>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-4"
    >
      {/* Header with confidence score */}
      <div className="card p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Target className="w-5 h-5 text-blue-500" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Grammar Analysis
            </h3>
          </div>
          
          {isAnalyzing && (
            <div className="flex items-center space-x-2 text-blue-500">
              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
              <span className="text-sm">Analyzing...</span>
            </div>
          )}
        </div>

        {/* Confidence Score */}
        <div className={`rounded-lg p-3 border ${getScoreBackground(confidenceScore)}`}>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Confidence Score</span>
            <span className={`text-2xl font-bold ${getScoreColor(confidenceScore)}`}>
              {Math.round(confidenceScore)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <motion.div
              className={`h-2 rounded-full ${
                confidenceScore >= 85 ? 'bg-green-500' :
                confidenceScore >= 70 ? 'bg-blue-500' :
                confidenceScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
              }`}
              initial={{ width: 0 }}
              animate={{ width: `${confidenceScore}%` }}
              transition={{ duration: 1, ease: "easeOut" }}
            />
          </div>
        </div>

        {/* Quick Stats */}
        {speechMetrics && (
          <div className="grid grid-cols-2 gap-4 mt-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {speechMetrics.wordCount}
              </div>
              <div className="text-sm text-gray-500">Words</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-500">
                {fillerWords.length}
              </div>
              <div className="text-sm text-gray-500">Filler Words</div>
            </div>
          </div>
        )}
      </div>

      {/* Grammar Errors */}
      {grammarErrors.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="card p-4"
        >
          <div className="flex items-center space-x-2 mb-3">
            <AlertTriangle className="w-5 h-5 text-yellow-500" />
            <h4 className="font-semibold text-gray-900 dark:text-white">
              Grammar Issues ({grammarErrors.length})
            </h4>
          </div>
          
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {grammarErrors.slice(0, 5).map((error, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
              >
                <div className="flex items-start space-x-2">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    error.severity === 'high' ? 'bg-red-500' :
                    error.severity === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
                  }`} />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{error.message}</p>
                    {error.suggestions.length > 0 && (
                      <div className="mt-1">
                        <span className="text-xs text-gray-600">Suggestions: </span>
                        <span className="text-xs text-blue-600">
                          {error.suggestions.join(', ')}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
          
          {grammarErrors.length > 5 && (
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="text-sm text-blue-600 hover:text-blue-700 mt-2"
            >
              {showDetails ? 'Show Less' : `Show ${grammarErrors.length - 5} More`}
            </button>
          )}
        </motion.div>
      )}

      {/* Filler Words */}
      {fillerWords.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="card p-4"
        >
          <div className="flex items-center space-x-2 mb-3">
            <Zap className="w-5 h-5 text-orange-500" />
            <h4 className="font-semibold text-gray-900 dark:text-white">
              Filler Words Detected
            </h4>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {fillerWords.slice(0, 10).map((filler, index) => (
              <motion.span
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
                className="px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs font-medium"
              >
                "{filler.word}"
              </motion.span>
            ))}
          </div>
          
          {speechMetrics && speechMetrics.fillerWordPercentage > 0 && (
            <div className="mt-2 text-sm text-gray-600">
              {speechMetrics.fillerWordPercentage.toFixed(1)}% of your speech contains filler words
            </div>
          )}
        </motion.div>
      )}

      {/* Feedback */}
      {formattedFeedback.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="card p-4"
        >
          <div className="flex items-center space-x-2 mb-3">
            <TrendingUp className="w-5 h-5 text-green-500" />
            <h4 className="font-semibold text-gray-900 dark:text-white">
              Feedback & Tips
            </h4>
          </div>
          
          <div className="space-y-2">
            {formattedFeedback.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`p-3 rounded-lg border ${
                  item.color === 'red' ? 'bg-red-50 border-red-200' :
                  item.color === 'yellow' ? 'bg-yellow-50 border-yellow-200' :
                  item.color === 'green' ? 'bg-green-50 border-green-200' :
                  'bg-blue-50 border-blue-200'
                }`}
              >
                <div className="flex items-start space-x-2">
                  <span className="text-lg">{item.icon}</span>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{item.message}</p>
                    {item.category && (
                      <span className="text-xs text-gray-500 capitalize">
                        {item.category.replace('_', ' ')}
                      </span>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Suggestions */}
      {suggestions.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="card p-4"
        >
          <div className="flex items-center space-x-2 mb-3">
            <Info className="w-5 h-5 text-blue-500" />
            <h4 className="font-semibold text-gray-900 dark:text-white">
              Improvement Suggestions
            </h4>
          </div>
          
          <div className="space-y-2">
            {suggestions.map((suggestion, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-3 bg-blue-50 border border-blue-200 rounded-lg"
              >
                <div className="flex items-start space-x-2">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    suggestion.priority === 'high' ? 'bg-red-500' :
                    suggestion.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                  }`} />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{suggestion.title}</p>
                    <p className="text-sm text-gray-600">{suggestion.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Positive feedback when everything is good */}
      {summary && !summary.hasErrors && !summary.hasFillers && summary.confidenceScore > 80 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="card p-4 bg-green-50 border-green-200"
        >
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span className="text-green-700 font-medium">
              Excellent! Your speech shows great clarity and confidence.
            </span>
          </div>
        </motion.div>
      )}
    </motion.div>
  )
}

export default GrammarFeedback
