import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppStore } from '../../store/appStore';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const TimeTravelReplay = ({ isOpen, onClose }) => {
  const { sessionRecording, neuroAI, currentSession } = useAppStore();
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [emotionData, setEmotionData] = useState([]);
  const [stressData, setStressData] = useState([]);
  const intervalRef = useRef(null);

  useEffect(() => {
    if (sessionRecording.events.length > 0) {
      processSessionData();
    }
  }, [sessionRecording.events]);

  useEffect(() => {
    if (isPlaying) {
      intervalRef.current = setInterval(() => {
        setCurrentTime(prev => {
          const maxTime = getSessionDuration();
          if (prev >= maxTime) {
            setIsPlaying(false);
            return maxTime;
          }
          return prev + (100 * playbackSpeed); // 100ms increments
        });
      }, 100);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isPlaying, playbackSpeed]);

  const processSessionData = () => {
    const events = sessionRecording.events;
    const startTime = events[0]?.timestamp || Date.now();
    
    const emotions = [];
    const stress = [];
    
    events.forEach(event => {
      const relativeTime = event.timestamp - startTime;
      
      if (event.type === 'neuro_update') {
        emotions.push({
          x: relativeTime / 1000, // Convert to seconds
          y: getEmotionScore(event.data.emotionalState),
          label: event.data.emotionalState
        });
        
        stress.push({
          x: relativeTime / 1000,
          y: event.data.stressLevel * 100,
          confidence: event.data.confidenceLevel * 100
        });
      }
    });
    
    setEmotionData(emotions);
    setStressData(stress);
  };

  const getEmotionScore = (emotion) => {
    const scores = {
      'positive': 80,
      'neutral': 50,
      'stressed': 20,
      'anxious': 10,
      'confident': 90
    };
    return scores[emotion] || 50;
  };

  const getSessionDuration = () => {
    if (sessionRecording.events.length === 0) return 0;
    const startTime = sessionRecording.events[0].timestamp;
    const endTime = sessionRecording.events[sessionRecording.events.length - 1].timestamp;
    return endTime - startTime;
  };

  const formatTime = (milliseconds) => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getEventsAtTime = (time) => {
    const startTime = sessionRecording.events[0]?.timestamp || Date.now();
    const targetTime = startTime + time;
    
    return sessionRecording.events.filter(event => 
      Math.abs(event.timestamp - targetTime) < 1000 // Within 1 second
    );
  };

  const getEventIcon = (eventType) => {
    const icons = {
      'suggestion': '💡',
      'stress_spike': '⚠️',
      'confidence_boost': '🚀',
      'hesitation': '🤔',
      'great_answer': '⭐',
      'neuro_update': '🧠',
      'gesture': '👋',
      'eye_contact': '👁️'
    };
    return icons[eventType] || '📝';
  };

  const getEventColor = (eventType) => {
    const colors = {
      'suggestion': 'bg-blue-500',
      'stress_spike': 'bg-red-500',
      'confidence_boost': 'bg-green-500',
      'hesitation': 'bg-yellow-500',
      'great_answer': 'bg-purple-500',
      'neuro_update': 'bg-indigo-500',
      'gesture': 'bg-pink-500',
      'eye_contact': 'bg-cyan-500'
    };
    return colors[eventType] || 'bg-gray-500';
  };

  const emotionChartData = {
    labels: emotionData.map(point => formatTime(point.x * 1000)),
    datasets: [
      {
        label: 'Emotional State',
        data: emotionData.map(point => point.y),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      }
    ]
  };

  const stressChartData = {
    labels: stressData.map(point => formatTime(point.x * 1000)),
    datasets: [
      {
        label: 'Stress Level',
        data: stressData.map(point => point.y),
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Confidence Level',
        data: stressData.map(point => point.confidence),
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
        fill: true,
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          color: 'white'
        }
      },
      title: {
        display: false,
      },
    },
    scales: {
      x: {
        ticks: {
          color: 'white'
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      y: {
        ticks: {
          color: 'white'
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    maintainAspectRatio: false,
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        className="bg-gray-900 rounded-2xl w-full max-w-6xl h-5/6 overflow-hidden"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">🕰️ Time-Travel Session Replay</h2>
              <p className="text-purple-100">Relive your interview with AI-powered insights</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-purple-200 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="flex h-full">
          {/* Timeline Sidebar */}
          <div className="w-1/3 bg-gray-800 p-4 overflow-y-auto">
            <h3 className="text-white font-semibold mb-4">Session Timeline</h3>
            
            {/* Playback Controls */}
            <div className="bg-gray-700 rounded-lg p-4 mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <button
                  onClick={() => setIsPlaying(!isPlaying)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  {isPlaying ? '⏸️' : '▶️'}
                </button>
                
                <select
                  value={playbackSpeed}
                  onChange={(e) => setPlaybackSpeed(Number(e.target.value))}
                  className="bg-gray-600 text-white rounded px-2 py-1"
                >
                  <option value={0.5}>0.5x</option>
                  <option value={1}>1x</option>
                  <option value={2}>2x</option>
                  <option value={4}>4x</option>
                </select>
              </div>
              
              <div className="text-white text-sm">
                {formatTime(currentTime)} / {formatTime(getSessionDuration())}
              </div>
              
              <input
                type="range"
                min={0}
                max={getSessionDuration()}
                value={currentTime}
                onChange={(e) => setCurrentTime(Number(e.target.value))}
                className="w-full mt-2"
              />
            </div>

            {/* Event Timeline */}
            <div className="space-y-2">
              {sessionRecording.events.map((event, index) => {
                const relativeTime = event.timestamp - (sessionRecording.events[0]?.timestamp || 0);
                const isActive = Math.abs(currentTime - relativeTime) < 2000;
                
                return (
                  <motion.div
                    key={index}
                    className={`p-3 rounded-lg cursor-pointer transition-all ${
                      isActive 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                    onClick={() => {
                      setCurrentTime(relativeTime);
                      setSelectedEvent(event);
                    }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getEventIcon(event.type)}</span>
                      <div className="flex-1">
                        <div className="font-medium text-sm">{event.type.replace('_', ' ')}</div>
                        <div className="text-xs opacity-75">{formatTime(relativeTime)}</div>
                      </div>
                      <div className={`w-2 h-2 rounded-full ${getEventColor(event.type)}`}></div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {/* Current Event Details */}
            {selectedEvent && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 rounded-lg p-4 mb-6 text-white"
              >
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-2xl">{getEventIcon(selectedEvent.type)}</span>
                  <h3 className="text-lg font-semibold capitalize">
                    {selectedEvent.type.replace('_', ' ')}
                  </h3>
                </div>
                <p className="text-gray-300 mb-2">{selectedEvent.description}</p>
                {selectedEvent.data && (
                  <pre className="text-xs bg-gray-700 p-2 rounded overflow-x-auto">
                    {JSON.stringify(selectedEvent.data, null, 2)}
                  </pre>
                )}
              </motion.div>
            )}

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Emotional State Chart */}
              <div className="bg-gray-800 rounded-lg p-4">
                <h3 className="text-white font-semibold mb-4">Emotional Journey</h3>
                <div className="h-64">
                  <Line data={emotionChartData} options={chartOptions} />
                </div>
              </div>

              {/* Stress & Confidence Chart */}
              <div className="bg-gray-800 rounded-lg p-4">
                <h3 className="text-white font-semibold mb-4">Stress & Confidence</h3>
                <div className="h-64">
                  <Line data={stressChartData} options={chartOptions} />
                </div>
              </div>
            </div>

            {/* AI Commentary */}
            <div className="bg-gray-800 rounded-lg p-4 mt-6 text-white">
              <h3 className="font-semibold mb-4">🤖 AI Commentary</h3>
              <div className="space-y-3">
                <div className="bg-blue-600/20 border-l-4 border-blue-600 p-3 rounded">
                  <p className="text-sm">
                    <strong>Peak Performance:</strong> You showed excellent confidence between 
                    {formatTime(Math.max(...stressData.map(d => d.confidence)) * 1000)} when discussing your technical experience.
                  </p>
                </div>
                <div className="bg-yellow-600/20 border-l-4 border-yellow-600 p-3 rounded">
                  <p className="text-sm">
                    <strong>Improvement Opportunity:</strong> Consider practicing breathing techniques 
                    to manage stress spikes during challenging questions.
                  </p>
                </div>
                <div className="bg-green-600/20 border-l-4 border-green-600 p-3 rounded">
                  <p className="text-sm">
                    <strong>Strength:</strong> Your emotional regulation improved significantly 
                    throughout the session, showing great adaptability.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default TimeTravelReplay;
