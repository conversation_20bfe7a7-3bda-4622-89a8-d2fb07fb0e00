import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppStore } from '../../store/appStore';
import { Hands } from '@mediapipe/hands';
import { Camera } from '@mediapipe/camera_utils';

const GestureControls = () => {
  const { gestureControls, updateGestureControls, isRecording } = useAppStore();
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const handsRef = useRef(null);
  const cameraRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [currentGesture, setCurrentGesture] = useState(null);
  const [gestureConfidence, setGestureConfidence] = useState(0);
  const [showGestureGuide, setShowGestureGuide] = useState(false);
  const [lastGestureTime, setLastGestureTime] = useState(0);

  // Gesture definitions
  const gestureDefinitions = {
    'peace': {
      name: 'Two Fingers (Peace)',
      action: 'Mute/Unmute AI',
      icon: '✌️',
      description: 'Show two fingers (peace sign) to toggle AI suggestions',
      landmarks: [8, 12], // Index and middle finger tips
      threshold: 0.8
    },
    'open_palm': {
      name: 'Open Palm',
      action: 'Show Last Tip',
      icon: '👋',
      description: 'Show open palm to display the last AI suggestion again',
      landmarks: [4, 8, 12, 16, 20], // All fingertips
      threshold: 0.7
    },
    'pinch': {
      name: 'Pinch',
      action: 'Expand Sidebar',
      icon: '🤏',
      description: 'Pinch thumb and index finger to temporarily expand sidebar',
      landmarks: [4, 8], // Thumb and index finger tips
      threshold: 0.9
    },
    'thumbs_up': {
      name: 'Thumbs Up',
      action: 'Mark as Helpful',
      icon: '👍',
      description: 'Thumbs up to mark current suggestion as helpful',
      landmarks: [4], // Thumb tip
      threshold: 0.8
    },
    'fist': {
      name: 'Closed Fist',
      action: 'Emergency Stop',
      icon: '✊',
      description: 'Closed fist to immediately stop all AI assistance',
      landmarks: [], // No extended fingers
      threshold: 0.9
    }
  };

  useEffect(() => {
    const initializeGestureRecognition = async () => {
      try {
        const hands = new Hands({
          locateFile: (file) => `https://cdn.jsdelivr.net/npm/@mediapipe/hands/${file}`
        });

        hands.setOptions({
          maxNumHands: 2,
          modelComplexity: 1,
          minDetectionConfidence: 0.7,
          minTrackingConfidence: 0.5
        });

        hands.onResults(onHandsResults);
        handsRef.current = hands;
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize gesture recognition:', error);
      }
    };

    initializeGestureRecognition();
  }, []);

  useEffect(() => {
    if (gestureControls.enabled && isInitialized && isRecording) {
      startGestureDetection();
    } else {
      stopGestureDetection();
    }
  }, [gestureControls.enabled, isInitialized, isRecording]);

  const startGestureDetection = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: 640, height: 480 }
      });

      videoRef.current.srcObject = stream;

      const camera = new Camera(videoRef.current, {
        onFrame: async () => {
          if (handsRef.current) {
            await handsRef.current.send({ image: videoRef.current });
          }
        },
        width: 640,
        height: 480
      });

      camera.start();
      cameraRef.current = camera;

      updateGestureControls({ handsDetected: true });
    } catch (error) {
      console.error('Failed to start gesture detection:', error);
    }
  };

  const stopGestureDetection = () => {
    if (cameraRef.current) {
      cameraRef.current.stop();
      cameraRef.current = null;
    }

    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = videoRef.current.srcObject.getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }

    updateGestureControls({ handsDetected: false });
  };

  const onHandsResults = (results) => {
    if (results.multiHandLandmarks && results.multiHandLandmarks.length > 0) {
      const landmarks = results.multiHandLandmarks[0];
      const gesture = recognizeGesture(landmarks);
      
      if (gesture && gesture.confidence > gesture.threshold) {
        const now = Date.now();
        
        // Prevent rapid gesture triggering
        if (now - lastGestureTime > 1000) {
          setCurrentGesture(gesture);
          setGestureConfidence(gesture.confidence);
          executeGestureAction(gesture.type);
          setLastGestureTime(now);
          
          updateGestureControls({
            lastGesture: gesture.type,
            gestureHistory: [...gestureControls.gestureHistory.slice(-9), {
              type: gesture.type,
              confidence: gesture.confidence,
              timestamp: now
            }]
          });
        }
      }
    }

    // Draw hand landmarks on canvas for debugging
    if (canvasRef.current && results.multiHandLandmarks) {
      drawHandLandmarks(results.multiHandLandmarks);
    }
  };

  const recognizeGesture = (landmarks) => {
    const fingerStates = getFingerStates(landmarks);
    
    // Peace sign (two fingers up)
    if (fingerStates.index && fingerStates.middle && !fingerStates.ring && !fingerStates.pinky) {
      return {
        type: 'peace',
        confidence: 0.9,
        threshold: gestureDefinitions.peace.threshold
      };
    }
    
    // Open palm (all fingers up)
    if (fingerStates.thumb && fingerStates.index && fingerStates.middle && 
        fingerStates.ring && fingerStates.pinky) {
      return {
        type: 'open_palm',
        confidence: 0.8,
        threshold: gestureDefinitions.open_palm.threshold
      };
    }
    
    // Pinch (thumb and index close)
    const thumbTip = landmarks[4];
    const indexTip = landmarks[8];
    const distance = calculateDistance(thumbTip, indexTip);
    
    if (distance < 0.05 && !fingerStates.middle && !fingerStates.ring && !fingerStates.pinky) {
      return {
        type: 'pinch',
        confidence: Math.max(0, 1 - distance * 20),
        threshold: gestureDefinitions.pinch.threshold
      };
    }
    
    // Thumbs up
    if (fingerStates.thumb && !fingerStates.index && !fingerStates.middle && 
        !fingerStates.ring && !fingerStates.pinky) {
      const thumbDirection = landmarks[4].y - landmarks[3].y;
      if (thumbDirection < -0.1) { // Thumb pointing up
        return {
          type: 'thumbs_up',
          confidence: 0.85,
          threshold: gestureDefinitions.thumbs_up.threshold
        };
      }
    }
    
    // Closed fist (no fingers up)
    if (!fingerStates.thumb && !fingerStates.index && !fingerStates.middle && 
        !fingerStates.ring && !fingerStates.pinky) {
      return {
        type: 'fist',
        confidence: 0.9,
        threshold: gestureDefinitions.fist.threshold
      };
    }
    
    return null;
  };

  const getFingerStates = (landmarks) => {
    return {
      thumb: landmarks[4].y < landmarks[3].y,
      index: landmarks[8].y < landmarks[6].y,
      middle: landmarks[12].y < landmarks[10].y,
      ring: landmarks[16].y < landmarks[14].y,
      pinky: landmarks[20].y < landmarks[18].y
    };
  };

  const calculateDistance = (point1, point2) => {
    return Math.sqrt(
      Math.pow(point1.x - point2.x, 2) + 
      Math.pow(point1.y - point2.y, 2) + 
      Math.pow(point1.z - point2.z, 2)
    );
  };

  const executeGestureAction = (gestureType) => {
    const actions = {
      'peace': () => {
        // Toggle AI mute/unmute
        updateGestureControls({ silentMode: !gestureControls.silentMode });
        showGestureNotification('AI suggestions ' + (gestureControls.silentMode ? 'enabled' : 'muted'));
      },
      'open_palm': () => {
        // Show last tip again
        showGestureNotification('Showing last AI suggestion');
        // Trigger show last suggestion in parent component
        window.dispatchEvent(new CustomEvent('showLastSuggestion'));
      },
      'pinch': () => {
        // Expand sidebar temporarily
        showGestureNotification('Sidebar expanded');
        window.dispatchEvent(new CustomEvent('expandSidebar', { detail: { temporary: true } }));
      },
      'thumbs_up': () => {
        // Mark current suggestion as helpful
        showGestureNotification('Marked as helpful!');
        window.dispatchEvent(new CustomEvent('markSuggestionHelpful'));
      },
      'fist': () => {
        // Emergency stop
        showGestureNotification('Emergency stop activated');
        window.dispatchEvent(new CustomEvent('emergencyStop'));
      }
    };

    if (actions[gestureType]) {
      actions[gestureType]();
    }
  };

  const showGestureNotification = (message) => {
    // Create a temporary notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-20 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-pulse';
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 2000);
  };

  const drawHandLandmarks = (handLandmarks) => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = 'red';
    
    handLandmarks.forEach(landmarks => {
      landmarks.forEach(landmark => {
        ctx.beginPath();
        ctx.arc(landmark.x * canvas.width, landmark.y * canvas.height, 3, 0, 2 * Math.PI);
        ctx.fill();
      });
    });
  };

  return (
    <div className="gesture-controls">
      {/* Hidden video and canvas for processing */}
      <video
        ref={videoRef}
        className="hidden"
        autoPlay
        muted
        playsInline
      />
      
      <canvas
        ref={canvasRef}
        className="hidden"
        width={640}
        height={480}
      />

      {/* Gesture Controls Toggle */}
      <motion.div
        className="fixed bottom-4 left-4 z-50"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
      >
        <button
          onClick={() => updateGestureControls({ enabled: !gestureControls.enabled })}
          className={`p-3 rounded-full shadow-lg transition-all ${
            gestureControls.enabled 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-700 text-gray-300'
          }`}
        >
          👋
        </button>
        
        {gestureControls.enabled && (
          <button
            onClick={() => setShowGestureGuide(!showGestureGuide)}
            className="ml-2 p-2 bg-gray-700 text-white rounded-full shadow-lg"
          >
            ❓
          </button>
        )}
      </motion.div>

      {/* Current Gesture Indicator */}
      <AnimatePresence>
        {currentGesture && gestureControls.enabled && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed top-1/2 left-4 transform -translate-y-1/2 bg-gray-900/90 backdrop-blur-sm rounded-lg p-4 text-white z-50"
          >
            <div className="flex items-center space-x-3">
              <span className="text-3xl">{gestureDefinitions[currentGesture.type]?.icon}</span>
              <div>
                <div className="font-semibold">{gestureDefinitions[currentGesture.type]?.name}</div>
                <div className="text-sm text-gray-300">{gestureDefinitions[currentGesture.type]?.action}</div>
                <div className="text-xs text-blue-400">
                  Confidence: {Math.round(gestureConfidence * 100)}%
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Gesture Guide Modal */}
      <AnimatePresence>
        {showGestureGuide && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-gray-900 rounded-2xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white">👋 Gesture Commands Guide</h2>
                <button
                  onClick={() => setShowGestureGuide(false)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                {Object.entries(gestureDefinitions).map(([key, gesture]) => (
                  <motion.div
                    key={key}
                    className="bg-gray-800 rounded-lg p-4 flex items-center space-x-4"
                    whileHover={{ scale: 1.02 }}
                  >
                    <span className="text-4xl">{gesture.icon}</span>
                    <div className="flex-1">
                      <h3 className="text-white font-semibold">{gesture.name}</h3>
                      <p className="text-blue-400 font-medium">{gesture.action}</p>
                      <p className="text-gray-400 text-sm">{gesture.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>

              <div className="mt-6 p-4 bg-blue-600/20 border border-blue-600/30 rounded-lg">
                <h3 className="text-white font-semibold mb-2">💡 Tips for Best Results</h3>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• Keep your hand clearly visible to the camera</li>
                  <li>• Hold gestures for 1-2 seconds for recognition</li>
                  <li>• Ensure good lighting for accurate detection</li>
                  <li>• Practice gestures before important interviews</li>
                </ul>
              </div>

              {/* Gesture History */}
              {gestureControls.gestureHistory.length > 0 && (
                <div className="mt-6">
                  <h3 className="text-white font-semibold mb-3">Recent Gestures</h3>
                  <div className="space-y-2">
                    {gestureControls.gestureHistory.slice(-5).map((gesture, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-800 rounded p-2">
                        <div className="flex items-center space-x-2">
                          <span>{gestureDefinitions[gesture.type]?.icon}</span>
                          <span className="text-white text-sm">{gestureDefinitions[gesture.type]?.name}</span>
                        </div>
                        <div className="text-gray-400 text-xs">
                          {new Date(gesture.timestamp).toLocaleTimeString()}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Status Indicator */}
      {gestureControls.enabled && (
        <div className="fixed bottom-16 left-4 z-40">
          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
            gestureControls.handsDetected 
              ? 'bg-green-600 text-white' 
              : 'bg-yellow-600 text-white'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              gestureControls.handsDetected ? 'bg-green-300' : 'bg-yellow-300'
            } animate-pulse`}></div>
            <span>
              {gestureControls.handsDetected ? 'Hands Detected' : 'Looking for Hands'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default GestureControls;
