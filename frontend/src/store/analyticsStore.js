import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import axios from 'axios'

/**
 * Analytics Store
 * Manages user analytics, progress tracking, and learning insights
 */
export const useAnalyticsStore = create(
  persist(
    (set, get) => ({
      // Analytics data
      userStats: null,
      progressData: [],
      weeklySummary: null,
      insights: null,
      dashboardData: null,
      
      // Loading states
      isLoadingStats: false,
      isLoadingProgress: false,
      isLoadingInsights: false,
      isLoadingDashboard: false,
      
      // Error states
      statsError: null,
      progressError: null,
      insightsError: null,
      dashboardError: null,
      
      // Settings
      selectedTimeframe: '30d',
      autoRefresh: true,
      lastRefresh: null,

      // Actions
      setSelectedTimeframe: (timeframe) => set({ selectedTimeframe: timeframe }),
      setAutoRefresh: (enabled) => set({ autoRefresh: enabled }),

      /**
       * Record session analytics
       */
      recordSessionAnalytics: async (sessionId, analytics) => {
        try {
          const response = await axios.post('/api/analytics/record', {
            sessionId,
            analytics
          })
          
          if (response.data.success) {
            // Refresh dashboard data after recording
            const { refreshDashboard } = get()
            refreshDashboard()
            return true
          }
          return false
        } catch (error) {
          console.error('Failed to record analytics:', error)
          return false
        }
      },

      /**
       * Load user statistics
       */
      loadUserStats: async (userName) => {
        set({ isLoadingStats: true, statsError: null })
        
        try {
          const response = await axios.get(`/api/analytics/stats/${userName}`)
          
          if (response.data.success) {
            set({ 
              userStats: response.data.stats,
              isLoadingStats: false,
              lastRefresh: new Date().toISOString()
            })
            return response.data.stats
          } else {
            set({ 
              statsError: 'Failed to load statistics',
              isLoadingStats: false 
            })
            return null
          }
        } catch (error) {
          console.error('Error loading user stats:', error)
          set({ 
            statsError: error.response?.data?.error || 'Failed to load statistics',
            isLoadingStats: false 
          })
          return null
        }
      },

      /**
       * Load progress data
       */
      loadProgressData: async (userName, timeframe = null) => {
        const selectedTimeframe = timeframe || get().selectedTimeframe
        set({ isLoadingProgress: true, progressError: null })
        
        try {
          const response = await axios.get(`/api/analytics/progress/${userName}`, {
            params: { timeframe: selectedTimeframe }
          })
          
          if (response.data.success) {
            set({ 
              progressData: response.data.progress,
              isLoadingProgress: false,
              lastRefresh: new Date().toISOString()
            })
            return response.data.progress
          } else {
            set({ 
              progressError: 'Failed to load progress data',
              isLoadingProgress: false 
            })
            return []
          }
        } catch (error) {
          console.error('Error loading progress data:', error)
          set({ 
            progressError: error.response?.data?.error || 'Failed to load progress data',
            isLoadingProgress: false 
          })
          return []
        }
      },

      /**
       * Load weekly summary
       */
      loadWeeklySummary: async (userName) => {
        try {
          const response = await axios.get(`/api/analytics/weekly-summary/${userName}`)
          
          if (response.data.success) {
            set({ weeklySummary: response.data.summary })
            return response.data.summary
          }
          return null
        } catch (error) {
          console.error('Error loading weekly summary:', error)
          return null
        }
      },

      /**
       * Load learning insights
       */
      loadInsights: async (userName) => {
        set({ isLoadingInsights: true, insightsError: null })
        
        try {
          const response = await axios.get(`/api/analytics/insights/${userName}`)
          
          if (response.data.success) {
            set({ 
              insights: response.data,
              isLoadingInsights: false,
              lastRefresh: new Date().toISOString()
            })
            return response.data
          } else {
            set({ 
              insightsError: 'Failed to load insights',
              isLoadingInsights: false 
            })
            return null
          }
        } catch (error) {
          console.error('Error loading insights:', error)
          set({ 
            insightsError: error.response?.data?.error || 'Failed to load insights',
            isLoadingInsights: false 
          })
          return null
        }
      },

      /**
       * Load comprehensive dashboard data
       */
      loadDashboard: async (userName, timeframe = null) => {
        const selectedTimeframe = timeframe || get().selectedTimeframe
        set({ isLoadingDashboard: true, dashboardError: null })
        
        try {
          const response = await axios.get(`/api/analytics/dashboard/${userName}`, {
            params: { timeframe: selectedTimeframe }
          })
          
          if (response.data.success) {
            const dashboard = response.data.dashboard
            set({ 
              dashboardData: dashboard,
              userStats: dashboard.stats,
              progressData: dashboard.progress,
              weeklySummary: dashboard.weeklySummary,
              insights: dashboard.insights,
              isLoadingDashboard: false,
              lastRefresh: new Date().toISOString()
            })
            return dashboard
          } else {
            set({ 
              dashboardError: 'Failed to load dashboard',
              isLoadingDashboard: false 
            })
            return null
          }
        } catch (error) {
          console.error('Error loading dashboard:', error)
          set({ 
            dashboardError: error.response?.data?.error || 'Failed to load dashboard',
            isLoadingDashboard: false 
          })
          return null
        }
      },

      /**
       * Refresh dashboard data
       */
      refreshDashboard: async () => {
        const { dashboardData } = get()
        if (dashboardData?.userName) {
          return get().loadDashboard(dashboardData.userName)
        }
      },

      /**
       * Export analytics data
       */
      exportAnalytics: async (userName, format = 'json', timeframe = '30d') => {
        try {
          const response = await axios.get(`/api/analytics/export/${userName}`, {
            params: { format, timeframe },
            responseType: format === 'csv' ? 'blob' : 'json'
          })

          if (format === 'csv') {
            // Handle CSV download
            const blob = new Blob([response.data], { type: 'text/csv' })
            const url = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = url
            link.download = `${userName}-analytics-${timeframe}.csv`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)
            return true
          } else {
            // Handle JSON download
            const dataStr = JSON.stringify(response.data, null, 2)
            const blob = new Blob([dataStr], { type: 'application/json' })
            const url = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = url
            link.download = `${userName}-analytics-${timeframe}.json`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)
            return true
          }
        } catch (error) {
          console.error('Error exporting analytics:', error)
          return false
        }
      },

      /**
       * Get analytics summary for quick display
       */
      getAnalyticsSummary: () => {
        const { userStats, weeklySummary, insights } = get()
        
        if (!userStats) return null

        return {
          totalSessions: userStats.totalSessions || 0,
          totalTime: userStats.totalDuration || 0,
          averageConfidence: userStats.overallConfidence || 0,
          weeklyImprovement: weeklySummary?.improvements?.confidence || 0,
          keyInsights: insights?.insights?.slice(0, 3) || [],
          recommendations: insights?.recommendations?.slice(0, 2) || []
        }
      },

      /**
       * Get progress trend
       */
      getProgressTrend: () => {
        const { progressData } = get()
        
        if (!progressData || progressData.length < 2) {
          return { trend: 'insufficient_data', change: 0 }
        }

        const recent = progressData.slice(-5)
        const older = progressData.slice(-10, -5)
        
        if (older.length === 0) {
          return { trend: 'insufficient_data', change: 0 }
        }

        const recentAvg = recent.reduce((sum, day) => sum + (day.averageConfidence || 0), 0) / recent.length
        const olderAvg = older.reduce((sum, day) => sum + (day.averageConfidence || 0), 0) / older.length
        
        const change = recentAvg - olderAvg
        const trend = change > 5 ? 'improving' : change < -5 ? 'declining' : 'stable'
        
        return { trend, change: Math.round(change * 100) / 100 }
      },

      /**
       * Clear all analytics data
       */
      clearAnalytics: () => set({
        userStats: null,
        progressData: [],
        weeklySummary: null,
        insights: null,
        dashboardData: null,
        statsError: null,
        progressError: null,
        insightsError: null,
        dashboardError: null,
        lastRefresh: null
      }),

      /**
       * Check if data needs refresh
       */
      needsRefresh: () => {
        const { lastRefresh, autoRefresh } = get()
        if (!autoRefresh || !lastRefresh) return true
        
        const lastRefreshTime = new Date(lastRefresh)
        const now = new Date()
        const timeDiff = now - lastRefreshTime
        
        // Refresh if data is older than 5 minutes
        return timeDiff > 5 * 60 * 1000
      }
    }),
    {
      name: 'vocapilot-analytics',
      partialize: (state) => ({
        selectedTimeframe: state.selectedTimeframe,
        autoRefresh: state.autoRefresh
      })
    }
  )
)
