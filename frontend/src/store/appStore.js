import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export const useAppStore = create(
  persist(
    (set, get) => ({
      // User setup data
      resume: null,
      jobDescription: '',
      selectedLanguage: 'en',
      userName: '',

      // Audio settings
      isRecording: false,
      audioPermission: null,
      selectedMicrophone: null,
      selectedSpeaker: null,

      // Session data
      currentSession: null,
      sessions: [],

      // Real-time data
      transcript: '',
      suggestions: [],
      toneAnalysis: null,

      // NeuroAI Guidance Layer
      neuroAI: {
        stressLevel: 0,
        breathingPace: 'normal',
        eyeMovement: null,
        typingPatterns: [],
        cognitiveLoad: 0,
        emotionalState: 'neutral',
        confidenceLevel: 0.5,
        adaptiveMode: true,
      },

      // Time-Travel Replay System
      sessionRecording: {
        isRecording: false,
        events: [],
        timeline: [],
        currentPlayback: null,
        playbackSpeed: 1,
      },

      // Persona-Based Role Simulation
      currentPersona: null,
      availablePersonas: [
        {
          id: 'cto-fintech',
          name: 'CTO of Fintech Startup',
          avatar: '🧓',
          personality: 'analytical',
          questionStyle: 'technical-deep',
          difficulty: 'hard',
        },
        {
          id: 'hr-junior',
          name: 'Junior HR Representative',
          avatar: '🧑‍🎓',
          personality: 'friendly',
          questionStyle: 'behavioral',
          difficulty: 'easy',
        },
        {
          id: 'ai-lead-google',
          name: 'Senior AI Lead from Google',
          avatar: '👩‍💼',
          personality: 'challenging',
          questionStyle: 'system-design',
          difficulty: 'expert',
        },
      ],

      // AR Copilot Panel
      arMode: {
        enabled: false,
        overlayVisible: true,
        holographicSuggestions: [],
        peripheralUI: true,
      },

      // Gesture-Based Controls
      gestureControls: {
        enabled: false,
        handsDetected: false,
        lastGesture: null,
        gestureHistory: [],
        silentMode: false,
      },

      // Real-Time Job Fit Score
      jobFitScore: {
        currentScore: 0,
        scoreHistory: [],
        recommendations: [],
        skillsAnalysis: {},
        careerMap: null,
      },

      // Dream UI 2.0 Settings
      uiTheme: {
        mode: 'celestial', // 'celestial' | 'nebula' | 'starfield'
        animations: true,
        starfieldEnabled: true,
        adaptiveTheme: true,
        glowEffects: true,
        voiceWaveform: true,
      },

      // Smart Features
      smartMode: {
        autoDetection: true,
        currentMode: 'behavioral', // 'technical' | 'behavioral' | 'mock'
        adaptiveQuestions: true,
        contextAwareness: true,
      },

      // Live Translation
      translation: {
        enabled: false,
        targetLanguage: 'en',
        accentTraining: false,
        nativeLanguage: 'en',
      },

      // Actions
      setResume: (resume) => set({ resume }),
      setJobDescription: (jobDescription) => set({ jobDescription }),
      setSelectedLanguage: (language) => set({ selectedLanguage: language }),
      setUserName: (name) => set({ userName: name }),

      setIsRecording: (recording) => set({ isRecording: recording }),
      setAudioPermission: (permission) => set({ audioPermission: permission }),
      setSelectedMicrophone: (mic) => set({ selectedMicrophone: mic }),
      setSelectedSpeaker: (speaker) => set({ selectedSpeaker: speaker }),

      setCurrentSession: (session) => set({ currentSession: session }),
      addSession: (session) => {
        const sessions = get().sessions
        set({ sessions: [session, ...sessions] })
      },

      setTranscript: (transcript) => set({ transcript }),
      addSuggestion: (suggestion) => {
        const suggestions = get().suggestions
        set({ suggestions: [suggestion, ...suggestions.slice(0, 4)] }) // Keep only 5 latest
      },
      clearSuggestions: () => set({ suggestions: [] }),
      setToneAnalysis: (analysis) => set({ toneAnalysis: analysis }),

      // NeuroAI Actions
      updateNeuroAI: (updates) => set((state) => ({
        neuroAI: { ...state.neuroAI, ...updates }
      })),

      // Session Recording Actions
      startSessionRecording: () => set((state) => ({
        sessionRecording: { ...state.sessionRecording, isRecording: true, events: [] }
      })),
      stopSessionRecording: () => set((state) => ({
        sessionRecording: { ...state.sessionRecording, isRecording: false }
      })),
      addSessionEvent: (event) => set((state) => ({
        sessionRecording: {
          ...state.sessionRecording,
          events: [...state.sessionRecording.events, { ...event, timestamp: Date.now() }]
        }
      })),

      // Persona Actions
      setCurrentPersona: (persona) => set({ currentPersona: persona }),

      // AR Mode Actions
      toggleARMode: () => set((state) => ({
        arMode: { ...state.arMode, enabled: !state.arMode.enabled }
      })),
      updateARMode: (updates) => set((state) => ({
        arMode: { ...state.arMode, ...updates }
      })),

      // Gesture Controls Actions
      updateGestureControls: (updates) => set((state) => ({
        gestureControls: { ...state.gestureControls, ...updates }
      })),

      // Job Fit Score Actions
      updateJobFitScore: (score) => set((state) => ({
        jobFitScore: {
          ...state.jobFitScore,
          currentScore: score,
          scoreHistory: [...state.jobFitScore.scoreHistory, { score, timestamp: Date.now() }]
        }
      })),

      // UI Theme Actions
      updateUITheme: (updates) => set((state) => ({
        uiTheme: { ...state.uiTheme, ...updates }
      })),

      // Smart Mode Actions
      updateSmartMode: (updates) => set((state) => ({
        smartMode: { ...state.smartMode, ...updates }
      })),

      // Translation Actions
      updateTranslation: (updates) => set((state) => ({
        translation: { ...state.translation, ...updates }
      })),

      // Reset functions
      resetSession: () => set({
        transcript: '',
        suggestions: [],
        toneAnalysis: null,
        isRecording: false,
        neuroAI: {
          stressLevel: 0,
          breathingPace: 'normal',
          eyeMovement: null,
          typingPatterns: [],
          cognitiveLoad: 0,
          emotionalState: 'neutral',
          confidenceLevel: 0.5,
          adaptiveMode: true,
        },
        sessionRecording: {
          isRecording: false,
          events: [],
          timeline: [],
          currentPlayback: null,
          playbackSpeed: 1,
        },
        jobFitScore: {
          currentScore: 0,
          scoreHistory: [],
          recommendations: [],
          skillsAnalysis: {},
          careerMap: null,
        },
      }),

      resetSetup: () => set({
        resume: null,
        jobDescription: '',
        selectedLanguage: 'en',
        userName: '',
        audioPermission: null,
      }),
    }),
    {
      name: 'vocapilot-store',
      partialize: (state) => ({
        userName: state.userName,
        jobDescription: state.jobDescription,
        selectedLanguage: state.selectedLanguage,
        audioPermission: state.audioPermission,
        selectedMicrophone: state.selectedMicrophone,
        selectedSpeaker: state.selectedSpeaker,
        uiTheme: state.uiTheme,
        smartMode: state.smartMode,
        translation: state.translation,
      }),
    }
  )
)
