# VocaPilot Frontend Environment Configuration
# Copy this file to .env and update the values as needed

# API Configuration
VITE_API_URL=http://localhost:5000
VITE_WS_URL=http://localhost:5000

# Application Configuration
VITE_APP_NAME=VocaPilot
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION="Your voice. Your guide. In real time."

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG=false
VITE_ENABLE_PWA=true

# Audio Configuration
VITE_AUDIO_SAMPLE_RATE=16000
VITE_AUDIO_CHUNK_SIZE=1024
VITE_MAX_RECORDING_DURATION=3600  # 1 hour in seconds

# UI Configuration
VITE_DEFAULT_THEME=light
VITE_ENABLE_DARK_MODE=true
VITE_DEFAULT_LANGUAGE=en

# Performance
VITE_ENABLE_SERVICE_WORKER=true
VITE_ENABLE_CODE_SPLITTING=true

# External Services (Optional)
VITE_SENTRY_DSN=
VITE_GOOGLE_ANALYTICS_ID=
VITE_HOTJAR_ID=

# Development
VITE_MOCK_API=false
VITE_SHOW_DEV_TOOLS=false
