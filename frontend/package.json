{"name": "vocapilot-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@mediapipe/camera_utils": "^0.3.1675466862", "@mediapipe/control_utils": "^0.6.1675466023", "@mediapipe/drawing_utils": "^0.3.1675466124", "@mediapipe/face_mesh": "^0.4.1633559619", "@mediapipe/hands": "^0.4.1675469240", "@mediapipe/pose": "^0.5.1675469404", "@react-three/drei": "^10.6.1", "@react-three/fiber": "^9.3.0", "@tensorflow/tfjs": "^4.22.0", "animejs": "^4.1.2", "axios": "^1.11.0", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "clsx": "^2.1.1", "framer-motion": "^10.16.4", "gsap": "^3.13.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lottie-react": "^2.4.1", "lucide-react": "^0.263.1", "ml-matrix": "^6.12.1", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.30.1", "react-speech-kit": "^3.0.1", "react-webcam": "^7.2.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^1.14.0", "three": "^0.179.1", "tone": "^15.1.22", "zustand": "^4.5.7"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "vite": "^4.4.5", "vitest": "^0.34.1"}}