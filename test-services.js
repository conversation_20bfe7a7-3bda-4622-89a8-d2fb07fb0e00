#!/usr/bin/env node

/**
 * VocaPilot Services Health Check
 * Tests all running services to ensure they're working properly
 */

import axios from 'axios'

const SERVICES = {
  frontend: { url: 'http://localhost:3000', name: 'Frontend (React)' },
  backend: { url: 'http://localhost:5000/health', name: 'Backend API' },
  whisper: { url: 'http://localhost:8001/docs', name: 'Whisper Service' }
}

const COLORS = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${COLORS[color]}${message}${COLORS.reset}`)
}

async function testService(service, url, name) {
  try {
    const response = await axios.get(url, { timeout: 5000 })

    if (response.status === 200) {
      log(`✅ ${name} - OK (${response.status})`, 'green')
      return true
    } else {
      log(`❌ ${name} - Unexpected status: ${response.status}`, 'red')
      return false
    }
  } catch (error) {
    // For frontend, a 404 is expected since it's a React app
    if (service === 'frontend' && error.response && error.response.status === 404) {
      log(`✅ ${name} - OK (React app running, 404 expected)`, 'green')
      return true
    }
    log(`❌ ${name} - Error: ${error.message}`, 'red')
    return false
  }
}

async function testAllServices() {
  log(`${COLORS.bold}🎯 VocaPilot Services Health Check${COLORS.reset}`, 'blue')
  log('Testing all running services...\n')

  let allPassed = true

  for (const [key, service] of Object.entries(SERVICES)) {
    const result = await testService(key, service.url, service.name)
    allPassed = allPassed && result
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  log('\n' + '='.repeat(50))
  
  if (allPassed) {
    log('🎉 All services are running successfully!', 'green')
    log('VocaPilot is ready to use:', 'blue')
    log('• Frontend: http://localhost:3000', 'blue')
    log('• Backend API: http://localhost:5000', 'blue')
    log('• Whisper Service: http://localhost:8000', 'blue')
  } else {
    log('⚠️  Some services have issues. Check the logs above.', 'yellow')
  }
}

// Run the tests
testAllServices().catch(error => {
  console.error('Test runner failed:', error)
  process.exit(1)
})
