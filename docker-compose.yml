version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=http://localhost:5000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=development
      - PORT=5000
      - OLLAMA_URL=http://ollama:11434
      - WHISPER_SERVICE_URL=http://whisper:8001
      - TTS_SERVICE_URL=http://tts:8002
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./uploads:/app/uploads
    depends_on:
      - ollama

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    command: serve

  whisper:
    build:
      context: ./whisper-service
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - PORT=8001
    volumes:
      - ./whisper-service:/app
      - whisper_models:/app/models

  tts:
    build:
      context: ./tts-service
      dockerfile: Dockerfile
    ports:
      - "8002:8001"
    environment:
      - PORT=8001
      - HOST=0.0.0.0
    volumes:
      - ./tts-service:/app
      - tts_models:/app/models

volumes:
  ollama_data:
  whisper_models:
  tts_models:
