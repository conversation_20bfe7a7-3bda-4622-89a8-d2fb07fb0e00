#!/bin/bash

# VocaPilot V2 Installation Script
# This script sets up VocaPilot V2 with all enhanced features

set -e

echo "🚀 VocaPilot V2 Installation Script"
echo "=================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on supported OS
check_os() {
    print_status "Checking operating system..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        print_success "Linux detected"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        print_success "macOS detected"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
        print_success "Windows detected"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        echo "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi
    print_success "Docker found"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        echo "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi
    print_success "Docker Compose found"
    
    # Check Node.js (for development)
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js found: $NODE_VERSION"
    else
        print_warning "Node.js not found. Required for development mode."
    fi
    
    # Check Python (for TTS service)
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        print_success "Python3 found: $PYTHON_VERSION"
    else
        print_warning "Python3 not found. TTS service will run in Docker only."
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Frontend dependencies
    if [ -d "frontend" ]; then
        print_status "Installing frontend dependencies..."
        cd frontend
        if command -v npm &> /dev/null; then
            npm install
            print_success "Frontend dependencies installed"
        else
            print_warning "npm not found, skipping frontend dependency installation"
        fi
        cd ..
    fi
    
    # Backend dependencies
    if [ -d "backend" ]; then
        print_status "Installing backend dependencies..."
        cd backend
        if command -v npm &> /dev/null; then
            npm install
            print_success "Backend dependencies installed"
        else
            print_warning "npm not found, skipping backend dependency installation"
        fi
        cd ..
    fi
    
    # TTS service dependencies
    if [ -d "tts-service" ]; then
        print_status "Installing TTS service dependencies..."
        cd tts-service
        if command -v python3 &> /dev/null && command -v pip3 &> /dev/null; then
            python3 -m venv venv
            source venv/bin/activate
            pip install -r requirements.txt
            print_success "TTS service dependencies installed"
            deactivate
        else
            print_warning "Python3/pip3 not found, TTS service will use Docker"
        fi
        cd ..
    fi
    
    # Whisper service dependencies
    if [ -d "whisper-service" ]; then
        print_status "Installing Whisper service dependencies..."
        cd whisper-service
        if command -v python3 &> /dev/null && command -v pip3 &> /dev/null; then
            python3 -m venv venv
            source venv/bin/activate
            pip install -r requirements.txt
            print_success "Whisper service dependencies installed"
            deactivate
        else
            print_warning "Python3/pip3 not found, Whisper service will use Docker"
        fi
        cd ..
    fi
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        print_status "Creating backend .env file..."
        cat > backend/.env << EOF
NODE_ENV=development
PORT=5000
OLLAMA_URL=http://localhost:11434
WHISPER_SERVICE_URL=http://localhost:8001
TTS_SERVICE_URL=http://localhost:8002
LANGUAGETOOL_URL=https://api.languagetool.org/v2
DATABASE_PATH=./data/vocapilot.db
CORS_ORIGIN=http://localhost:3000
EOF
        print_success "Backend .env file created"
    else
        print_warning "Backend .env file already exists"
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env" ]; then
        print_status "Creating frontend .env file..."
        cat > frontend/.env << EOF
REACT_APP_API_URL=http://localhost:5000
REACT_APP_WS_URL=ws://localhost:5000
REACT_APP_ENVIRONMENT=development
EOF
        print_success "Frontend .env file created"
    else
        print_warning "Frontend .env file already exists"
    fi
    
    # TTS service environment
    if [ ! -f "tts-service/.env" ]; then
        print_status "Creating TTS service .env file..."
        cat > tts-service/.env << EOF
HOST=0.0.0.0
PORT=8001
EOF
        print_success "TTS service .env file created"
    else
        print_warning "TTS service .env file already exists"
    fi
    
    # Whisper service environment
    if [ ! -f "whisper-service/.env" ]; then
        print_status "Creating Whisper service .env file..."
        cat > whisper-service/.env << EOF
HOST=0.0.0.0
PORT=8001
EOF
        print_success "Whisper service .env file created"
    else
        print_warning "Whisper service .env file already exists"
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    # Data directory for database
    mkdir -p backend/data
    print_success "Backend data directory created"
    
    # Uploads directory
    mkdir -p backend/uploads
    print_success "Backend uploads directory created"
    
    # Logs directory
    mkdir -p logs
    print_success "Logs directory created"
    
    # Models directory for AI models
    mkdir -p models
    print_success "Models directory created"
}

# Download AI models
download_models() {
    print_status "Downloading AI models..."
    
    # Check if Ollama is running
    if ! curl -s http://localhost:11434/api/tags &> /dev/null; then
        print_warning "Ollama is not running. Please start Ollama and run 'ollama pull llama3:8b' manually."
    else
        print_status "Pulling Llama 3 model..."
        if ollama pull llama3:8b; then
            print_success "Llama 3 model downloaded"
        else
            print_warning "Failed to download Llama 3 model. You can download it later with 'ollama pull llama3:8b'"
        fi
    fi
}

# Build Docker images
build_docker_images() {
    print_status "Building Docker images..."
    
    if docker-compose build; then
        print_success "Docker images built successfully"
    else
        print_error "Failed to build Docker images"
        exit 1
    fi
}

# Initialize database
initialize_database() {
    print_status "Initializing database..."
    
    # Start backend service temporarily to initialize database
    docker-compose up -d backend
    sleep 5
    
    # Initialize analytics tables
    curl -X POST http://localhost:5000/api/analytics/initialize || print_warning "Failed to initialize analytics tables"
    
    # Stop backend service
    docker-compose stop backend
    
    print_success "Database initialized"
}

# Create startup scripts
create_startup_scripts() {
    print_status "Creating startup scripts..."
    
    # Development startup script
    cat > start-dev.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting VocaPilot V2 in development mode..."

# Start all services
docker-compose up -d

echo "✅ VocaPilot V2 is starting up!"
echo ""
echo "Services will be available at:"
echo "  Frontend: http://localhost:3000"
echo "  Backend:  http://localhost:5000"
echo "  Whisper:  http://localhost:8001"
echo "  TTS:      http://localhost:8002"
echo "  Ollama:   http://localhost:11434"
echo ""
echo "Use 'docker-compose logs -f' to view logs"
echo "Use 'docker-compose down' to stop all services"
EOF
    chmod +x start-dev.sh
    print_success "Development startup script created"
    
    # Production startup script
    cat > start-prod.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting VocaPilot V2 in production mode..."

# Set production environment
export NODE_ENV=production

# Start all services
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

echo "✅ VocaPilot V2 is running in production mode!"
EOF
    chmod +x start-prod.sh
    print_success "Production startup script created"
    
    # Stop script
    cat > stop.sh << 'EOF'
#!/bin/bash
echo "🛑 Stopping VocaPilot V2..."
docker-compose down
echo "✅ VocaPilot V2 stopped"
EOF
    chmod +x stop.sh
    print_success "Stop script created"
}

# Main installation function
main() {
    echo "Starting VocaPilot V2 installation..."
    echo ""
    
    check_os
    check_prerequisites
    install_dependencies
    setup_environment
    create_directories
    build_docker_images
    initialize_database
    create_startup_scripts
    
    echo ""
    print_success "🎉 VocaPilot V2 installation completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Start Ollama and download the Llama 3 model:"
    echo "   ollama pull llama3:8b"
    echo ""
    echo "2. Start VocaPilot V2:"
    echo "   ./start-dev.sh"
    echo ""
    echo "3. Open your browser and go to:"
    echo "   http://localhost:3000"
    echo ""
    echo "For more information, see the documentation in the docs/ folder."
    echo ""
    print_success "Happy learning with VocaPilot V2! 🎤✨"
}

# Run main function
main "$@"
