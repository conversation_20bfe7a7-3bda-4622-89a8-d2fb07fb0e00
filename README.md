# 🎯 VocaPilot

> **"Your voice. Your guide. In real time."**

A free, open-source, real-time AI interview & meeting copilot designed to assist users during live video interviews and calls, offering contextual answers, tone feedback, and resume-based support — with zero paid plans or assessments.

![VocaPilot Demo](https://via.placeholder.com/800x400/3B82F6/FFFFFF?text=VocaPilot+Demo)

## ✨ Features

### 🧠 Live Interview Copilot
- **Real-time audio transcription** with <2s latency using Whisper.cpp
- **AI-powered suggestions** tailored to your resume and job description
- **Contextual answers** generated by open-source LLMs (Llama 3, Mistral)
- **Distraction-free overlay** or side panel display

### 🎤 Real-Time AI Conversation Assistant
- **Live audio transcription** with Whisper.cpp and Vosk fallback
- **AI voice responses** using Coqui TTS for natural conversation
- **Animated chat bubbles** with seamless UI integration
- **Continuous conversation flow** with context awareness

### 🧠 Live Speaking Feedback Engine
- **Grammar analysis** using LanguageTool integration
- **Confidence scoring** with real-time assessment (1-100 scale)
- **Filler word detection** and highlighting ("um", "uh", "like", etc.)
- **Animated feedback cards** with color-coded severity levels
- **Improvement suggestions** with actionable tips

### 🌍 Google Meet Companion (Browser Overlay)
- **Video call detection** for Google Meet, Teams, Zoom
- **Draggable overlay interface** with minimal distraction
- **Real-time transcript display** during calls
- **AI response suggestions** contextual to conversation
- **Grammar tips overlay** for professional communication

### 🔊 Pronunciation Shadowing Mode
- **Curated sentence libraries** with difficulty levels
- **AI-powered pronunciation scoring** with detailed feedback
- **Fluency and intonation analysis** using advanced algorithms
- **Progress tracking** with star ratings and improvement metrics
- **Interactive practice sessions** with immediate feedback

### 📊 Analytics Dashboard
- **Progress visualization** using Chart.js with interactive charts
- **Weekly/monthly tracking** of speaking metrics
- **Performance insights** with AI-generated recommendations
- **Data export** in JSON/CSV formats
- **Local data storage** using IndexedDB for privacy

### 🧩 Daily Roleplay Mode
- **Scenario-based practice** (interviews, meetings, presentations)
- **AI persona management** with contextual responses
- **Guided dialogue sessions** with real-time scoring
- **Professional scenarios** tailored to user's field

### 📁 Enhanced Resume Integration
- **PDF and text resume upload** with automatic parsing
- **Keyword matching** with job descriptions
- **Experience highlighting** for relevant responses
- **Skills-based answer generation**

### 💬 Advanced Multilingual Support
- **5 languages supported**: English, French, Spanish, Arabic, German
- **Auto-detection** or manual language selection
- **Whisper-powered** multilingual speech-to-text
- **Native TTS voices** for each supported language
- **Cultural context awareness** in AI responses

### 🔐 Privacy-First Design
- **Local-first processing** with optional cloud components
- **No external API dependencies** for core functionality
- **Transparent data handling** with user consent
- **Open-source codebase** for full transparency
- **Encrypted local storage** for sensitive data

### 🖥️ Modern UI/UX with Animations
- **Framer Motion animations** for smooth interactions
- **Clean, responsive design** built with React.js + Tailwind CSS
- **Dark/light mode** toggle with system preference detection
- **Mobile-friendly** interface with touch optimizations
- **Accessibility-focused** design with ARIA compliance
- **Real-time visual feedback** with animated progress indicators

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- Docker & Docker Compose
- Git

### One-Command Setup
```bash
git clone https://github.com/vocapilot/vocapilot.git
cd vocapilot
docker-compose up --build
```

That's it! VocaPilot will be available at:
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **Whisper Service**: http://localhost:8001
- **TTS Service**: http://localhost:8002
- **Ollama LLM**: http://localhost:11434

### Manual Setup (Development)

1. **Clone the repository**
```bash
git clone https://github.com/vocapilot/vocapilot.git
cd vocapilot
```

2. **Install dependencies**
```bash
npm run install:all
```

3. **Start services**
```bash
# Start all services
npm run dev

# Or start individually
npm run dev:frontend  # Frontend only
npm run dev:backend   # Backend only
```

4. **Setup Ollama (for AI features)**
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull Llama 3 model
ollama pull llama3
```

## 📖 Documentation

- **[Setup Guide](./SETUP.md)** - Detailed installation instructions
- **[User Guide](./USER-GUIDE.md)** - How to use VocaPilot
- **[Architecture](./ARCHITECTURE.md)** - Technical architecture overview
- **[API Documentation](./docs/api.md)** - Backend API reference
- **[Contributing](./CONTRIBUTING.md)** - How to contribute

## 🧰 Tech Stack

| Component | Technology | Purpose |
|-----------|------------|---------|
| **Frontend** | React.js, Tailwind CSS, Vite | User interface |
| **Backend** | Node.js, Express, Socket.IO | API and real-time communication |
| **Speech-to-Text** | Whisper.cpp | Audio transcription |
| **AI/LLM** | Ollama (Llama 3, Mistral) | Response generation |
| **Resume Parsing** | spaCy, Natural | Text processing |
| **Database** | SQLite | Session storage |
| **Deployment** | Docker, Docker Compose | Containerization |

## 🎮 Usage

### 1. Setup Your Profile
- Enter your name and select preferred language
- Upload your resume (PDF or TXT)
- Paste job description (optional)
- Grant microphone permissions

### 2. Start Live Session
- Click the microphone to start recording
- Speak naturally during your interview
- View real-time transcript and AI suggestions
- Get tone and speech feedback

### 3. Review & Export
- View session history and analytics
- Export transcripts and suggestions
- Track improvement over time
- Download reports as PDF or TXT

## 🔧 Configuration

### Environment Variables

**Frontend (.env)**
```env
VITE_API_URL=http://localhost:5000
```

**Backend (.env)**
```env
NODE_ENV=development
PORT=5000
OLLAMA_URL=http://localhost:11434
WHISPER_SERVICE_URL=http://localhost:8000
```

### Customization
- **Models**: Change Whisper model size in `whisper-service/app.py`
- **Languages**: Add new languages in `backend/services/ai.js`
- **Themes**: Customize colors in `frontend/tailwind.config.js`

## 🧪 Testing

```bash
# Run all tests
npm test

# Frontend tests
npm run test:frontend

# Backend tests  
npm run test:backend

# E2E tests
npm run test:e2e
```

## 📊 Performance

- **Transcription Latency**: <2 seconds
- **AI Response Time**: <3 seconds
- **Memory Usage**: ~500MB (with base Whisper model)
- **CPU Usage**: Moderate (optimized for real-time)

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) for details.

### Development Setup
```bash
# Fork and clone the repo
git clone https://github.com/yourusername/vocapilot.git

# Create feature branch
git checkout -b feature/amazing-feature

# Make changes and test
npm test

# Commit and push
git commit -m "Add amazing feature"
git push origin feature/amazing-feature

# Open a Pull Request
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenAI Whisper** for speech recognition
- **Ollama** for local LLM hosting
- **React.js** and **Tailwind CSS** for the UI
- **The open-source community** for inspiration and tools

## 📞 Support

- **Documentation**: [docs.vocapilot.com](https://docs.vocapilot.com)
- **Issues**: [GitHub Issues](https://github.com/vocapilot/vocapilot/issues)
- **Discussions**: [GitHub Discussions](https://github.com/vocapilot/vocapilot/discussions)
- **Discord**: [Join our community](https://discord.gg/vocapilot)

## 🗺️ Roadmap

- [ ] **Chrome Extension** for seamless integration
- [ ] **Advanced Analytics** with detailed insights
- [ ] **Team Features** for collaborative practice
- [ ] **Mobile App** for iOS and Android
- [ ] **More Languages** (10+ total)
- [ ] **Custom Models** for specific industries

---

<div align="center">

**Made with ❤️ for the open-source community**

[⭐ Star us on GitHub](https://github.com/vocapilot/vocapilot) | [🐦 Follow on Twitter](https://twitter.com/vocapilot) | [💬 Join Discord](https://discord.gg/vocapilot)

</div>
