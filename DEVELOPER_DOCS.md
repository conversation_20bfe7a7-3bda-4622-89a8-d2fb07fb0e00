# 🛠️ VocaPilot X - Developer Documentation

## 🏗️ Architecture Overview

VocaPilot X is built with a modern, modular architecture designed for scalability, performance, and maintainability.

### Technology Stack

#### Frontend
- **React 18**: Core UI framework with hooks and context
- **Zustand**: Lightweight state management
- **Three.js**: 3D graphics and WebGL rendering
- **MediaPipe**: Computer vision and ML models
- **TensorFlow.js**: Machine learning inference
- **Framer Motion**: Advanced animations and transitions
- **Tailwind CSS**: Utility-first styling
- **Chart.js**: Data visualization and analytics

#### Backend
- **Node.js**: Runtime environment
- **Express.js**: Web application framework
- **Socket.IO**: Real-time bidirectional communication
- **Whisper**: Speech-to-text processing
- **Ollama**: Local LLM integration

#### AI/ML Components
- **MediaPipe Face Mesh**: Facial landmark detection
- **MediaPipe Hands**: Hand gesture recognition
- **TensorFlow.js Models**: Emotion recognition
- **Web Speech API**: Voice synthesis and recognition
- **Custom NLP**: Job fit analysis and content classification

## 📁 Project Structure

```
vocapilot-x/
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   ├── NeuroAI/
│   │   │   │   └── NeuroAIGuidance.jsx
│   │   │   ├── TimeTravel/
│   │   │   │   └── TimeTravelReplay.jsx
│   │   │   ├── Persona/
│   │   │   │   └── PersonaSimulator.jsx
│   │   │   ├── Gesture/
│   │   │   │   └── GestureControls.jsx
│   │   │   ├── AR/
│   │   │   │   └── ARCopilotPanel.jsx
│   │   │   ├── JobFit/
│   │   │   │   └── JobFitAnalyzer.jsx
│   │   │   ├── Smart/
│   │   │   │   └── SmartModeDetector.jsx
│   │   │   └── UI/
│   │   │       └── CelestialDashboard.jsx
│   │   ├── store/
│   │   │   └── appStore.js
│   │   ├── hooks/
│   │   ├── utils/
│   │   └── pages/
│   └── package.json
├── backend/
│   ├── src/
│   │   ├── routes/
│   │   ├── services/
│   │   ├── models/
│   │   └── utils/
│   └── package.json
└── docs/
```

## 🧠 NeuroAI Guidance System

### Core Components

#### Facial Analysis Engine
```javascript
// NeuroAIGuidance.jsx
const analyzeEyeMovement = (leftEye, rightEye) => {
  const leftEAR = calculateEyeAspectRatio(leftEye);
  const rightEAR = calculateEyeAspectRatio(rightEye);
  
  return {
    leftEAR,
    rightEAR,
    blinkRate: (leftEAR + rightEAR) / 2,
    timestamp: Date.now()
  };
};
```

#### Stress Detection Algorithm
```javascript
const detectStressFromFace = (landmarks) => {
  const browFurrow = analyzeBrowFurrow(landmarks);
  const jawTension = analyzeJawTension(landmarks);
  const lipTension = analyzeLipTension(landmarks);
  
  return Math.min(1, (browFurrow + jawTension + lipTension) / 3);
};
```

### Integration Points
- **MediaPipe Setup**: Initialize face detection models
- **Real-time Processing**: 30 FPS analysis pipeline
- **State Management**: Zustand store integration
- **UI Feedback**: Stress indicators and breathing guides

### Performance Considerations
- **Model Loading**: Lazy load ML models to reduce initial bundle size
- **Memory Management**: Dispose of unused tensors and models
- **Frame Rate Optimization**: Skip frames during high CPU usage
- **Fallback Modes**: Graceful degradation for low-end devices

## 🕰️ Time-Travel Replay System

### Data Structure
```javascript
const sessionEvent = {
  type: 'suggestion' | 'stress_spike' | 'confidence_boost' | 'hesitation',
  timestamp: Date.now(),
  data: {
    // Event-specific data
  },
  description: 'Human-readable description'
};
```

### Recording Pipeline
```javascript
const addSessionEvent = (event) => {
  set((state) => ({
    sessionRecording: {
      ...state.sessionRecording,
      events: [...state.sessionRecording.events, {
        ...event,
        timestamp: Date.now()
      }]
    }
  }));
};
```

### Playback Engine
- **Timeline Navigation**: Scrub through session events
- **Variable Speed**: 0.5x to 4x playback rates
- **Event Filtering**: Show/hide specific event types
- **Chart Synchronization**: Sync timeline with emotion graphs

### Chart Integration
```javascript
const emotionChartData = {
  labels: emotionData.map(point => formatTime(point.x * 1000)),
  datasets: [{
    label: 'Emotional State',
    data: emotionData.map(point => point.y),
    borderColor: 'rgb(59, 130, 246)',
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    tension: 0.4,
    fill: true,
  }]
};
```

## 🎭 Persona System Architecture

### Persona Definition Schema
```javascript
const personaSchema = {
  id: 'unique-identifier',
  name: 'Display Name',
  avatar: 'emoji',
  personality: 'analytical' | 'friendly' | 'challenging',
  questionStyle: 'technical-deep' | 'behavioral' | 'system-design',
  difficulty: 'easy' | 'hard' | 'expert',
  voice: {
    rate: 0.8,
    pitch: 0.9,
    volume: 0.7
  },
  questionPatterns: ['array', 'of', 'questions'],
  responseStyle: 'analytical',
  followUpProbability: 0.8,
  interruptionStyle: 'technical-clarification',
  stressLevel: 'high',
  culturalContext: 'startup-fast-paced'
};
```

### Question Generation Engine
```javascript
const generatePersonaQuestion = (persona, type = 'follow-up') => {
  const questions = persona.questionPatterns;
  
  if (type === 'opening') {
    // Customize based on job description
    return customizeOpeningQuestion(questions[0]);
  }
  
  const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
  return addPersonaFlavor(randomQuestion, persona);
};
```

### Voice Synthesis Integration
```javascript
const speakPersonaResponse = (text, persona) => {
  const utterance = new SpeechSynthesisUtterance(text);
  utterance.rate = persona.voice.rate;
  utterance.pitch = persona.voice.pitch;
  utterance.volume = persona.voice.volume;
  
  speechSynthesis.speak(utterance);
};
```

## 👋 Gesture Recognition System

### MediaPipe Integration
```javascript
const initializeGestureRecognition = async () => {
  const hands = new Hands({
    locateFile: (file) => `https://cdn.jsdelivr.net/npm/@mediapipe/hands/${file}`
  });

  hands.setOptions({
    maxNumHands: 2,
    modelComplexity: 1,
    minDetectionConfidence: 0.7,
    minTrackingConfidence: 0.5
  });

  hands.onResults(onHandsResults);
  return hands;
};
```

### Gesture Classification
```javascript
const recognizeGesture = (landmarks) => {
  const fingerStates = getFingerStates(landmarks);
  
  // Peace sign detection
  if (fingerStates.index && fingerStates.middle && 
      !fingerStates.ring && !fingerStates.pinky) {
    return { type: 'peace', confidence: 0.9 };
  }
  
  // Additional gesture patterns...
  return null;
};
```

### Action Dispatch System
```javascript
const executeGestureAction = (gestureType) => {
  const actions = {
    'peace': () => toggleAIMute(),
    'open_palm': () => showLastSuggestion(),
    'pinch': () => expandSidebar(),
    'thumbs_up': () => markSuggestionHelpful(),
    'fist': () => emergencyStop()
  };

  if (actions[gestureType]) {
    actions[gestureType]();
  }
};
```

## 🥽 AR Copilot Implementation

### WebXR Setup
```javascript
const initializeWebXR = async () => {
  const session = await navigator.xr.requestSession('immersive-ar', {
    requiredFeatures: ['local'],
    optionalFeatures: ['dom-overlay'],
    domOverlay: { root: document.body }
  });

  const renderer = new THREE.WebGLRenderer({ alpha: true });
  renderer.xr.enabled = true;
  renderer.xr.setSession(session);
  
  return { session, renderer };
};
```

### 3D Object Management
```javascript
const createHolographicElements = () => {
  const scene = new THREE.Scene();
  
  // AI Suggestion Cards
  suggestions.forEach((suggestion, index) => {
    const cardGeometry = new THREE.PlaneGeometry(2, 1);
    const cardMaterial = new THREE.MeshBasicMaterial({
      color: 0x3B82F6,
      transparent: true,
      opacity: 0.8
    });
    
    const card = new THREE.Mesh(cardGeometry, cardMaterial);
    card.position.set(-3 + index * 3, 2 - index * 0.5, -2);
    scene.add(card);
  });
  
  return scene;
};
```

### Fallback System
```javascript
const initializeWebRTCAR = async () => {
  // Fallback for browsers without WebXR
  const stream = await navigator.mediaDevices.getUserMedia({
    video: { facingMode: 'user' }
  });
  
  // Overlay Three.js on video stream
  const renderer = new THREE.WebGLRenderer({ alpha: true });
  renderer.setClearColor(0x000000, 0);
  
  return { stream, renderer };
};
```

## 🎯 Job Fit Analysis Engine

### Scoring Algorithm
```javascript
const analyzeJobFit = async () => {
  const jobRequirements = extractJobRequirements(jobDescription);
  const candidateProfile = analyzeCandidateProfile();
  
  const technicalFit = calculateTechnicalFit(jobRequirements, candidateProfile);
  const culturalFit = calculateCulturalFit(jobRequirements, candidateProfile);
  const experienceFit = calculateExperienceFit(jobRequirements, candidateProfile);
  const communicationFit = calculateCommunicationFit();
  
  const overallScore = Math.round(
    technicalFit * 0.4 + culturalFit * 0.2 + 
    experienceFit * 0.3 + communicationFit * 0.1
  );
  
  return { overallScore, breakdown: { technical, cultural, experience, communication } };
};
```

### NLP Processing
```javascript
const extractJobRequirements = (description) => {
  const requirements = {
    skills: [],
    experience: [],
    education: []
  };
  
  const text = description.toLowerCase();
  
  // Extract technical skills
  const techSkills = ['javascript', 'python', 'react', 'node.js'];
  requirements.skills = techSkills.filter(skill => text.includes(skill));
  
  // Extract experience requirements
  const experienceMatch = text.match(/(\d+)\+?\s*years?\s*(of\s*)?experience/g);
  if (experienceMatch) {
    requirements.experience = experienceMatch.map(match => ({
      years: parseInt(match.match(/\d+/)[0]),
      context: match
    }));
  }
  
  return requirements;
};
```

## 🤖 Smart Mode Detection

### Platform Detection
```javascript
const detectPlatform = () => {
  const currentUrl = window.location.href.toLowerCase();
  const platformPatterns = {
    'hackerrank': ['hackerrank.com', 'hacker rank'],
    'leetcode': ['leetcode.com', 'leet code'],
    'zoom': ['zoom.us', 'zoom meeting']
  };
  
  for (const [platform, patterns] of Object.entries(platformPatterns)) {
    for (const pattern of patterns) {
      if (currentUrl.includes(pattern)) {
        return platform;
      }
    }
  }
  
  return null;
};
```

### Content Analysis
```javascript
const analyzeTranscriptForMode = (text) => {
  const interviewTypeKeywords = {
    technical: ['algorithm', 'data structure', 'coding'],
    behavioral: ['tell me about yourself', 'greatest strength'],
    systemDesign: ['design a system', 'architecture', 'scalability']
  };
  
  const scores = {};
  Object.entries(interviewTypeKeywords).forEach(([mode, keywords]) => {
    let score = 0;
    keywords.forEach(keyword => {
      const matches = (text.match(new RegExp(keyword, 'gi')) || []).length;
      score += matches;
    });
    scores[mode] = score / Math.max(text.length / 100, 1);
  });
  
  const bestMode = Object.entries(scores).reduce((best, [mode, score]) => {
    return score > (best.score || 0) ? { mode, score } : best;
  }, {});
  
  return bestMode.mode ? { mode: bestMode.mode, confidence: bestMode.score } : null;
};
```

## 🌌 Celestial Dashboard

### Three.js Scene Setup
```javascript
const Starfield = ({ count = 5000 }) => {
  const mesh = useRef();
  
  const positions = useMemo(() => {
    const positions = new Float32Array(count * 3);
    for (let i = 0; i < count; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 2000;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 2000;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 2000;
    }
    return positions;
  }, [count]);

  useFrame((state, delta) => {
    if (mesh.current) {
      mesh.current.rotation.x -= delta / 10;
      mesh.current.rotation.y -= delta / 15;
    }
  });

  return (
    <Points ref={mesh} positions={positions} stride={3}>
      <PointMaterial transparent color="#ffffff" size={2} />
    </Points>
  );
};
```

### Performance Constellation
```javascript
const PerformanceConstellation = ({ performance }) => {
  const constellationPoints = useMemo(() => {
    const metrics = [
      { value: neuroAI.confidenceLevel, color: '#3B82F6' },
      { value: 1 - neuroAI.stressLevel, color: '#10B981' },
      { value: jobFitScore.currentScore / 100, color: '#8B5CF6' }
    ];
    
    return metrics.map((metric, index) => {
      const angle = (index / metrics.length) * Math.PI * 2;
      const distance = 50 * metric.value;
      
      return {
        position: [
          Math.cos(angle) * distance,
          Math.sin(angle) * distance,
          0
        ],
        color: metric.color,
        size: 5 + metric.value * 10
      };
    });
  }, [neuroAI, jobFitScore]);

  return (
    <group>
      {constellationPoints.map((point, index) => (
        <mesh key={index} position={point.position}>
          <sphereGeometry args={[point.size, 16, 16]} />
          <meshBasicMaterial color={point.color} transparent opacity={0.8} />
        </mesh>
      ))}
    </group>
  );
};
```

## 📊 State Management

### Zustand Store Structure
```javascript
export const useAppStore = create(
  persist(
    (set, get) => ({
      // Core state
      resume: null,
      jobDescription: '',
      transcript: '',
      suggestions: [],
      
      // NeuroAI state
      neuroAI: {
        stressLevel: 0,
        confidenceLevel: 0.5,
        emotionalState: 'neutral',
        breathingPace: 'normal'
      },
      
      // Session recording
      sessionRecording: {
        isRecording: false,
        events: [],
        timeline: []
      },
      
      // Actions
      updateNeuroAI: (updates) => set((state) => ({
        neuroAI: { ...state.neuroAI, ...updates }
      })),
      
      addSessionEvent: (event) => set((state) => ({
        sessionRecording: {
          ...state.sessionRecording,
          events: [...state.sessionRecording.events, {
            ...event,
            timestamp: Date.now()
          }]
        }
      }))
    }),
    {
      name: 'vocapilot-store',
      partialize: (state) => ({
        // Only persist certain state
        userName: state.userName,
        jobDescription: state.jobDescription,
        uiTheme: state.uiTheme
      })
    }
  )
);
```

## 🔧 Development Setup

### Prerequisites
```bash
Node.js >= 18.0.0
npm >= 9.0.0
Git
```

### Installation
```bash
# Clone repository
git clone https://github.com/your-org/vocapilot-x.git
cd vocapilot-x

# Install frontend dependencies
cd frontend
npm install --legacy-peer-deps

# Install backend dependencies
cd ../backend
npm install

# Start development servers
npm run dev:frontend  # Port 3000
npm run dev:backend   # Port 5000
```

### Environment Variables
```bash
# frontend/.env
VITE_API_URL=http://localhost:5000
VITE_WEBSOCKET_URL=ws://localhost:5000

# backend/.env
PORT=5000
WHISPER_MODEL_PATH=./models/whisper
OLLAMA_URL=http://localhost:11434
```

## 🧪 Testing Strategy

### Unit Tests
```javascript
// components/__tests__/NeuroAIGuidance.test.jsx
import { render, screen } from '@testing-library/react';
import NeuroAIGuidance from '../NeuroAI/NeuroAIGuidance';

test('renders stress indicator when stress level is high', () => {
  const mockStore = {
    neuroAI: { stressLevel: 0.8 },
    isRecording: true
  };
  
  render(<NeuroAIGuidance />, { wrapper: StoreProvider });
  expect(screen.getByText(/stress detected/i)).toBeInTheDocument();
});
```

### Integration Tests
```javascript
// tests/integration/gesture-controls.test.js
describe('Gesture Controls Integration', () => {
  test('peace gesture mutes AI suggestions', async () => {
    const { gestureControls } = renderWithStore(<GestureControls />);
    
    // Simulate peace gesture
    await simulateGesture('peace');
    
    expect(gestureControls.silentMode).toBe(true);
  });
});
```

### Performance Tests
```javascript
// tests/performance/starfield.test.js
describe('Starfield Performance', () => {
  test('maintains 60fps with 5000 particles', async () => {
    const { fps } = await measurePerformance(() => {
      render(<Starfield count={5000} />);
    });
    
    expect(fps).toBeGreaterThan(55);
  });
});
```

## 🚀 Deployment

### Build Process
```bash
# Frontend build
cd frontend
npm run build

# Backend build
cd backend
npm run build

# Docker build
docker build -t vocapilot-x .
```

### Production Configuration
```yaml
# docker-compose.yml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
      
  backend:
    build: ./backend
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
```

## 📈 Performance Optimization

### Bundle Optimization
- **Code Splitting**: Lazy load heavy components
- **Tree Shaking**: Remove unused dependencies
- **Asset Optimization**: Compress images and models
- **CDN Integration**: Serve static assets from CDN

### Runtime Optimization
- **WebWorkers**: Offload ML computations
- **Memory Management**: Dispose of unused resources
- **Frame Rate Control**: Adaptive quality based on performance
- **Caching**: Cache ML models and processed data

### Monitoring
```javascript
// utils/performance.js
export const measurePerformance = (operation) => {
  const start = performance.now();
  const result = operation();
  const end = performance.now();
  
  console.log(`Operation took ${end - start} milliseconds`);
  return result;
};
```

## 🔒 Security Considerations

### Data Privacy
- **Local Processing**: ML inference runs client-side
- **Encrypted Storage**: Sensitive data encrypted at rest
- **Minimal Data Collection**: Only collect necessary analytics
- **User Consent**: Clear opt-in for data usage

### API Security
```javascript
// backend/middleware/auth.js
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.sendStatus(401);
  }
  
  jwt.verify(token, process.env.ACCESS_TOKEN_SECRET, (err, user) => {
    if (err) return res.sendStatus(403);
    req.user = user;
    next();
  });
};
```

## 🤝 Contributing Guidelines

### Code Style
- **ESLint**: Follow configured linting rules
- **Prettier**: Consistent code formatting
- **TypeScript**: Gradual migration to TypeScript
- **Comments**: Document complex algorithms

### Pull Request Process
1. **Feature Branch**: Create from `develop` branch
2. **Tests**: Add tests for new functionality
3. **Documentation**: Update relevant docs
4. **Review**: Get approval from maintainers

### Component Guidelines
```javascript
// components/ExampleComponent.jsx
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAppStore } from '../store/appStore';

/**
 * ExampleComponent - Brief description
 * @param {Object} props - Component props
 * @param {string} props.title - Component title
 * @param {Function} props.onAction - Action callback
 */
const ExampleComponent = ({ title, onAction }) => {
  // Component implementation
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="example-component"
    >
      {/* Component JSX */}
    </motion.div>
  );
};

export default ExampleComponent;
```

---

This documentation provides a comprehensive guide for developers working on VocaPilot X. For specific implementation details, refer to the inline code comments and component documentation.
