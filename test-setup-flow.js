#!/usr/bin/env node

/**
 * Test Setup Flow
 * Tests the complete setup flow to ensure it works correctly
 */

import axios from 'axios'

const COLORS = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${COLORS[color]}${message}${COLORS.reset}`)
}

async function testSetupFlow() {
  log(`${COLORS.bold}🧪 Testing VocaPilot Setup Flow${COLORS.reset}`, 'blue')
  log('Testing complete setup process...\n')

  try {
    // Test 1: Create a session (simulating setup completion)
    log('1. Testing session creation...', 'blue')
    const sessionResponse = await axios.post('http://localhost:5000/api/session/create', {
      userName: 'Test User',
      jobDescription: 'Software Engineer at Tech Company',
      language: 'en'
    })

    if (sessionResponse.data.success) {
      log(`✅ Session created successfully! ID: ${sessionResponse.data.session.id}`, 'green')
      
      // Test 2: Verify session data
      log('2. Verifying session data...', 'blue')
      const session = sessionResponse.data.session
      
      if (session.userName === 'Test User' && 
          session.jobDescription === 'Software Engineer at Tech Company' &&
          session.language === 'en') {
        log('✅ Session data is correct', 'green')
      } else {
        log('❌ Session data mismatch', 'red')
        console.log('Expected:', { userName: 'Test User', jobDescription: 'Software Engineer at Tech Company', language: 'en' })
        console.log('Actual:', session)
      }

      // Test 3: Test session retrieval
      log('3. Testing session retrieval...', 'blue')
      const getUserSessionsResponse = await axios.get(`http://localhost:5000/api/session/user/Test User`)
      
      if (getUserSessionsResponse.data.success) {
        log(`✅ Retrieved ${getUserSessionsResponse.data.sessions.length} sessions for user`, 'green')
      } else {
        log('❌ Failed to retrieve user sessions', 'red')
      }

    } else {
      log('❌ Session creation failed', 'red')
      console.log(sessionResponse.data)
    }

    // Test 4: Test health endpoints
    log('4. Testing service health...', 'blue')
    
    const backendHealth = await axios.get('http://localhost:5000/health')
    if (backendHealth.status === 200) {
      log('✅ Backend health check passed', 'green')
    }

    const whisperHealth = await axios.get('http://localhost:8001/docs')
    if (whisperHealth.status === 200) {
      log('✅ Whisper service health check passed', 'green')
    }

    log('\n' + '='.repeat(50))
    log('🎉 All setup flow tests passed!', 'green')
    log('The setup loop issue should be resolved.', 'blue')

  } catch (error) {
    log('\n❌ Setup flow test failed:', 'red')
    console.error(error.message)
    if (error.response) {
      console.error('Response data:', error.response.data)
    }
  }
}

// Run the tests
testSetupFlow().catch(error => {
  console.error('Test runner failed:', error)
  process.exit(1)
})
