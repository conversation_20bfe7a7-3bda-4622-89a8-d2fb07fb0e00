# VocaPilot Environment Configuration
# Copy this file to .env and update the values as needed

# Application Environment
NODE_ENV=development
APP_NAME=VocaPilot
APP_VERSION=1.0.0

# Frontend Configuration
FRONTEND_URL=http://localhost:3000

# Backend Configuration
BACKEND_PORT=5000
BACKEND_HOST=0.0.0.0

# Database Configuration
DATABASE_PATH=./backend/vocapilot.db
DATABASE_MAX_CONNECTIONS=10

# AI Services
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=llama3
WHISPER_SERVICE_URL=http://localhost:8000

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
CORS_ORIGIN=http://localhost:3000
SESSION_SECRET=your-session-secret-key

# File Upload
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=./backend/uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined

# Features
ENABLE_ANALYTICS=false
ENABLE_DEBUG_MODE=false
ENABLE_OFFLINE_MODE=true

# Performance
ENABLE_COMPRESSION=true
ENABLE_CACHING=true
CACHE_TTL=3600  # 1 hour
