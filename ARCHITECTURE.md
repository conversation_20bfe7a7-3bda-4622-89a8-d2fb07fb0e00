# 🏗️ VocaPilot V2 Architecture

This document provides a comprehensive overview of VocaPilot V2's enhanced technical architecture, featuring advanced AI-powered speech training, real-time feedback, and comprehensive analytics.

## 📋 System Overview

VocaPilot V2 is an advanced distributed system consisting of multiple AI microservices that work together to provide comprehensive speech training and communication improvement. The enhanced architecture includes real-time grammar feedback, pronunciation training, TTS integration, and advanced analytics while maintaining privacy, performance, and scalability.

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │ Whisper Service │
│   (React.js)    │◄──►│   (Node.js)     │◄──►│   (Python)      │
│   + Analytics   │    │   + Grammar     │    │   STT Engine    │
│   + TTS UI      │    │   + TTS API     │    └─────────────────┘
└─────────────────┘    └─────────────────┘              │
         │                       │                       │
         │                       ▼                       ▼
         │              ┌─────────────────┐    ┌─────────────────┐
         │              │    Database     │    │  TTS Service    │
         │              │   (SQLite)      │    │  (Coqui TTS)    │
         │              │   + Analytics   │    │  Voice Synth    │
         │              └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                        ┌─────────────────┐
                        │  Ollama LLM     │    ┌─────────────────┐
                        │  (Llama 3)      │◄──►│ LanguageTool    │
                        │  AI Responses   │    │ Grammar API     │
                        └─────────────────┘    └─────────────────┘
```

## 🧩 Component Architecture

### Frontend (React.js Application)

**Location**: `frontend/`
**Technology**: React.js, Tailwind CSS, Vite
**Port**: 3000

#### Key Components:
- **Pages**: Landing, Setup, LiveCopilot, History, Analytics, PronunciationTrainer
- **Components**: Navbar, Footer, AudioRecorder, TranscriptDisplay, GrammarFeedback, TTSControls
- **Hooks**: useAudioRecording, useWebSocket, useAppStore, useTTS, useGrammarAnalysis, usePronunciationAnalysis
- **Services**: API client, WebSocket client, TTS integration
- **Store**: Zustand for state management, Analytics store

#### Responsibilities:
- User interface and experience
- Audio capture and processing
- Real-time communication with backend
- Session management and history
- Resume upload and display

### Backend (Node.js API Server)

**Location**: `backend/`
**Technology**: Node.js, Express.js, Socket.IO
**Port**: 5000

#### Key Modules:
- **Routes**: `/api/audio`, `/api/ai`, `/api/resume`, `/api/session`
- **Services**: Database, AI, Audio processing, Socket handling
- **Middleware**: Authentication, validation, rate limiting
- **Models**: Session, Resume, Transcript, Suggestion

#### Responsibilities:
- RESTful API endpoints
- Real-time WebSocket communication
- Database operations
- AI service coordination
- File upload handling
- Session management

### Whisper Service (Python Microservice)

**Location**: `whisper-service/`
**Technology**: Python, FastAPI, OpenAI Whisper
**Port**: 8000

#### Key Features:
- **Real-time transcription**: <2s latency
- **Multilingual support**: 5 languages
- **Audio preprocessing**: Format conversion, noise reduction
- **Batch processing**: For longer audio files
- **Model optimization**: GPU acceleration when available

#### Responsibilities:
- Speech-to-text conversion
- Audio format handling
- Language detection
- Confidence scoring
- Real-time streaming

### Ollama LLM Service

**Technology**: Ollama, Llama 3/Mistral models
**Port**: 11434

#### Capabilities:
- **Text generation**: Contextual responses
- **Multilingual**: Responses in target language
- **Local processing**: No external API calls
- **Model management**: Easy model switching
- **Performance optimization**: GPU acceleration

#### Responsibilities:
- AI response generation
- Context understanding
- Resume-based suggestions
- Interview question analysis
- Feedback generation

### Database (SQLite)

**Location**: `backend/vocapilot.db`
**Technology**: SQLite with Node.js sqlite3 driver

#### Schema:
```sql
-- Sessions table
CREATE TABLE sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_name TEXT NOT NULL,
    job_description TEXT,
    language TEXT DEFAULT 'en',
    start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    end_time DATETIME,
    duration INTEGER,
    transcript TEXT,
    suggestions TEXT,
    tone_analysis TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Resumes table
CREATE TABLE resumes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_name TEXT NOT NULL,
    filename TEXT NOT NULL,
    content TEXT NOT NULL,
    parsed_data TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Transcripts table
CREATE TABLE transcripts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id INTEGER,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    text TEXT NOT NULL,
    confidence REAL,
    language TEXT,
    FOREIGN KEY (session_id) REFERENCES sessions (id)
);

-- Suggestions table
CREATE TABLE suggestions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id INTEGER,
    transcript_id INTEGER,
    suggestion TEXT NOT NULL,
    context TEXT,
    confidence REAL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions (id),
    FOREIGN KEY (transcript_id) REFERENCES transcripts (id)
);
```

## 🔄 Data Flow

### 1. Audio Processing Pipeline

```
Microphone → Browser → Frontend → Backend → Whisper → Database
     │                                           │
     └─────────────── Real-time Stream ─────────┘
```

**Detailed Flow:**
1. **Audio Capture**: Browser captures microphone input using WebRTC
2. **Chunking**: Audio is split into 1-second chunks for real-time processing
3. **Format Conversion**: Audio converted to WAV format for Whisper
4. **Transcription**: Whisper service processes audio and returns text
5. **Storage**: Transcript stored in database with confidence scores
6. **Broadcasting**: Real-time transcript sent to frontend via WebSocket

### 2. AI Suggestion Pipeline

```
Transcript → Context Builder → LLM → Response Generator → Frontend
     │              │                        │
     ▼              ▼                        ▼
  Database    Resume + Job Desc         Suggestions
```

**Detailed Flow:**
1. **Trigger**: New transcript triggers AI suggestion generation
2. **Context Building**: Combine transcript with resume and job description
3. **Prompt Engineering**: Create structured prompt for LLM
4. **LLM Processing**: Ollama generates contextual response
5. **Post-processing**: Format and validate AI response
6. **Storage**: Save suggestion to database
7. **Delivery**: Send suggestion to frontend via WebSocket

### 3. Resume Processing Pipeline

```
File Upload → PDF/TXT Parser → NLP Processing → Structured Data → Database
     │              │               │                │
     ▼              ▼               ▼                ▼
   Validation   Text Extraction   Entity Recognition  Storage
```

**Detailed Flow:**
1. **Upload**: User uploads PDF or TXT resume file
2. **Validation**: Check file type, size, and format
3. **Text Extraction**: Extract raw text from PDF or read TXT
4. **NLP Processing**: Use spaCy and Natural.js for entity recognition
5. **Structuring**: Organize data into skills, experience, education
6. **Storage**: Save parsed data as JSON in database
7. **Indexing**: Create searchable index for quick retrieval

## 🔌 API Design

### RESTful Endpoints

#### Audio API (`/api/audio`)
```
POST /transcribe          - Transcribe audio file
POST /analyze-tone        - Analyze speech characteristics
GET  /languages          - Get supported languages
GET  /test               - Test audio services
```

#### AI API (`/api/ai`)
```
POST /suggest            - Generate AI suggestion
POST /feedback/:sessionId - Generate session feedback
GET  /status             - Check AI service status
POST /test               - Test AI generation
GET  /models             - List available models
POST /practice-questions - Generate practice questions
```

#### Resume API (`/api/resume`)
```
POST /upload             - Upload and parse resume
GET  /:userName          - Get user's resume
DELETE /:userName        - Delete user's resume
POST /parse              - Parse resume text
GET  /:userName/analysis - Get resume analysis
```

#### Session API (`/api/session`)
```
POST /create             - Create new session
GET  /:sessionId         - Get session details
PUT  /:sessionId         - Update session
GET  /user/:userName     - Get user's sessions
DELETE /:sessionId       - Delete session
GET  /:sessionId/stats   - Get session statistics
GET  /:sessionId/export  - Export session data
```

### WebSocket Events

#### Client → Server
```javascript
'join-session'     - Join a session room
'audio-transcript' - Send real-time transcript
'audio-data'       - Send audio for tone analysis
'start-recording'  - Start recording session
'stop-recording'   - Stop recording session
'request-feedback' - Request speech feedback
```

#### Server → Client
```javascript
'session-joined'    - Confirm session join
'transcript-update' - Real-time transcript update
'ai-suggestion'     - New AI suggestion
'tone-analysis'     - Speech analysis results
'recording-started' - Recording started confirmation
'recording-stopped' - Recording stopped confirmation
'feedback-response' - Speech feedback
'error'            - Error message
```

## 🔒 Security & Privacy

### Data Protection
- **Local-first**: Core processing happens locally
- **No external APIs**: No data sent to third-party services
- **Encrypted storage**: Sensitive data encrypted at rest
- **Session isolation**: User data separated by session
- **Automatic cleanup**: Old sessions automatically purged

### Authentication & Authorization
- **Session-based**: Simple session management
- **No user accounts**: Privacy-focused, no registration required
- **Rate limiting**: Prevent abuse and DoS attacks
- **Input validation**: All inputs sanitized and validated
- **CORS protection**: Restrict cross-origin requests

### Privacy Features
- **Consent-based**: Clear consent for all data processing
- **Data minimization**: Only collect necessary data
- **Right to deletion**: Users can delete their data
- **Transparency**: Open source for full transparency
- **Local processing**: Audio never leaves user's device unnecessarily

## ⚡ Performance Optimizations

### Frontend Optimizations
- **Code splitting**: Lazy load components
- **Bundle optimization**: Tree shaking and minification
- **Caching**: Service worker for offline functionality
- **Debouncing**: Reduce API calls for real-time features
- **Virtual scrolling**: Handle large transcript lists

### Backend Optimizations
- **Connection pooling**: Efficient database connections
- **Caching**: Redis for session data (optional)
- **Compression**: Gzip response compression
- **Rate limiting**: Prevent resource exhaustion
- **Async processing**: Non-blocking I/O operations

### Audio Processing Optimizations
- **Streaming**: Real-time audio processing
- **Chunking**: Process audio in small segments
- **Format optimization**: Use efficient audio formats
- **GPU acceleration**: Leverage GPU for Whisper when available
- **Model caching**: Keep models in memory

### AI Processing Optimizations
- **Model warming**: Keep LLM models loaded
- **Context caching**: Cache frequently used contexts
- **Batch processing**: Group similar requests
- **Response streaming**: Stream AI responses
- **Fallback mechanisms**: Graceful degradation

## 🐳 Deployment Architecture

### Docker Composition
```yaml
services:
  frontend:    # React.js app with Nginx
  backend:     # Node.js API server
  whisper:     # Python Whisper service
  ollama:      # LLM service
  database:    # SQLite (file-based)
```

### Scaling Considerations
- **Horizontal scaling**: Multiple backend instances
- **Load balancing**: Nginx for request distribution
- **Database scaling**: PostgreSQL for production
- **CDN integration**: Static asset delivery
- **Monitoring**: Health checks and metrics

### Production Deployment
```
Internet → Load Balancer → Frontend (Nginx) → Backend (Node.js)
                              │                    │
                              ▼                    ▼
                         Static Assets        API + WebSocket
                                                   │
                                                   ▼
                                            Whisper + Ollama
```

## 🔧 Configuration Management

### Environment Variables
```bash
# Frontend
VITE_API_URL=http://localhost:5000
VITE_WS_URL=http://localhost:5000

# Backend
NODE_ENV=development
PORT=5000
OLLAMA_URL=http://localhost:11434
WHISPER_SERVICE_URL=http://localhost:8000
DATABASE_PATH=./vocapilot.db

# Whisper Service
MODEL_SIZE=base
DEVICE=auto
HOST=0.0.0.0
PORT=8000
```

### Feature Flags
- **AI_ENABLED**: Enable/disable AI features
- **ANALYTICS_ENABLED**: Enable usage analytics
- **DEBUG_MODE**: Verbose logging
- **OFFLINE_MODE**: Local-only processing

## 📊 Monitoring & Observability

### Metrics Collection
- **Performance**: Response times, throughput
- **Errors**: Error rates, failure types
- **Usage**: Feature adoption, user behavior
- **Resources**: CPU, memory, disk usage

### Logging Strategy
- **Structured logging**: JSON format for parsing
- **Log levels**: Debug, info, warn, error
- **Correlation IDs**: Track requests across services
- **Privacy**: No sensitive data in logs

### Health Checks
```javascript
// Health check endpoints
GET /health           - Overall system health
GET /health/database  - Database connectivity
GET /health/ai        - AI service status
GET /health/audio     - Audio service status
```

## 🔮 Future Architecture Considerations

### Scalability Improvements
- **Microservices**: Further service decomposition
- **Event sourcing**: Audit trail and replay capability
- **CQRS**: Separate read/write models
- **Message queues**: Async processing with Redis/RabbitMQ

### Performance Enhancements
- **Edge computing**: Process audio closer to users
- **Model optimization**: Quantized models for speed
- **Caching layers**: Multi-level caching strategy
- **CDN integration**: Global content delivery

### Feature Additions
- **Real-time collaboration**: Multiple users in session
- **Advanced analytics**: ML-powered insights
- **Integration APIs**: Third-party service connections
- **Mobile apps**: Native iOS/Android applications

---

This architecture provides a solid foundation for VocaPilot while maintaining flexibility for future enhancements and scaling requirements.
