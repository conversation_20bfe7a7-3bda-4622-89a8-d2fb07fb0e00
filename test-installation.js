#!/usr/bin/env node

/**
 * VocaPilot Installation Test Script
 * 
 * This script tests all components of VocaPilot to ensure proper installation
 * and functionality. Run this after setting up VocaPilot to verify everything
 * is working correctly.
 */

import axios from 'axios'
import { spawn } from 'child_process'
import fs from 'fs'
import path from 'path'

const SERVICES = {
  frontend: { url: 'http://localhost:3000', name: 'Frontend (React)' },
  backend: { url: 'http://localhost:5000/health', name: 'Backend API' },
  whisper: { url: 'http://localhost:8000/health', name: 'Whisper Service' },
  ollama: { url: 'http://localhost:11434/api/tags', name: 'Ollama LLM' }
}

const COLORS = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

class VocaPilotTester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      tests: []
    }
  }

  log(message, color = 'reset') {
    console.log(`${COLORS[color]}${message}${COLORS.reset}`)
  }

  logTest(name, status, message = '') {
    const icon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️'
    const color = status === 'pass' ? 'green' : status === 'fail' ? 'red' : 'yellow'
    
    this.log(`${icon} ${name}`, color)
    if (message) {
      this.log(`   ${message}`, 'reset')
    }
    
    this.results.tests.push({ name, status, message })
    this.results[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++
  }

  async testService(service, url, name) {
    try {
      const response = await axios.get(url, { timeout: 5000 })
      
      if (response.status === 200) {
        this.logTest(`${name} Connection`, 'pass', `Status: ${response.status}`)
        return true
      } else {
        this.logTest(`${name} Connection`, 'fail', `Unexpected status: ${response.status}`)
        return false
      }
    } catch (error) {
      this.logTest(`${name} Connection`, 'fail', `Error: ${error.message}`)
      return false
    }
  }

  async testFileStructure() {
    this.log('\n📁 Testing File Structure...', 'blue')
    
    const requiredFiles = [
      'package.json',
      'docker-compose.yml',
      'README.md',
      'frontend/package.json',
      'frontend/src/main.jsx',
      'backend/package.json',
      'backend/server.js',
      'whisper-service/app.py',
      'whisper-service/requirements.txt'
    ]

    for (const file of requiredFiles) {
      if (fs.existsSync(file)) {
        this.logTest(`File: ${file}`, 'pass')
      } else {
        this.logTest(`File: ${file}`, 'fail', 'File not found')
      }
    }
  }

  async testEnvironmentFiles() {
    this.log('\n🔧 Testing Environment Configuration...', 'blue')
    
    const envFiles = [
      '.env.example',
      'frontend/.env.example',
      'backend/.env.example',
      'whisper-service/.env.example'
    ]

    for (const file of envFiles) {
      if (fs.existsSync(file)) {
        this.logTest(`Environment: ${file}`, 'pass')
      } else {
        this.logTest(`Environment: ${file}`, 'warn', 'Example file not found')
      }
    }
  }

  async testNodeModules() {
    this.log('\n📦 Testing Dependencies...', 'blue')
    
    const nodeModulesPaths = [
      'node_modules',
      'frontend/node_modules',
      'backend/node_modules'
    ]

    for (const path of nodeModulesPaths) {
      if (fs.existsSync(path)) {
        this.logTest(`Dependencies: ${path}`, 'pass')
      } else {
        this.logTest(`Dependencies: ${path}`, 'fail', 'Run npm install')
      }
    }
  }

  async testServices() {
    this.log('\n🌐 Testing Service Connections...', 'blue')
    
    for (const [key, service] of Object.entries(SERVICES)) {
      await this.testService(key, service.url, service.name)
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }

  async testBackendAPI() {
    this.log('\n🔌 Testing Backend API Endpoints...', 'blue')
    
    const endpoints = [
      { path: '/health', name: 'Health Check' },
      { path: '/api/audio/languages', name: 'Audio Languages' },
      { path: '/api/ai/status', name: 'AI Status' }
    ]

    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(`http://localhost:5000${endpoint.path}`, { timeout: 5000 })
        this.logTest(`API: ${endpoint.name}`, 'pass', `${endpoint.path}`)
      } catch (error) {
        this.logTest(`API: ${endpoint.name}`, 'fail', `${endpoint.path} - ${error.message}`)
      }
    }
  }

  async testWhisperService() {
    this.log('\n🎤 Testing Whisper Service...', 'blue')
    
    try {
      // Test health endpoint
      const healthResponse = await axios.get('http://localhost:8000/health', { timeout: 5000 })
      this.logTest('Whisper Health', 'pass', 'Service is healthy')
      
      // Test languages endpoint
      const langResponse = await axios.get('http://localhost:8000/languages', { timeout: 5000 })
      this.logTest('Whisper Languages', 'pass', `${langResponse.data.supported_languages?.length || 0} languages`)
      
    } catch (error) {
      this.logTest('Whisper Service', 'fail', error.message)
    }
  }

  async testOllamaService() {
    this.log('\n🤖 Testing Ollama LLM Service...', 'blue')
    
    try {
      const response = await axios.get('http://localhost:11434/api/tags', { timeout: 10000 })
      const models = response.data.models || []
      
      if (models.length > 0) {
        this.logTest('Ollama Models', 'pass', `${models.length} models available`)
        
        // Check for recommended models
        const hasLlama = models.some(m => m.name.includes('llama'))
        if (hasLlama) {
          this.logTest('Recommended Model', 'pass', 'Llama model found')
        } else {
          this.logTest('Recommended Model', 'warn', 'Consider installing llama3 model')
        }
      } else {
        this.logTest('Ollama Models', 'warn', 'No models installed. Run: ollama pull llama3')
      }
      
    } catch (error) {
      this.logTest('Ollama Service', 'fail', error.message)
    }
  }

  async testDockerSetup() {
    this.log('\n🐳 Testing Docker Setup...', 'blue')
    
    return new Promise((resolve) => {
      const docker = spawn('docker', ['--version'], { stdio: 'pipe' })
      
      docker.on('close', (code) => {
        if (code === 0) {
          this.logTest('Docker Installation', 'pass', 'Docker is available')
        } else {
          this.logTest('Docker Installation', 'fail', 'Docker not found')
        }
        
        // Test Docker Compose
        const compose = spawn('docker-compose', ['--version'], { stdio: 'pipe' })
        
        compose.on('close', (code) => {
          if (code === 0) {
            this.logTest('Docker Compose', 'pass', 'Docker Compose is available')
          } else {
            this.logTest('Docker Compose', 'fail', 'Docker Compose not found')
          }
          resolve()
        })
        
        compose.on('error', () => {
          this.logTest('Docker Compose', 'fail', 'Docker Compose not found')
          resolve()
        })
      })
      
      docker.on('error', () => {
        this.logTest('Docker Installation', 'fail', 'Docker not found')
        resolve()
      })
    })
  }

  async testBrowserCompatibility() {
    this.log('\n🌐 Testing Browser Compatibility...', 'blue')
    
    // These are checks that would typically be done in the browser
    // For now, we'll just verify the frontend is serving the right content
    try {
      const response = await axios.get('http://localhost:3000', { timeout: 5000 })
      
      if (response.data.includes('VocaPilot')) {
        this.logTest('Frontend Content', 'pass', 'VocaPilot app is loading')
      } else {
        this.logTest('Frontend Content', 'warn', 'Unexpected content')
      }
      
      // Check for required JavaScript features in the served content
      const hasModernJS = response.data.includes('type="module"')
      if (hasModernJS) {
        this.logTest('Modern JavaScript', 'pass', 'ES6 modules detected')
      } else {
        this.logTest('Modern JavaScript', 'warn', 'Check browser compatibility')
      }
      
    } catch (error) {
      this.logTest('Browser Compatibility', 'fail', error.message)
    }
  }

  async runAllTests() {
    this.log(`${COLORS.bold}🎯 VocaPilot Installation Test${COLORS.reset}`, 'blue')
    this.log('Testing all components and services...\n')

    await this.testFileStructure()
    await this.testEnvironmentFiles()
    await this.testNodeModules()
    await this.testDockerSetup()
    await this.testServices()
    await this.testBackendAPI()
    await this.testWhisperService()
    await this.testOllamaService()
    await this.testBrowserCompatibility()

    this.printSummary()
  }

  printSummary() {
    this.log('\n📊 Test Summary', 'bold')
    this.log('═'.repeat(50))
    
    this.log(`✅ Passed: ${this.results.passed}`, 'green')
    this.log(`❌ Failed: ${this.results.failed}`, 'red')
    this.log(`⚠️  Warnings: ${this.results.warnings}`, 'yellow')
    
    const total = this.results.passed + this.results.failed + this.results.warnings
    const successRate = Math.round((this.results.passed / total) * 100)
    
    this.log(`\n📈 Success Rate: ${successRate}%`, successRate >= 80 ? 'green' : 'yellow')
    
    if (this.results.failed === 0) {
      this.log('\n🎉 All critical tests passed! VocaPilot is ready to use.', 'green')
      this.log('Visit http://localhost:3000 to start using VocaPilot.', 'blue')
    } else {
      this.log('\n🔧 Some tests failed. Please check the issues above.', 'red')
      this.log('Refer to the SETUP.md guide for troubleshooting.', 'yellow')
    }
    
    if (this.results.warnings > 0) {
      this.log('\n💡 Some warnings were found. These are not critical but may affect functionality.', 'yellow')
    }
  }
}

// Run the tests
const tester = new VocaPilotTester()
tester.runAllTests().catch(error => {
  console.error('Test runner failed:', error)
  process.exit(1)
})
